import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';

interface CommodityPrice {
  id: string;
  name: string;
  price: number;
  unit: string;
  change: number;
  lastUpdated: string;
}

const MarketPriceWidget: React.FC = () => {
  const { token } = useAuth();
  const { currentFarm } = useFarm();
  const [commodityPrices, setCommodityPrices] = useState<CommodityPrice[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMarketPrices = async () => {
      if (!currentFarm || !token) return;

      try {
        setLoading(true);
        setError(null);

        // Get the farm's location
        const farmResponse = await fetch(`/api/farms/${currentFarm.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!farmResponse.ok) {
          throw new Error('Failed to fetch farm data');
        }

        const farmData = await farmResponse.json();
        const { latitude, longitude } = farmData.location || { latitude: 0, longitude: 0 };

        // Get market prices for the farm's location
        const pricesResponse = await fetch(
          `/api/market-prices?lat=${latitude}&lon=${longitude}&commodities=beef,corn,wheat,soybeans`, 
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );

        if (!pricesResponse.ok) {
          throw new Error('Failed to fetch market prices');
        }

        const pricesData = await pricesResponse.json();

        // Transform the data to match our component's expected format
        const formattedPrices: CommodityPrice[] = pricesData.currentPrices.map((item: any) => ({
          id: item.commodity,
          name: item.commodity.charAt(0).toUpperCase() + item.commodity.slice(1),
          price: item.price,
          unit: item.unit,
          change: item.percentChange / 100 * item.price, // Convert percent change to absolute change
          lastUpdated: pricesData.lastUpdated
        }));

        setCommodityPrices(formattedPrices);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching market prices:', err);
        setError(err.response?.data?.error || err.message || 'Failed to load market prices');
        setLoading(false);
      }
    };

    fetchMarketPrices();
  }, [currentFarm, token]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-500">Loading market prices...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        <p>Error: {error}</p>
      </div>
    );
  }

  if (commodityPrices.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>No market price data available.</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Commodity
              </th>
              <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Change
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {commodityPrices.map((commodity) => (
              <tr key={commodity.id} className="hover:bg-gray-50">
                <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                  {commodity.name}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-900">
                  ${commodity.price.toFixed(2)}/{commodity.unit}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm text-right font-medium ${
                  commodity.change > 0 ? 'text-green-600' : commodity.change < 0 ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {commodity.change > 0 ? '+' : ''}{commodity.change.toFixed(2)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="mt-2 text-xs text-gray-500 text-right">
        Last updated: {new Date(commodityPrices[0].lastUpdated).toLocaleString()}
      </div>
    </div>
  );
};

export default MarketPriceWidget;
