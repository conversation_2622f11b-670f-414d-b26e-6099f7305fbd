import express from 'express';
import { 
  getDriverPerformance, 
  getDriversPerformanceComparison, 
  getDriverPerformanceTrends 
} from '../controllers/driverPerformanceController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get performance metrics for a driver
router.get('/drivers/:driverId/performance', checkFarmAccess, getDriverPerformance);

// Get performance comparison for all drivers
router.get('/farms/:farmId/drivers-performance-comparison', checkFarmAccess, getDriversPerformanceComparison);

// Get performance trends for a driver
router.get('/drivers/:driverId/performance-trends', checkFarmAccess, getDriverPerformanceTrends);

export default router;