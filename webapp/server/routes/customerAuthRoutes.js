import express from 'express';
import {
  login,
  register,
  verifyEmail,
  forgotPassword,
  resetPassword,
  refreshToken,
  logout,
  sendLoginLink,
  loginWithToken
} from '../controllers/customerAuthController.js';

const router = express.Router();

// Public routes
router.post('/login', login);
router.post('/register', register);
router.get('/verify-email/:token', verifyEmail);
router.post('/forgot-password', forgotPassword);
router.post('/reset-password/:token', resetPassword);
router.post('/refresh-token', refreshToken);
router.post('/logout', logout);
router.post('/send-login-link', sendLoginLink);
router.get('/login-with-token/:token', loginWithToken);

export default router;
