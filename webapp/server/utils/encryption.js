import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

// Get encryption key from environment variables or use a default (for development only)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-encryption-key-for-development-only';
const IV_LENGTH = 16; // For AES, this is always 16

/**
 * Encrypt a string using AES-256-CBC
 * @param {string} text - The text to encrypt
 * @returns {string} - The encrypted text as a base64 string
 */
export const encrypt = (text) => {
  if (!text) return null;
  
  try {
    // Create an initialization vector
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Create cipher
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    
    // Encrypt the text
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Return iv + encrypted data as base64
    return Buffer.from(iv.toString('hex') + ':' + encrypted).toString('base64');
  } catch (error) {
    console.error('Encryption error:', error);
    return null;
  }
};

/**
 * Decrypt a string that was encrypted using AES-256-CBC
 * @param {string} encryptedText - The encrypted text as a base64 string
 * @returns {string} - The decrypted text
 */
export const decrypt = (encryptedText) => {
  if (!encryptedText) return null;
  
  try {
    // Convert from base64 to utf8
    const encryptedTextBuf = Buffer.from(encryptedText, 'base64').toString('utf8');
    
    // Split iv and encrypted text
    const textParts = encryptedTextBuf.split(':');
    const iv = Buffer.from(textParts.shift(), 'hex');
    const encryptedData = textParts.join(':');
    
    // Create decipher
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    
    // Decrypt the data
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    return null;
  }
};

/**
 * Encrypt camera credentials in a configuration object
 * @param {Object} config - The camera configuration object
 * @returns {Object} - The configuration object with encrypted credentials
 */
export const encryptCameraCredentials = (config) => {
  if (!config) return null;
  
  // Create a copy of the config object
  const encryptedConfig = { ...config };
  
  // Encrypt the password if it exists and isn't already encrypted
  if (encryptedConfig.password && !encryptedConfig.password.startsWith('enc:')) {
    encryptedConfig.password = 'enc:' + encrypt(encryptedConfig.password);
  }
  
  return encryptedConfig;
};

/**
 * Decrypt camera credentials in a configuration object
 * @param {Object} config - The camera configuration object with encrypted credentials
 * @returns {Object} - The configuration object with decrypted credentials
 */
export const decryptCameraCredentials = (config) => {
  if (!config) return null;
  
  // Create a copy of the config object
  const decryptedConfig = { ...config };
  
  // Decrypt the password if it exists and is encrypted
  if (decryptedConfig.password && decryptedConfig.password.startsWith('enc:')) {
    const encryptedPassword = decryptedConfig.password.substring(4); // Remove 'enc:' prefix
    decryptedConfig.password = decrypt(encryptedPassword);
  }
  
  return decryptedConfig;
};