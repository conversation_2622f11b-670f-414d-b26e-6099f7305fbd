import express from 'express';
import { 
  getAllIssues,
  getIssue,
  getIssueEvents,
  getStats,
  resolveIssue
} from '../controllers/sentryController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication and global admin middleware to all routes
router.use(authenticate);
router.use(isGlobalAdmin);

// Get all Sentry issues
router.get('/', getAllIssues);

// Get a specific Sentry issue by ID
router.get('/:issueId', getIssue);

// Get events for a specific Sentry issue
router.get('/:issueId/events', getIssueEvents);

// Get stats for Sentry issues
router.get('/stats/overview', getStats);

// Resolve a Sentry issue
router.put('/:issueId/resolve', resolveIssue);

export default router;