import express from 'express';
import {
  getFarmChemicalProducts,
  getChemicalProductById,
  createChemicalProduct,
  updateChemicalProduct,
  deleteChemicalProduct
} from '../controllers/chemicalProductController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all chemical routes
router.use(authenticate);

// Get all chemical products for a farm
router.get('/farm/:farmId', getFarmChemicalProducts);

// Get a single chemical product by ID
router.get('/:chemicalProductId', getChemicalProductById);

// Create a new chemical product
router.post('/', createChemicalProduct);

// Update a chemical product
router.put('/:chemicalProductId', updateChemicalProduct);

// Delete a chemical product
router.delete('/:chemicalProductId', deleteChemicalProduct);

export default router;