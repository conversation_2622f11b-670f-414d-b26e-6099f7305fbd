import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// Define allowed file types
const ALLOWED_FILE_TYPES = {
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/gif': 'gif',
  'image/webp': 'webp',
  'application/pdf': 'pdf',
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'text/plain': 'txt',
  'text/csv': 'csv'
};

// Define allowed file types for invoice documents (only PDF and text files)
const ALLOWED_INVOICE_DOCUMENT_TYPES = {
  'application/pdf': 'pdf',
  'text/plain': 'txt'
};

// Maximum file size (10MB in bytes)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Configure storage for support ticket attachments
const supportTicketStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'support-tickets');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const ticketId = req.params.ticketId || 'temp';
    const fileExtension = path.extname(file.originalname) || '';
    const uniqueFilename = `${ticketId}_${uuidv4()}${fileExtension}`;
    cb(null, uniqueFilename);
  }
});

// File filter to validate file types
const fileFilter = (req, file, cb) => {
  // Check if the file type is allowed
  if (ALLOWED_FILE_TYPES[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, documents, and PDFs are allowed.'), false);
  }
};

// Configure storage for invoice documents
const invoiceDocumentStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'invoice-documents');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const invoiceId = req.params.invoiceId || 'temp';
    const fileExtension = path.extname(file.originalname) || '';
    const uniqueFilename = `invoice_${invoiceId}_${uuidv4()}${fileExtension}`;
    cb(null, uniqueFilename);
  }
});

// File filter for invoice documents (only PDF and text files)
const invoiceDocumentFileFilter = (req, file, cb) => {
  // Check if the file type is allowed for invoice documents
  if (ALLOWED_INVOICE_DOCUMENT_TYPES[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only PDF and text files are allowed for invoice documents.'), false);
  }
};

// Create multer upload instance with size limits and file type validation
export const uploadSupportTicketFile = multer({
  storage: supportTicketStorage,
  limits: {
    fileSize: MAX_FILE_SIZE // 10MB
  },
  fileFilter: fileFilter
});

// Create multer upload instance for invoice documents
export const uploadInvoiceDocument = multer({
  storage: invoiceDocumentStorage,
  limits: {
    fileSize: MAX_FILE_SIZE // 10MB
  },
  fileFilter: invoiceDocumentFileFilter
});

// Error handling middleware for multer errors
export const handleFileUploadErrors = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File size exceeds the limit of 10MB.'
      });
    }
    return res.status(400).json({
      error: `File upload error: ${err.message}`
    });
  } else if (err) {
    return res.status(400).json({
      error: err.message
    });
  }
  next();
};
