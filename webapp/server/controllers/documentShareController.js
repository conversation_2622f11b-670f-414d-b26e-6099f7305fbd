import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import Document from '../models/Document.js';
import DocumentShare from '../models/DocumentShare.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import DocumentPermission from '../models/DocumentPermission.js';
import Farm from '../models/Farm.js';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

// Helper function to check if a user has access to a farm
const checkUserFarmAccess = async (userId, farmId) => {
  if (!userId || !farmId) return false;

  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  return !!userFarm;
};

// Helper function to check if a user has permission to share a document
const checkSharePermission = async (userId, documentId, farmId) => {
  // Global admins can share any document
  const user = await User.findByPk(userId);
  if (user?.is_global_admin) return true;

  // Check if the user has explicit share permission for this document
  const documentPermission = await DocumentPermission.findOne({
    where: {
      entity_type: 'document',
      entity_id: documentId,
      user_id: userId,
      can_share: true
    }
  });

  if (documentPermission) return true;

  // Check if the user has role-based share permission for this document
  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  if (!userFarm) return false;

  const rolePermission = await DocumentPermission.findOne({
    where: {
      entity_type: 'document',
      entity_id: documentId,
      role_id: userFarm.role_id,
      can_share: true
    }
  });

  return !!rolePermission;
};

// Generate a secure random token
const generateShareToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Create a share link for a document
 */
export const createShareLink = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { documentId } = req.params;
    const { expiresAt, password, oneTimeUse, recipientFarmId } = req.body;

    // Get document
    const document = await Document.findByPk(documentId);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if user has permission to share this document
    const hasPermission = await checkSharePermission(req.user.id, documentId, document.farm_id);
    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have permission to share this document' });
    }

    // If recipient farm ID is provided, validate it
    let recipientFarm = null;
    if (recipientFarmId) {
      // Check that we're not sharing with the same farm
      if (recipientFarmId === document.farm_id) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Cannot share a document with the same farm that owns it' });
      }

      // Check if recipient farm exists
      recipientFarm = await Farm.findByPk(recipientFarmId);
      if (!recipientFarm) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Recipient farm not found' });
      }
    }

    // Generate a unique share token
    const shareToken = generateShareToken();

    // Hash password if provided
    let passwordHash = null;
    if (password) {
      passwordHash = await bcrypt.hash(password, 10);
    }

    // Create share record
    const share = await DocumentShare.create({
      document_id: documentId,
      share_token: shareToken,
      expires_at: expiresAt ? new Date(expiresAt) : null,
      password_hash: passwordHash,
      one_time_use: oneTimeUse === true,
      used: false,
      created_by: req.user.id,
      farm_id: document.farm_id,
      recipient_farm_id: recipientFarmId || null
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Share link created successfully',
      share: {
        id: share.id,
        shareToken: share.share_token,
        expiresAt: share.expires_at,
        isPasswordProtected: !!share.password_hash,
        isOneTimeUse: share.one_time_use,
        used: share.used,
        shareUrl: `${req.protocol}://${req.get('host')}/api/documents/share/${share.share_token}`,
        recipientFarmId: share.recipient_farm_id,
        recipientFarmName: recipientFarm ? recipientFarm.name : null,
        isFarmToFarmShare: !!share.recipient_farm_id
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating share link:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get all share links for a document
 */
export const getDocumentShares = async (req, res) => {
  try {
    const { documentId } = req.params;

    // Get document
    const document = await Document.findByPk(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Get shares
    const shares = await DocumentShare.findAll({
      where: { document_id: documentId },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Format response
    const formattedShares = shares.map(share => ({
      id: share.id,
      shareToken: share.share_token,
      expiresAt: share.expires_at,
      isPasswordProtected: !!share.password_hash,
      isOneTimeUse: share.one_time_use,
      used: share.used,
      shareUrl: `${req.protocol}://${req.get('host')}/api/documents/share/${share.share_token}`,
      createdBy: share.creator,
      createdAt: share.created_at,
      recipientFarm: share.recipientFarm,
      isFarmToFarmShare: !!share.recipient_farm_id
    }));

    return res.status(200).json(formattedShares);
  } catch (error) {
    console.error('Error getting document shares:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a share link
 */
export const deleteShareLink = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { shareId } = req.params;

    // Get share
    const share = await DocumentShare.findByPk(shareId, {
      include: [
        {
          model: Document,
          as: 'document'
        }
      ]
    });

    if (!share) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Share link not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, share.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this share link' });
    }

    // Delete share
    await share.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Share link deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting share link:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Access a shared document
 */
export const accessSharedDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { token } = req.params;
    const { password } = req.body;

    // Get share
    const share = await DocumentShare.findOne({
      where: { share_token: token },
      include: [
        {
          model: Document,
          as: 'document'
        },
        {
          model: Farm,
          as: 'recipientFarm'
        }
      ],
      transaction
    });

    if (!share) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Share link not found or has expired' });
    }

    // Check if share has expired
    if (share.expires_at && new Date(share.expires_at) < new Date()) {
      await transaction.rollback();
      return res.status(403).json({ error: 'Share link has expired' });
    }

    // Check if one-time link has been used
    if (share.one_time_use && share.used) {
      await transaction.rollback();
      return res.status(403).json({ error: 'This share link has already been used' });
    }

    // Check if this is a farm-to-farm share and if the user belongs to the recipient farm
    if (share.recipient_farm_id) {
      // If user is not authenticated, they can't access a farm-to-farm share
      if (!req.user) {
        await transaction.rollback();
        return res.status(401).json({ error: 'Authentication required to access this document' });
      }

      // Check if user belongs to the recipient farm
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: share.recipient_farm_id,
          is_approved: true
        }
      });

      if (!userFarm && !req.user.is_global_admin) {
        await transaction.rollback();
        return res.status(403).json({ error: 'You do not have access to this document. It is shared with a specific farm.' });
      }
    }

    // Check password if required
    if (share.password_hash) {
      if (!password) {
        await transaction.rollback();
        return res.status(401).json({ 
          error: 'Password required',
          requiresPassword: true
        });
      }

      const passwordMatch = await bcrypt.compare(password, share.password_hash);
      if (!passwordMatch) {
        await transaction.rollback();
        return res.status(401).json({ error: 'Invalid password' });
      }
    }

    // Mark one-time link as used
    if (share.one_time_use && !share.used) {
      share.used = true;
      await share.save({ transaction });
    }

    await transaction.commit();

    // Return document info
    return res.status(200).json({
      document: {
        id: share.document.id,
        name: share.document.name,
        description: share.document.description,
        fileType: share.document.file_type,
        fileSize: share.document.file_size,
        createdAt: share.document.created_at,
        downloadUrl: `${req.protocol}://${req.get('host')}/api/documents/share/${token}/download${password ? `?password=${encodeURIComponent(password)}` : ''}`
      }
    });
  } catch (error) {
    console.error('Error accessing shared document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Download a shared document
 */
export const downloadSharedDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { token } = req.params;
    const { password } = req.query;

    // Get share
    const share = await DocumentShare.findOne({
      where: { share_token: token },
      include: [
        {
          model: Document,
          as: 'document'
        },
        {
          model: Farm,
          as: 'recipientFarm'
        }
      ],
      transaction
    });

    if (!share) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Share link not found or has expired' });
    }

    // Check if share has expired
    if (share.expires_at && new Date(share.expires_at) < new Date()) {
      await transaction.rollback();
      return res.status(403).json({ error: 'Share link has expired' });
    }

    // Check if one-time link has been used
    if (share.one_time_use && share.used) {
      await transaction.rollback();
      return res.status(403).json({ error: 'This share link has already been used' });
    }

    // Check if this is a farm-to-farm share and if the user belongs to the recipient farm
    if (share.recipient_farm_id) {
      // If user is not authenticated, they can't access a farm-to-farm share
      if (!req.user) {
        await transaction.rollback();
        return res.status(401).json({ error: 'Authentication required to access this document' });
      }

      // Check if user belongs to the recipient farm
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: share.recipient_farm_id,
          is_approved: true
        }
      });

      if (!userFarm && !req.user.is_global_admin) {
        await transaction.rollback();
        return res.status(403).json({ error: 'You do not have access to this document. It is shared with a specific farm.' });
      }
    }

    // Check password if required
    if (share.password_hash) {
      if (!password) {
        await transaction.rollback();
        return res.status(401).json({ 
          error: 'Password required',
          requiresPassword: true
        });
      }

      const passwordMatch = await bcrypt.compare(password, share.password_hash);
      if (!passwordMatch) {
        await transaction.rollback();
        return res.status(401).json({ error: 'Invalid password' });
      }
    }

    // Mark one-time link as used
    if (share.one_time_use && !share.used) {
      share.used = true;
      await share.save({ transaction });
    }

    await transaction.commit();

    // Get document
    const document = share.document;

    // Stream file to response
    // Note: This is a simplified version. In a real implementation, you would use the same
    // file handling logic as in the documentController.downloadDocument method.
    res.setHeader('Content-Type', document.mime_type);
    res.setHeader('Content-Disposition', `attachment; filename="${document.name}"`);

    // For simplicity, we'll just return a success message
    // In a real implementation, you would stream the file content
    return res.status(200).send('File content would be streamed here');
  } catch (error) {
    console.error('Error downloading shared document:', error);
    return res.status(500).json({ error: error.message });
  }
};
