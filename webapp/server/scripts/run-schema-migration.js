import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { sequelize } from '../config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Function to parse dependencies from SQL file
function parseDependencies(sqlContent) {
  const dependencyRegex = /--\s*Depends on:\s*([^\n]+)/g;
  const dependencies = [];
  let match;

  while ((match = dependencyRegex.exec(sqlContent)) !== null) {
    // Split the dependency line by commas and trim each dependency
    const deps = match[1].split(',').map(dep => dep.trim());
    dependencies.push(...deps);
  }

  return dependencies;
}

// Function to build a dependency graph
function buildDependencyGraph(migrations) {
  const graph = {};
  
  // Initialize graph with all migrations
  migrations.forEach(migration => {
    graph[migration.filename] = [];
  });
  
  // Add dependencies
  migrations.forEach(migration => {
    migration.dependencies.forEach(dependency => {
      if (graph[dependency]) {
        // Add reverse dependency (dependency -> migration)
        graph[dependency].push(migration.filename);
      } else {
        console.warn(`Warning: Migration ${migration.filename} depends on ${dependency}, but ${dependency} was not found.`);
      }
    });
  });
  
  return graph;
}

// Function to perform topological sort
function topologicalSort(graph) {
  const visited = new Set();
  const temp = new Set();
  const order = [];
  
  function visit(node) {
    if (temp.has(node)) {
      throw new Error(`Circular dependency detected: ${node}`);
    }
    
    if (!visited.has(node)) {
      temp.add(node);
      
      // Visit all dependencies
      (graph[node] || []).forEach(dependency => {
        visit(dependency);
      });
      
      temp.delete(node);
      visited.add(node);
      order.unshift(node); // Add to the beginning of the array
    }
  }
  
  // Visit all nodes
  Object.keys(graph).forEach(node => {
    if (!visited.has(node)) {
      visit(node);
    }
  });
  
  return order;
}

// Function to check if a migration has been applied
async function isMigrationApplied(filename) {
  try {
    const result = await sequelize.query(
      'SELECT * FROM schema_migrations WHERE filename = :filename',
      {
        replacements: { filename },
        type: sequelize.QueryTypes.SELECT
      }
    );
    return result.length > 0;
  } catch (error) {
    // If the schema_migrations table doesn't exist, no migrations have been applied
    if (error.message.includes('relation "schema_migrations" does not exist')) {
      return false;
    }
    throw error;
  }
}

// Function to record a migration as applied
async function recordMigration(filename, description, status = 'success', errorMessage = null) {
  try {
    await sequelize.query(
      `INSERT INTO schema_migrations 
       (filename, description, status, error_message, applied_at, created_at, updated_at) 
       VALUES (:filename, :description, :status, :errorMessage, NOW(), NOW(), NOW())
       ON CONFLICT (filename) 
       DO UPDATE SET 
         status = :status, 
         error_message = :errorMessage, 
         applied_at = NOW(), 
         updated_at = NOW()`,
      {
        replacements: { 
          filename, 
          description: description || '', 
          status, 
          errorMessage 
        },
        type: sequelize.QueryTypes.INSERT
      }
    );
  } catch (error) {
    console.error(`Error recording migration ${filename}:`, error.message);
    throw error;
  }
}

// Function to record migration dependencies
async function recordDependencies(filename, dependencies) {
  try {
    for (const dependency of dependencies) {
      await sequelize.query(
        `INSERT INTO schema_migration_dependencies 
         (migration_filename, depends_on_filename, created_at, updated_at) 
         VALUES (:migration_filename, :depends_on_filename, NOW(), NOW())
         ON CONFLICT (migration_filename, depends_on_filename) DO NOTHING`,
        {
          replacements: { 
            migration_filename: filename, 
            depends_on_filename: dependency 
          },
          type: sequelize.QueryTypes.INSERT
        }
      );
    }
  } catch (error) {
    console.error(`Error recording dependencies for ${filename}:`, error.message);
    throw error;
  }
}

// Function to execute a migration
async function executeMigration(migration) {
  console.log(`Executing migration: ${migration.filename}`);
  
  try {
    // Split the SQL into individual statements
    const statements = migration.content
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');
    
    console.log(`Found ${statements.length} SQL statements to execute in ${migration.filename}.`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        await sequelize.query(statement);
        console.log(`Executed statement ${i + 1}/${statements.length} from ${migration.filename}`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}/${statements.length} from ${migration.filename}:`, error.message);
        console.error('Statement:', statement);
        
        // Record the migration as failed
        await recordMigration(migration.filename, migration.description, 'failed', error.message);
        
        throw error;
      }
    }
    
    // Record the migration as successful
    await recordMigration(migration.filename, migration.description);
    
    // Record dependencies
    await recordDependencies(migration.filename, migration.dependencies);
    
    console.log(`Migration ${migration.filename} completed successfully!`);
    return true;
  } catch (error) {
    console.error(`Error executing migration ${migration.filename}:`, error.message);
    return false;
  }
}

// Function to ensure the schema_migrations table exists
async function ensureMigrationsTableExists() {
  try {
    // Check if the schema_migrations table exists
    const result = await sequelize.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'schema_migrations')",
      { type: sequelize.QueryTypes.SELECT }
    );
    
    if (!result[0].exists) {
      console.log('schema_migrations table does not exist, creating it...');
      
      // Read and execute the add_schema_migrations_table.sql file
      const migrationsTablePath = path.join(__dirname, '../db/migrations/add_schema_migrations_table.sql');
      const migrationsTableSQL = fs.readFileSync(migrationsTablePath, 'utf8');
      
      // Split the SQL into individual statements
      const statements = migrationsTableSQL
        .split(';')
        .filter(statement => statement.trim() !== '')
        .map(statement => statement.trim() + ';');
      
      // Execute each statement
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        try {
          await sequelize.query(statement);
          console.log(`Executed statement ${i + 1}/${statements.length} from add_schema_migrations_table.sql`);
        } catch (error) {
          console.error(`Error executing statement ${i + 1}/${statements.length} from add_schema_migrations_table.sql:`, error.message);
          console.error('Statement:', statement);
          throw error;
        }
      }
      
      console.log('schema_migrations table created successfully!');
    } else {
      console.log('schema_migrations table already exists.');
    }
  } catch (error) {
    console.error('Error ensuring migrations table exists:', error.message);
    throw error;
  }
}

// Main function to run migrations
async function runMigrations() {
  try {
    console.log('Starting schema migration process...');
    
    // Ensure the schema_migrations table exists
    await ensureMigrationsTableExists();
    
    // Get all migration files
    const migrationsDir = path.join(__dirname, '../db/migrations');
    const files = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort files alphabetically
    
    console.log(`Found ${files.length} migration files.`);
    
    // Parse migrations and their dependencies
    const migrations = [];
    for (const file of files) {
      const filePath = path.join(migrationsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Extract description from the first line comment
      const descriptionMatch = content.match(/--\s*Migration:\s*([^\n]+)/);
      const description = descriptionMatch ? descriptionMatch[1].trim() : '';
      
      // Parse dependencies
      const dependencies = parseDependencies(content);
      
      migrations.push({
        filename: file,
        description,
        dependencies,
        content,
        path: filePath
      });
    }
    
    console.log('Parsed migrations and their dependencies:');
    migrations.forEach(migration => {
      console.log(`- ${migration.filename} depends on: ${migration.dependencies.join(', ') || 'none'}`);
    });
    
    // Build dependency graph
    const graph = buildDependencyGraph(migrations);
    
    // Perform topological sort to determine execution order
    const executionOrder = topologicalSort(graph);
    
    console.log('Determined execution order:');
    executionOrder.forEach((filename, index) => {
      console.log(`${index + 1}. ${filename}`);
    });
    
    // Execute migrations in order
    let successCount = 0;
    let skipCount = 0;
    let failCount = 0;
    
    for (const filename of executionOrder) {
      const migration = migrations.find(m => m.filename === filename);
      
      // Skip if migration has already been applied
      const applied = await isMigrationApplied(filename);
      if (applied) {
        console.log(`Migration ${filename} has already been applied, skipping.`);
        skipCount++;
        continue;
      }
      
      // Execute the migration
      const success = await executeMigration(migration);
      if (success) {
        successCount++;
      } else {
        failCount++;
        break; // Stop on first failure
      }
    }
    
    console.log('Migration process completed.');
    console.log(`Results: ${successCount} succeeded, ${skipCount} skipped, ${failCount} failed.`);
    
  } catch (error) {
    console.error('Error running migrations:', error.message);
  } finally {
    // Close the database connection
    await sequelize.close();
    process.exit(failCount > 0 ? 1 : 0);
  }
}

// Run the migrations
runMigrations();