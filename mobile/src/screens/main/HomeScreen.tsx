import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useAuth } from '@/store/AuthProvider';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { getWeatherForCurrentLocation, getWeatherIconName, WeatherData } from '@/services/weatherService';

type HomeScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Home'>,
  NativeStackScreenProps<MainStackParamList>
>;

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { user } = useAuth();

  // Mock data for tasks
  const tasks = [
    { id: '1', title: 'Spray north field', dueDate: '2023-09-15', status: 'pending' },
    { id: '2', title: 'Repair tractor', dueDate: '2023-09-16', status: 'in-progress' },
    { id: '3', title: 'Order seeds', dueDate: '2023-09-18', status: 'pending' },
  ];

  // State for weather data
  const [weather, setWeather] = useState<WeatherData>({
    current: { temp: 0, condition: '', icon: '' },
    forecast: [],
  });
  const [isLoadingWeather, setIsLoadingWeather] = useState<boolean>(true);
  const [weatherError, setWeatherError] = useState<string | null>(null);

  // Fetch weather data when component mounts
  useEffect(() => {
    const fetchWeatherData = async () => {
      try {
        setIsLoadingWeather(true);
        const data = await getWeatherForCurrentLocation();
        setWeather(data);
        setWeatherError(null);
      } catch (error) {
        console.error('Error fetching weather:', error);
        setWeatherError('Failed to load weather data');
      } finally {
        setIsLoadingWeather(false);
      }
    };

    fetchWeatherData();
  }, []);

  // Mock data for fields
  const fields = [
    { id: '1', name: 'North Field', acres: 120, crop: 'Corn' },
    { id: '2', name: 'South Field', acres: 85, crop: 'Soybeans' },
    { id: '3', name: 'West Field', acres: 65, crop: 'Wheat' },
  ];

  // Mock data for equipment
  const equipment = [
    { id: '1', name: 'John Deere Tractor', type: 'Tractor', status: 'ACTIVE' },
    { id: '2', name: 'Case IH Combine', type: 'Combine', status: 'MAINTENANCE' },
    { id: '3', name: 'Sprayer', type: 'Sprayer', status: 'ACTIVE' },
  ];

  // Mock data for inventory
  const inventory = [
    { id: '1', name: 'Corn Seed', category: 'Seeds', quantity: 500, unit: 'bags', reorderPoint: 50 },
    { id: '2', name: 'Nitrogen Fertilizer', category: 'Fertilizers', quantity: 2000, unit: 'gallons', reorderPoint: 200 },
    { id: '3', name: 'Herbicide', category: 'Chemicals', quantity: 150, unit: 'gallons', reorderPoint: 20 },
  ];

  // Mock data for employees
  const employees = [
    { id: '1', name: 'John Smith', position: 'Farm Manager', status: 'ACTIVE' },
    { id: '2', name: 'Sarah Johnson', position: 'Equipment Operator', status: 'ACTIVE' },
    { id: '3', name: 'Emily Davis', position: 'Administrative Assistant', status: 'ON_LEAVE' },
  ];

  // Mock data for finances
  const finances = [
    { 
      id: '1', 
      type: 'income', 
      description: 'Corn sale', 
      amount: 5000, 
      date: '2023-09-15',
      category: 'Sales',
    },
    { 
      id: '2', 
      type: 'expense', 
      description: 'Fertilizer purchase', 
      amount: 1200, 
      date: '2023-09-12',
      category: 'Supplies',
    },
    { 
      id: '3', 
      type: 'income', 
      description: 'Equipment rental', 
      amount: 3500, 
      date: '2023-09-10',
      category: 'Services',
    },
  ];

  const renderTaskItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.taskItem}
      onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
    >
      <View style={styles.taskHeader}>
        <Text style={styles.taskTitle}>{item.title}</Text>
        <View style={[
          styles.statusBadge, 
          item.status === 'pending' ? styles.pendingBadge : styles.inProgressBadge
        ]}>
          <Text style={styles.statusText}>
            {item.status === 'pending' ? 'Pending' : 'In Progress'}
          </Text>
        </View>
      </View>
      <Text style={styles.taskDate}>Due: {item.dueDate}</Text>
    </TouchableOpacity>
  );

  const renderFieldItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.fieldItem}
      onPress={() => navigation.navigate('FieldDetail', { fieldId: item.id })}
    >
      <View style={styles.fieldIcon}>
        <Ionicons name="leaf-outline" size={24} color="#22c55e" />
      </View>
      <View style={styles.fieldInfo}>
        <Text style={styles.fieldName}>{item.name}</Text>
        <Text style={styles.fieldDetails}>{item.acres} acres • {item.crop}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  const getEquipmentIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'tractor':
        return 'car-outline';
      case 'combine':
        return 'construct-outline';
      case 'sprayer':
        return 'water-outline';
      case 'truck':
        return 'truck-outline';
      default:
        return 'cog-outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return '#22c55e';
      case 'INACTIVE':
        return '#9ca3af';
      case 'MAINTENANCE':
        return '#f59e0b';
      case 'RETIRED':
        return '#6b7280';
      case 'ON_LEAVE':
        return '#f59e0b';
      default:
        return '#9ca3af';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'seeds':
        return 'leaf-outline';
      case 'fertilizers':
        return 'flask-outline';
      case 'chemicals':
        return 'warning-outline';
      case 'fuel':
        return 'flame-outline';
      default:
        return 'cube-outline';
    }
  };

  const renderEquipmentItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.equipmentItem}
      onPress={() => navigation.navigate('EquipmentDetail', { equipmentId: item.id })}
    >
      <View style={styles.equipmentIcon}>
        <Ionicons name={getEquipmentIcon(item.type)} size={24} color="#22c55e" />
      </View>
      <View style={styles.equipmentInfo}>
        <Text style={styles.equipmentName}>{item.name}</Text>
        <Text style={styles.equipmentType}>{item.type}</Text>
        <View style={styles.equipmentStatus}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: getStatusColor(item.status) }
          ]} />
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  const renderInventoryItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.inventoryItem}
      onPress={() => navigation.navigate('InventoryDetail', { inventoryId: item.id })}
    >
      <View style={styles.inventoryIcon}>
        <Ionicons name={getCategoryIcon(item.category)} size={24} color="#22c55e" />
      </View>
      <View style={styles.inventoryInfo}>
        <Text style={styles.inventoryName}>{item.name}</Text>
        <Text style={styles.inventoryCategory}>{item.category}</Text>
        <Text style={styles.inventoryQuantity}>
          {item.quantity} {item.unit}
          {item.reorderPoint && item.quantity <= item.reorderPoint && (
            <Text style={styles.lowStockText}> • Low Stock</Text>
          )}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );


  const renderEmployeeItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.employeeItem}
      onPress={() => navigation.navigate('EmployeeDetail', { employeeId: item.id })}
    >
      <View style={styles.employeeIcon}>
        <Ionicons name="person-outline" size={24} color="#22c55e" />
      </View>
      <View style={styles.employeeInfo}>
        <Text style={styles.employeeName}>{item.name}</Text>
        <Text style={styles.employeePosition}>{item.position}</Text>
        <View style={styles.employeeStatus}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: getStatusColor(item.status) }
          ]} />
          <Text style={styles.statusText}>{item.status.replace('_', ' ')}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  const renderFinancialItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.financialItem}
      onPress={() => {
        if (item.type === 'income') {
          navigation.navigate('InvoiceDetail', { invoiceId: item.id });
        } else {
          navigation.navigate('ExpenseDetail', { expenseId: item.id });
        }
      }}
    >
      <View style={[
        styles.financialIcon,
        { backgroundColor: item.type === 'income' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)' }
      ]}>
        <Ionicons 
          name={item.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'} 
          size={24} 
          color={item.type === 'income' ? '#22c55e' : '#ef4444'} 
        />
      </View>
      <View style={styles.financialInfo}>
        <Text style={styles.financialDescription}>{item.description}</Text>
        <Text style={styles.financialCategory}>{item.category}</Text>
        <Text style={styles.financialDate}>{item.date}</Text>
      </View>
      <Text style={[
        styles.financialAmount,
        { color: item.type === 'income' ? '#22c55e' : '#ef4444' }
      ]}>
        {item.type === 'income' ? '+' : '-'}${item.amount.toLocaleString()}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>Welcome back,</Text>
        <Text style={styles.userName}>{user?.name}</Text>
        <Text style={styles.farmName}>{user?.farms.find(f => f.id === user.currentFarmId)?.name}</Text>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('Recording')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="navigate" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>Record GPS</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('Tasks')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="add-circle" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>New Task</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('Fields')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="map" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>View Fields</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('LoRaWANMessaging')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="radio" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>LoRaWAN</Text>
        </TouchableOpacity>
      </View>

      {/* Weather Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Weather</Text>
        </View>
        <View style={styles.weatherContainer}>
          {isLoadingWeather ? (
            <View style={styles.weatherLoading}>
              <ActivityIndicator size="large" color="#22c55e" />
              <Text style={styles.weatherLoadingText}>Loading weather data...</Text>
            </View>
          ) : weatherError ? (
            <View style={styles.weatherError}>
              <Ionicons name="cloud-offline" size={40} color="#ef4444" />
              <Text style={styles.weatherErrorText}>{weatherError}</Text>
              <TouchableOpacity 
                style={styles.retryButton}
                onPress={async () => {
                  try {
                    setIsLoadingWeather(true);
                    const data = await getWeatherForCurrentLocation();
                    setWeather(data);
                    setWeatherError(null);
                  } catch (error) {
                    console.error('Error fetching weather:', error);
                    setWeatherError('Failed to load weather data');
                  } finally {
                    setIsLoadingWeather(false);
                  }
                }}
              >
                <Text style={styles.retryButtonText}>Retry</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              <View style={styles.currentWeather}>
                <Ionicons 
                  name={getWeatherIconName(weather.current.condition, weather.current.icon)} 
                  size={40} 
                  color="#f59e0b" 
                />
                <Text style={styles.currentTemp}>{weather.current.temp}°F</Text>
                <Text style={styles.weatherCondition}>{weather.current.condition}</Text>
              </View>
              <View style={styles.forecastContainer}>
                {weather.forecast.map((day, index) => (
                  <View key={index} style={styles.forecastDay}>
                    <Text style={styles.forecastDayName}>{day.day}</Text>
                    <Ionicons 
                      name={getWeatherIconName(day.condition, day.icon)} 
                      size={20} 
                      color={day.condition === 'Clear' || day.condition === 'Sunny' ? '#f59e0b' : '#64748b'} 
                    />
                    <Text style={styles.forecastTemp}>{day.high}° / {day.low}°</Text>
                  </View>
                ))}
              </View>
            </>
          )}
        </View>
      </View>

      {/* Tasks Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Upcoming Tasks</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Tasks')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={tasks}
          renderItem={renderTaskItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          style={styles.taskList}
        />
      </View>

      {/* Fields Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Fields</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Fields')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={fields}
          renderItem={renderFieldItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          style={styles.fieldList}
        />
      </View>

      {/* Equipment Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Equipment</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Equipment')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={equipment}
          renderItem={renderEquipmentItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          style={styles.equipmentList}
        />
      </View>

      {/* Inventory Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Inventory</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Inventory')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={inventory}
          renderItem={renderInventoryItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          style={styles.inventoryList}
        />
      </View>

      {/* Employees Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Team</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Employees')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={employees}
          renderItem={renderEmployeeItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          style={styles.employeeList}
        />
      </View>

      {/* Finances Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Finances')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={finances}
          renderItem={renderFinancialItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          style={styles.financialList}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  weatherLoading: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    height: 150,
  },
  weatherLoadingText: {
    marginTop: 10,
    color: '#64748b',
    fontSize: 16,
  },
  weatherError: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    height: 150,
  },
  weatherErrorText: {
    marginTop: 10,
    color: '#ef4444',
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#22c55e',
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  welcomeSection: {
    padding: 20,
    backgroundColor: '#22c55e',
  },
  welcomeText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 4,
  },
  farmName: {
    fontSize: 16,
    color: '#fff',
    marginTop: 2,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    marginTop: -20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
  },
  actionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#333',
  },
  sectionContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    marginTop: 20,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    color: '#22c55e',
  },
  weatherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentWeather: {
    flex: 1,
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    paddingRight: 15,
  },
  currentTemp: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 5,
  },
  weatherCondition: {
    fontSize: 14,
    color: '#666',
  },
  forecastContainer: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingLeft: 15,
  },
  forecastDay: {
    alignItems: 'center',
  },
  forecastDayName: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  forecastTemp: {
    fontSize: 12,
    marginTop: 5,
  },
  taskList: {
    marginTop: 5,
  },
  taskItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  pendingBadge: {
    backgroundColor: '#fef3c7',
  },
  inProgressBadge: {
    backgroundColor: '#dbeafe',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  taskDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  fieldList: {
    marginTop: 5,
  },
  fieldItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  fieldIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fieldInfo: {
    flex: 1,
  },
  fieldName: {
    fontSize: 16,
    fontWeight: '500',
  },
  fieldDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  equipmentList: {
    marginTop: 5,
  },
  equipmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  equipmentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  equipmentInfo: {
    flex: 1,
  },
  equipmentName: {
    fontSize: 16,
    fontWeight: '500',
  },
  equipmentType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  equipmentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  inventoryList: {
    marginTop: 5,
  },
  inventoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  inventoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  inventoryInfo: {
    flex: 1,
  },
  inventoryName: {
    fontSize: 16,
    fontWeight: '500',
  },
  inventoryCategory: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  inventoryQuantity: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  lowStockText: {
    color: '#ef4444',
    fontWeight: '500',
  },
  employeeList: {
    marginTop: 5,
  },
  employeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  employeeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '500',
  },
  employeePosition: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  employeeStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  financialList: {
    marginTop: 5,
  },
  financialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  financialIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  financialInfo: {
    flex: 1,
  },
  financialDescription: {
    fontSize: 16,
    fontWeight: '500',
  },
  financialCategory: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  financialDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  financialAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
