import dotenv from 'dotenv';
import OAuthClient from 'intuit-oauth';

dotenv.config();

// Initialize the QuickBooks OAuth client
const quickbooksClient = new OAuthClient({
  clientId: process.env.QUICKBOOKS_CLIENT_ID,
  clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET,
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
  redirectUri: process.env.QUICKBOOKS_REDIRECT_URI,
});

export default quickbooksClient;