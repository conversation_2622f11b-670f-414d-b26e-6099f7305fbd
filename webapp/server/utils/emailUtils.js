import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import { generateInvoicePdfBuffer } from './pdfUtils.js';
import { generateInvoiceAuthCode } from './authUtils.js';
import Farm from '../models/Farm.js';

dotenv.config();

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_PORT === '465',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

/**
 * Send an email notification for a support ticket comment
 * 
 * @param {Object} ticket - The support ticket object
 * @param {Object} comment - The comment object
 * @param {Object} commenter - The user who made the comment
 * @param {Object} recipient - The user who should receive the email
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendTicketCommentEmail = async (ticket, comment, commenter, recipient, frontendUrl) => {
  try {
    // Generate a unique email thread ID if one doesn't exist
    if (!ticket.email_thread_id) {
      ticket.email_thread_id = `ticket-${ticket.id}-${uuidv4()}`;
      await ticket.save();
    }

    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'support', 'ticket-response.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the comment date
    const commentDate = new Date(comment.created_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{firstName}}/g, recipient.first_name)
      .replace(/{{ticketId}}/g, ticket.id)
      .replace(/{{subject}}/g, ticket.subject)
      .replace(/{{status}}/g, ticket.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()))
      .replace(/{{commenterName}}/g, `${commenter.first_name} ${commenter.last_name}`)
      .replace(/{{commentContent}}/g, comment.content)
      .replace(/{{commentDate}}/g, commentDate)
      .replace(/{{ticketUrl}}/g, `${frontendUrl}/support/tickets/${ticket.id}`)
      .replace(/{{email}}/g, recipient.email)
      .replace(/{{emailThreadId}}/g, ticket.email_thread_id)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Support" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: recipient.email,
      subject: `Re: [Ticket #${ticket.id}] ${ticket.subject}`,
      html: emailTemplate,
      headers: {
        'References': ticket.email_thread_id,
        'Message-ID': `<${uuidv4()}@nxtacre.com>`,
        'In-Reply-To': ticket.email_thread_id,
        'X-Ticket-ID': ticket.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);

    // Update the last email sent timestamp
    ticket.last_email_sent_at = new Date();
    await ticket.save();

    return result;
  } catch (error) {
    console.error('Error sending ticket comment email:', error);
    throw error;
  }
};

/**
 * Get the frontend URL for a user
 * 
 * @param {string} userId - The user ID
 * @param {string} [farmId] - The farm ID (optional)
 * @returns {Promise<string>} - The frontend URL
 */
export const getFrontendUrl = async (userId, farmId = null) => {
  // Default frontend URL
  const defaultFrontendUrl = process.env.FRONTEND_URL || 'https://app.nxtacre.com';

  // If farmId is provided, use the farm's specific subdomain
  if (farmId) {
    try {
      // Get the farm by ID
      const farm = await Farm.findByPk(farmId);

      if (farm && farm.subdomain) {
        // Get the base domain from the default URL
        const url = new URL(defaultFrontendUrl);
        const baseDomain = url.hostname.split('.').slice(-2).join('.');

        // Create the farm-specific subdomain URL using the farm's subdomain
        return `${url.protocol}//${farm.subdomain}.${baseDomain}`;
      } else {
        // Fallback to the generic farms subdomain if the farm doesn't have a subdomain
        const url = new URL(defaultFrontendUrl);
        const baseDomain = url.hostname.split('.').slice(-2).join('.');

        return `${url.protocol}//farms.${baseDomain}`;
      }
    } catch (error) {
      console.error('Error getting farm for frontend URL:', error);
      // Fallback to the generic farms subdomain in case of error
      const url = new URL(defaultFrontendUrl);
      const baseDomain = url.hostname.split('.').slice(-2).join('.');

      return `${url.protocol}//farms.${baseDomain}`;
    }
  }

  return defaultFrontendUrl;
};

/**
 * Send an email notification when a document is ready for signing
 * 
 * @param {Object} document - The signable document object
 * @param {Object} signer - The signer who should receive the email
 * @param {Object} sender - The user who sent the document
 * @param {Object} farm - The farm associated with the document
 * @param {string} signingUrl - The URL where the signer can sign the document
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDocumentForSigningEmail = async (document, signer, sender, farm, signingUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'documents', 'document-sent.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the sent date
    const sentDate = new Date(signer.sent_at || document.updated_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Format the expiration date if it exists
    let expirationDate = '';
    if (document.expires_at) {
      expirationDate = new Date(document.expires_at).toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    // Format document type for display
    const formattedDocumentType = document.document_type.charAt(0).toUpperCase() + 
      document.document_type.slice(1).replace(/_/g, ' ');

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{signerName}}/g, signer.signer_name)
      .replace(/{{senderName}}/g, `${sender.first_name} ${sender.last_name}`)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{documentTitle}}/g, document.title)
      .replace(/{{documentType}}/g, formattedDocumentType)
      .replace(/{{sentDate}}/g, sentDate)
      .replace(/{{signingUrl}}/g, signingUrl)
      .replace(/{{senderEmail}}/g, sender.email)
      .replace(/{{signerEmail}}/g, signer.signer_email)
      .replace(/{{documentId}}/g, document.id)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional expiration date
    if (document.expires_at) {
      emailTemplate = emailTemplate.replace(/{{#if expirationDate}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{expirationDate}}/g, expirationDate);
    } else {
      emailTemplate = emailTemplate.replace(/{{#if expirationDate}}[\s\S]*?{{\/if}}/g, '');
    }

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Documents" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: signer.signer_email,
      subject: `Document Ready for Signature: ${document.title}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<document-${document.id}-${signer.id}@nxtacre.com>`,
        'X-Document-ID': document.id,
        'X-Signer-ID': signer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending document for signing email:', error);
    throw error;
  }
};

/**
 * Send an email notification when a document is signed
 * 
 * @param {Object} document - The signable document object
 * @param {Object} signer - The signer who signed the document
 * @param {Object} recipient - The user who should receive the email
 * @param {string} documentUrl - The URL where the recipient can view the document
 * @param {boolean} isCompleted - Whether all signatures have been collected
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDocumentSignedEmail = async (document, signer, recipient, documentUrl, isCompleted) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'documents', 'document-signed.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the signed date
    const signedDate = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Format document type and status for display
    const formattedDocumentType = document.document_type.charAt(0).toUpperCase() + 
      document.document_type.slice(1).replace(/_/g, ' ');
    const formattedDocumentStatus = isCompleted ? 'Completed' : 'Partially Signed';

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{recipientName}}/g, recipient.first_name)
      .replace(/{{signerName}}/g, signer.signer_name)
      .replace(/{{documentTitle}}/g, document.title)
      .replace(/{{documentType}}/g, formattedDocumentType)
      .replace(/{{documentStatus}}/g, formattedDocumentStatus)
      .replace(/{{signerEmail}}/g, signer.signer_email)
      .replace(/{{signedDate}}/g, signedDate)
      .replace(/{{signerIP}}/g, signer.last_ip || 'Not recorded')
      .replace(/{{documentUrl}}/g, documentUrl)
      .replace(/{{recipientEmail}}/g, recipient.email)
      .replace(/{{documentId}}/g, document.id)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional completed status
    if (isCompleted) {
      emailTemplate = emailTemplate.replace(/{{#if isCompleted}}([\s\S]*?){{else}}[\s\S]*?{{\/if}}/g, '$1');
    } else {
      emailTemplate = emailTemplate.replace(/{{#if isCompleted}}[\s\S]*?{{else}}([\s\S]*?){{\/if}}/g, '$1');
    }

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Documents" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: recipient.email,
      subject: `Document Signed: ${document.title}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<document-signed-${document.id}-${signer.id}-${Date.now()}@nxtacre.com>`,
        'X-Document-ID': document.id,
        'X-Signer-ID': signer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending document signed email:', error);
    throw error;
  }
};

/**
 * Send an email notification when a document is declined
 * 
 * @param {Object} document - The signable document object
 * @param {Object} signer - The signer who declined the document
 * @param {Object} recipient - The user who should receive the email
 * @param {string} documentUrl - The URL where the recipient can view the document
 * @param {string} declineReason - The reason for declining (optional)
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDocumentDeclinedEmail = async (document, signer, recipient, documentUrl, declineReason) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'documents', 'document-declined.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the declined date
    const declinedDate = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Format document type for display
    const formattedDocumentType = document.document_type.charAt(0).toUpperCase() + 
      document.document_type.slice(1).replace(/_/g, ' ');

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{recipientName}}/g, recipient.first_name)
      .replace(/{{signerName}}/g, signer.signer_name)
      .replace(/{{documentTitle}}/g, document.title)
      .replace(/{{documentType}}/g, formattedDocumentType)
      .replace(/{{documentStatus}}/g, 'Declined')
      .replace(/{{signerEmail}}/g, signer.signer_email)
      .replace(/{{declinedDate}}/g, declinedDate)
      .replace(/{{signerIP}}/g, signer.last_ip || 'Not recorded')
      .replace(/{{documentUrl}}/g, documentUrl)
      .replace(/{{recipientEmail}}/g, recipient.email)
      .replace(/{{documentId}}/g, document.id)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional decline reason
    if (declineReason) {
      emailTemplate = emailTemplate.replace(/{{#if declineReason}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{declineReason}}/g, declineReason);
    } else {
      emailTemplate = emailTemplate.replace(/{{#if declineReason}}[\s\S]*?{{\/if}}/g, '');
    }

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Documents" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: recipient.email,
      subject: `Document Declined: ${document.title}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<document-declined-${document.id}-${signer.id}-${Date.now()}@nxtacre.com>`,
        'X-Document-ID': document.id,
        'X-Signer-ID': signer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending document declined email:', error);
    throw error;
  }
};

/**
 * Send an initial email notification for a newly created support ticket
 * 
 * @param {Object} ticket - The support ticket object
 * @param {Object} creator - The user who created the ticket
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendTicketCreatedEmail = async (ticket, creator, frontendUrl) => {
  try {
    // Generate a unique email thread ID if one doesn't exist
    if (!ticket.email_thread_id) {
      ticket.email_thread_id = `ticket-${ticket.id}-${uuidv4()}`;
      await ticket.save();
    }

    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'support', 'ticket-created.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the priority and status for display
    const formattedPriority = ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1);
    const formattedStatus = ticket.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{firstName}}/g, creator.first_name)
      .replace(/{{ticketId}}/g, ticket.id)
      .replace(/{{subject}}/g, ticket.subject)
      .replace(/{{status}}/g, formattedStatus)
      .replace(/{{priority}}/g, formattedPriority)
      .replace(/{{description}}/g, ticket.description)
      .replace(/{{ticketUrl}}/g, `${frontendUrl}/support/tickets/${ticket.id}`)
      .replace(/{{email}}/g, creator.email)
      .replace(/{{emailThreadId}}/g, ticket.email_thread_id)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Support" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: creator.email,
      subject: `[Ticket #${ticket.id}] ${ticket.subject}`,
      html: emailTemplate,
      headers: {
        'Message-ID': ticket.email_thread_id,
        'X-Ticket-ID': ticket.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);

    // Update the last email sent timestamp
    ticket.last_email_sent_at = new Date();
    await ticket.save();

    return result;
  } catch (error) {
    console.error('Error sending ticket created email:', error);
    throw error;
  }
};

/**
 * Send a welcome email to a user who has been invited to a farm
 * 
 * @param {Object} user - The user who has been invited
 * @param {Object} farm - The farm the user has been invited to
 * @param {string} role - The role assigned to the user in the farm
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendFarmInvitationEmail = async (user, farm, role, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'farm-invitation.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the role for display
    const formattedRole = role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{firstName}}/g, user.first_name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{email}}/g, user.email)
      .replace(/{{role}}/g, formattedRole)
      .replace(/{{loginUrl}}/g, `${frontendUrl}/login`)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Farms" <<EMAIL>>`,
      to: user.email,
      replyTo: farm.billing_email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Welcome to ${farm.name} on NxtAcre`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<farm-invitation-${farm.id}-${user.id}-${Date.now()}@nxtacre.com>`,
        'X-Farm-ID': farm.id,
        'X-User-ID': user.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending farm invitation email:', error);
    throw error;
  }
};

/**
 * Send an invoice to a customer via email
 * 
 * @param {Object} invoice - The invoice object
 * @param {Object} customer - The customer object (optional if recipientEmail is provided)
 * @param {Object} farm - The farm that issued the invoice
 * @param {Object} sender - The user who is sending the invoice
 * @param {string} recipientEmail - Custom email address to send the invoice to (optional)
 * @param {string} frontendUrl - The URL to the frontend application
 * @param {string} trackingId - Unique ID for tracking email views
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendInvoiceEmail = async (invoice, customer, farm, sender, recipientEmail, frontendUrl, trackingId) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'invoice', 'invoice-request.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Determine the recipient email
    const toEmail = recipientEmail || (customer ? customer.email : null);

    if (!toEmail) {
      throw new Error('No recipient email provided');
    }

    // Format the dates
    const invoiceDate = new Date(invoice.issue_date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const dueDate = new Date(invoice.due_date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Format the amount
    const amount = parseFloat(invoice.total_amount).toFixed(2);

    // Get customer name
    const customerName = customer ? customer.name : 'Valued Customer';

    // Get the farm-specific frontend URL
    const farmFrontendUrl = await getFrontendUrl(null, farm.id);

    // Generate an auth code for direct invoice access
    const customerId = customer ? customer.id : null;
    const authCode = generateInvoiceAuthCode(invoice.id, customerId, farm.id);

    // Create the invoice URL with auth code
    const invoiceUrl = `${farmFrontendUrl}/invoices/${invoice.id}/view?auth=${authCode}`;

    // Create tracking pixel URL
    const baseUrl = process.env.API_URL || 'https://api.nxtacre.com';
    const trackingPixelUrl = `${baseUrl}/invoices/track/${trackingId}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customerName)
      .replace(/{{invoiceNumber}}/g, invoice.invoice_number)
      .replace(/{{invoiceDate}}/g, invoiceDate)
      .replace(/{{dueDate}}/g, dueDate)
      .replace(/{{amount}}/g, amount)
      .replace(/{{invoiceUrl}}/g, invoiceUrl)
      .replace(/{{email}}/g, toEmail)
      .replace(/{{year}}/g, new Date().getFullYear())
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{farmAddress}}/g, farm.billing_address || 'N/A')
      .replace(/{{farmCity}}/g, farm.billing_city || 'N/A')
      .replace(/{{farmState}}/g, farm.billing_state || 'N/A')
      .replace(/{{farmZipCode}}/g, farm.billing_zip_code || 'N/A')
      .replace(/{{farmCountry}}/g, farm.billing_country || 'USA')
      .replace(/{{farmEmail}}/g, farm.billing_email || 'N/A');

    // Add tracking pixel at the end of the email
    emailTemplate = emailTemplate.replace('</body>', `<img src="${trackingPixelUrl}" alt="" width="1" height="1" style="display:none;" />\n</body>`);

    // Generate PDF invoice
    const pdfBuffer = await generateInvoicePdfBuffer(invoice.id);

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: toEmail,
      replyTo: farm.billing_email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Invoice #${invoice.invoice_number} from ${farm.name}`,
      html: emailTemplate,
      attachments: [
        {
          filename: `Invoice_${invoice.invoice_number}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }
      ],
      headers: {
        'Message-ID': `<invoice-${invoice.id}-${Date.now()}@nxtacre.com>`,
        'X-Invoice-ID': invoice.id,
        'X-Farm-ID': farm.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending invoice email:', error);
    throw error;
  }
};

/**
 * Send a reminder email for an invoice
 * 
 * @param {Object} invoice - The invoice object
 * @param {Object} customer - The customer object (optional if recipientEmail is provided)
 * @param {Object} farm - The farm that issued the invoice
 * @param {Object} sender - The user who is sending the reminder
 * @param {string} recipientEmail - Custom email address to send the reminder to (optional)
 * @param {string} frontendUrl - The URL to the frontend application
 * @param {boolean} isOverdue - Whether the invoice is overdue
 * @param {string} trackingId - Unique ID for tracking email views
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendInvoiceReminderEmail = async (invoice, customer, farm, sender, recipientEmail, frontendUrl, isOverdue, trackingId) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'invoice', 'invoice-reminder.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Determine the recipient email
    const toEmail = recipientEmail || (customer ? customer.email : null);

    if (!toEmail) {
      throw new Error('No recipient email provided');
    }

    // Format the dates
    const invoiceDate = new Date(invoice.issue_date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const dueDate = new Date(invoice.due_date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Format the amount
    const amount = parseFloat(invoice.total_amount).toFixed(2);

    // Get customer name
    const customerName = customer ? customer.name : 'Valued Customer';

    // Calculate days overdue if applicable
    let daysOverdue = 0;
    if (isOverdue) {
      const today = new Date();
      const dueDateObj = new Date(invoice.due_date);
      const diffTime = Math.abs(today - dueDateObj);
      daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    // Get the farm-specific frontend URL
    const farmFrontendUrl = await getFrontendUrl(null, farm.id);

    // Generate an auth code for direct invoice access
    const customerId = customer ? customer.id : null;
    const authCode = generateInvoiceAuthCode(invoice.id, customerId, farm.id);

    // Create the invoice URL with auth code
    const invoiceUrl = `${farmFrontendUrl}/invoices/${invoice.id}/view?auth=${authCode}`;

    // Create tracking pixel URL
    const baseUrl = process.env.API_URL || 'https://api.nxtacre.com';
    const trackingPixelUrl = `${baseUrl}/invoices/track/${trackingId}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customerName)
      .replace(/{{invoiceNumber}}/g, invoice.invoice_number)
      .replace(/{{invoiceDate}}/g, invoiceDate)
      .replace(/{{dueDate}}/g, dueDate)
      .replace(/{{amount}}/g, amount)
      .replace(/{{daysOverdue}}/g, daysOverdue)
      .replace(/{{invoiceUrl}}/g, invoiceUrl)
      .replace(/{{email}}/g, toEmail)
      .replace(/{{year}}/g, new Date().getFullYear())
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{farmAddress}}/g, farm.billing_address || 'N/A')
      .replace(/{{farmCity}}/g, farm.billing_city || 'N/A')
      .replace(/{{farmState}}/g, farm.billing_state || 'N/A')
      .replace(/{{farmZipCode}}/g, farm.billing_zip_code || 'N/A')
      .replace(/{{farmCountry}}/g, farm.billing_country || 'USA')
      .replace(/{{farmEmail}}/g, farm.billing_email || 'N/A');

    // Add tracking pixel at the end of the email
    emailTemplate = emailTemplate.replace('</body>', `<img src="${trackingPixelUrl}" alt="" width="1" height="1" style="display:none;" />\n</body>`);

    // Handle conditional sections
    if (isOverdue) {
      emailTemplate = emailTemplate.replace(/{{#if overdue}}([\s\S]*?){{\/if}}/g, '$1')
                                   .replace(/{{#if upcoming}}[\s\S]*?{{\/if}}/g, '');
    } else {
      emailTemplate = emailTemplate.replace(/{{#if upcoming}}([\s\S]*?){{\/if}}/g, '$1')
                                   .replace(/{{#if overdue}}[\s\S]*?{{\/if}}/g, '');
    }

    // Generate PDF invoice
    const pdfBuffer = await generateInvoicePdfBuffer(invoice.id);

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: toEmail,
      replyTo: farm.billing_email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: isOverdue 
        ? `OVERDUE: Invoice #${invoice.invoice_number} from ${farm.name}`
        : `Payment Reminder: Invoice #${invoice.invoice_number} from ${farm.name}`,
      html: emailTemplate,
      attachments: [
        {
          filename: `Invoice_${invoice.invoice_number}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }
      ],
      headers: {
        'Message-ID': `<invoice-reminder-${invoice.id}-${Date.now()}@nxtacre.com>`,
        'X-Invoice-ID': invoice.id,
        'X-Farm-ID': farm.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending invoice reminder email:', error);
    throw error;
  }
};
