import axios from 'axios';
import { API_URL } from '../config';

// Define types for role permissions
export interface RolePermission {
  id: string;
  farm_id: string;
  role_name: string;
  feature: string;
  can_view: boolean;
  can_create: boolean;
  can_edit: boolean;
  can_delete: boolean;
}

// Define types for user roles
export type UserRole = 'farm_owner' | 'farm_admin' | 'farm_manager' | 'farm_employee' | 'accountant';

/**
 * Get the user's role for a specific farm
 */
export const getUserRoleForFarm = async (userId: string, farmId: string): Promise<UserRole | null> => {
  try {
    // Get all farms for the user
    const response = await axios.get(`${API_URL}/farms/user/${userId}`);

    // Find the specific farm and get the user's role
    const farm = response.data.farms?.find((farm: any) => farm.id === farmId);

    if (farm && farm.UserFarm && farm.UserFarm.role) {
      return farm.UserFarm.role as UserRole;
    }

    return null;
  } catch (error) {
    console.error(`Error fetching user role for farm ${farmId}:`, error);
    return null;
  }
};

// Define types for permission types
export type PermissionType = 'view' | 'create' | 'edit' | 'delete';

/**
 * Get default permissions for a role
 */
export const getDefaultRolePermissions = async (role: UserRole): Promise<RolePermission[]> => {
  try {
    // First, get all global roles
    const rolesResponse = await axios.get(`${API_URL}/roles/global`);
    const roles = rolesResponse.data;

    // Find the role with the matching name
    const roleObj = roles.find((r: any) => r.name === role);

    if (!roleObj) {
      console.error(`Global role ${role} not found`);
      // Return mock permissions as a fallback
      return getMockRolePermissions(role);
    }

    // Use the role ID to fetch default permissions
    const response = await axios.get(`${API_URL}/permissions/defaults/${roleObj.id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching default permissions for role ${role}:`, error);
    // Return mock permissions as a fallback
    return getMockRolePermissions(role);
  }
};

/**
 * Get permissions for a role in a farm
 */
export const getFarmRolePermissions = async (farmId: string, role: UserRole): Promise<RolePermission[]> => {
  try {
    // First, get all roles for the farm
    const rolesResponse = await axios.get(`${API_URL}/roles/farm/${farmId}`);
    const roles = rolesResponse.data;

    // Find the role with the matching name
    const roleObj = roles.find((r: any) => r.name === role);

    if (!roleObj) {
      console.error(`Role ${role} not found in farm ${farmId}`);
      // Return mock permissions as a fallback
      return getMockRolePermissions(role);
    }

    // Use the role ID to fetch permissions
    const response = await axios.get(`${API_URL}/permissions/farm/${farmId}/role/${roleObj.id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching permissions for role ${role} in farm ${farmId}:`, error);
    // Return mock permissions as a fallback
    return getMockRolePermissions(role);
  }
};

/**
 * Set permissions for a role in a farm
 */
export const setFarmRolePermissions = async (
  farmId: string,
  role: UserRole,
  feature: string,
  permissions: { [key in PermissionType]?: boolean }
): Promise<RolePermission> => {
  try {
    // First, get all roles for the farm
    const rolesResponse = await axios.get(`${API_URL}/roles/farm/${farmId}`);
    const roles = rolesResponse.data;

    // Find the role with the matching name
    const roleObj = roles.find((r: any) => r.name === role);

    if (!roleObj) {
      console.error(`Role ${role} not found in farm ${farmId}`);
      throw new Error(`Role ${role} not found in farm ${farmId}`);
    }

    // Use the role ID to set permissions
    const response = await axios.post(`${API_URL}/permissions/farm/${farmId}/role/${roleObj.id}`, {
      feature,
      ...permissions
    });
    return response.data;
  } catch (error) {
    console.error(`Error setting permissions for role ${role} in farm ${farmId}:`, error);
    throw error;
  }
};

/**
 * Check if a user has permission for a feature
 */
export const checkPermission = async (
  farmId: string,
  userId: string,
  feature: string,
  permission: PermissionType
): Promise<boolean> => {
  try {
    // Add timestamp to prevent caching and 304 responses
    const timestamp = new Date().getTime();
    const response = await axios.get(
      `${API_URL}/permissions/check?farmId=${farmId}&userId=${userId}&feature=${feature}&permission=${permission}&_t=${timestamp}`,
      {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
    return response.data.hasPermission;
  } catch (error) {
    console.error(`Error checking permission for user ${userId} in farm ${farmId}:`, error);

    // Use mock permissions as a fallback
    try {
      // Get the user's role for the farm
      const userRole = await getUserRoleForFarm(userId, farmId);

      // If we couldn't get the user's role, default to allowing access
      if (!userRole) {
        console.log(`No role found for user ${userId} in farm ${farmId}, defaulting to allow access`);
        return true;
      }

      // Get mock permissions for the user's role
      const mockPermissions = getMockRolePermissions(userRole);

      // Find the permission for the requested feature
      const featurePermission = mockPermissions.find(p => p.feature === feature);

      // If the feature isn't found in mock permissions, default to allowing access
      if (!featurePermission) {
        console.log(`No mock permission found for feature ${feature}, defaulting to allow access`);
        return true;
      }

      // Return the appropriate permission value
      switch (permission) {
        case 'view':
          return featurePermission.can_view;
        case 'create':
          return featurePermission.can_create;
        case 'edit':
          return featurePermission.can_edit;
        case 'delete':
          return featurePermission.can_delete;
        default:
          return true;
      }
    } catch (fallbackError) {
      console.error('Error using mock permissions fallback:', fallbackError);
      // If even the fallback fails, default to allowing access to ensure widgets are displayed
      return true;
    }
  }
};

/**
 * Get all features that a user has permission to view
 */
export const getUserViewableFeatures = async (farmId: string, userId: string): Promise<string[]> => {
  try {
    const response = await axios.get(`${API_URL}/permissions/user/${userId}/farm/${farmId}/viewable`);
    return response.data.features;
  } catch (error) {
    console.error(`Error fetching viewable features for user ${userId} in farm ${farmId}:`, error);
    return [];
  }
};


/**
 * Mock implementation for development
 * This function returns mock data for role permissions
 * It should be replaced with actual API calls in production
 */
export const getMockRolePermissions = (role: UserRole): RolePermission[] => {
  // Define default permissions for each role
  const permissions: { [key in UserRole]: { [key: string]: { [key in PermissionType]: boolean } } } = {
    farm_owner: {
      // Farm owners have full access to everything
      // Core Farm Management
      farms: { view: true, create: true, edit: true, delete: true },
      fields: { view: true, create: true, edit: true, delete: true },
      crops: { view: true, create: true, edit: true, delete: true },
      'crop-types': { view: true, create: true, edit: true, delete: true },
      livestock: { view: true, create: true, edit: true, delete: true },
      soil: { view: true, create: true, edit: true, delete: true },
      weather: { view: true, create: true, edit: true, delete: true },
      vets: { view: true, create: true, edit: true, delete: true },

      // Resource Management
      equipment: { view: true, create: true, edit: true, delete: true },
      'equipment-sharing': { view: true, create: true, edit: true, delete: true },
      maintenance: { view: true, create: true, edit: true, delete: true },
      inventory: { view: true, create: true, edit: true, delete: true },
      employees: { view: true, create: true, edit: true, delete: true },
      suppliers: { view: true, create: true, edit: true, delete: true },

      // Financial Management
      finances: { view: true, create: true, edit: true, delete: true },
      transactions: { view: true, create: true, edit: true, delete: true },
      'link-account': { view: true, create: true, edit: true, delete: true },
      customers: { view: true, create: true, edit: true, delete: true },
      invoices: { view: true, create: true, edit: true, delete: true },
      receipts: { view: true, create: true, edit: true, delete: true },
      bills: { view: true, create: true, edit: true, delete: true },
      billing: { view: true, create: true, edit: true, delete: true },
      products: { view: true, create: true, edit: true, delete: true },

      // HR & Labor Management
      'hr-management': { view: true, create: true, edit: true, delete: true },
      'labor-management': { view: true, create: true, edit: true, delete: true },

      // Transport Management
      'transport-management': { view: true, create: true, edit: true, delete: true },

      // Market & Analytics
      'market-integration': { view: true, create: true, edit: true, delete: true },
      'market-prices': { view: true, create: true, edit: true, delete: true },
      'field-health': { view: true, create: true, edit: true, delete: true },
      'sustainability-tracking': { view: true, create: true, edit: true, delete: true },

      // Tasks & Workflows
      tasks: { view: true, create: true, edit: true, delete: true },
      workflows: { view: true, create: true, edit: true, delete: true },

      // AI Features
      'ai-assistant': { view: true, create: true, edit: true, delete: true },
      'crop-management': { view: true, create: true, edit: true, delete: true },

      // Document Management
      'document-management': { view: true, create: true, edit: true, delete: true },

      // Reports & Settings
      reports: { view: true, create: true, edit: true, delete: true },
      settings: { view: true, create: true, edit: true, delete: true },
      roles: { view: true, create: true, edit: true, delete: true },
      integrations: { view: true, create: true, edit: true, delete: true },
      iot: { view: true, create: true, edit: true, delete: true },
      alerts: { view: true, create: true, edit: true, delete: true },
      'data-migration': { view: true, create: true, edit: true, delete: true },
      'menu-customization': { view: true, create: true, edit: true, delete: true },
      sessions: { view: true, create: true, edit: true, delete: true },
      'role-management': { view: true, create: true, edit: true, delete: true },
      grants: { view: true, create: true, edit: true, delete: true },
      'mobile-features': { view: true, create: true, edit: true, delete: true },

      // Support
      support: { view: true, create: true, edit: true, delete: true },

      // Chat
      chat: { view: true, create: true, edit: true, delete: true },

      // User Settings
      profile: { view: true, create: true, edit: true, delete: true },
      subscriptions: { view: true, create: true, edit: true, delete: true },
      'business-account': { view: true, create: true, edit: true, delete: true },
      faq: { view: true, create: true, edit: true, delete: true }
    },
    farm_admin: {
      // Farm admins have full access to most things, but can't delete farms or change settings
      // Core Farm Management
      farms: { view: true, create: false, edit: true, delete: false },
      fields: { view: true, create: true, edit: true, delete: true },
      crops: { view: true, create: true, edit: true, delete: true },
      'crop-types': { view: true, create: true, edit: true, delete: true },
      livestock: { view: true, create: true, edit: true, delete: true },
      soil: { view: true, create: true, edit: true, delete: true },
      weather: { view: true, create: true, edit: true, delete: true },
      vets: { view: true, create: true, edit: true, delete: true },

      // Resource Management
      equipment: { view: true, create: true, edit: true, delete: true },
      'equipment-sharing': { view: true, create: true, edit: true, delete: true },
      maintenance: { view: true, create: true, edit: true, delete: true },
      inventory: { view: true, create: true, edit: true, delete: true },
      employees: { view: true, create: true, edit: true, delete: true },
      suppliers: { view: true, create: true, edit: true, delete: true },

      // Financial Management
      finances: { view: true, create: true, edit: true, delete: true },
      transactions: { view: true, create: true, edit: true, delete: true },
      'link-account': { view: true, create: true, edit: true, delete: true },
      customers: { view: true, create: true, edit: true, delete: true },
      invoices: { view: true, create: true, edit: true, delete: true },
      receipts: { view: true, create: true, edit: true, delete: true },
      bills: { view: true, create: true, edit: true, delete: true },
      billing: { view: true, create: true, edit: true, delete: true },
      products: { view: true, create: true, edit: true, delete: true },

      // HR & Labor Management
      'hr-management': { view: true, create: true, edit: true, delete: true },
      'labor-management': { view: true, create: true, edit: true, delete: true },

      // Transport Management
      'transport-management': { view: true, create: true, edit: true, delete: true },

      // Market & Analytics
      'market-integration': { view: true, create: true, edit: true, delete: true },
      'market-prices': { view: true, create: true, edit: true, delete: true },
      'field-health': { view: true, create: true, edit: true, delete: true },
      'sustainability-tracking': { view: true, create: true, edit: true, delete: true },

      // Tasks & Workflows
      tasks: { view: true, create: true, edit: true, delete: true },
      workflows: { view: true, create: true, edit: true, delete: true },

      // AI Features
      'ai-assistant': { view: true, create: true, edit: true, delete: true },
      'crop-management': { view: true, create: true, edit: true, delete: true },

      // Document Management
      'document-management': { view: true, create: true, edit: true, delete: true },

      // Reports & Settings
      reports: { view: true, create: true, edit: true, delete: true },
      settings: { view: true, create: false, edit: false, delete: false },
      roles: { view: true, create: false, edit: false, delete: false },
      integrations: { view: true, create: false, edit: false, delete: false },
      iot: { view: true, create: true, edit: true, delete: false },
      alerts: { view: true, create: true, edit: true, delete: false },
      'data-migration': { view: true, create: false, edit: false, delete: false },
      'menu-customization': { view: true, create: false, edit: false, delete: false },
      sessions: { view: true, create: false, edit: false, delete: false },
      'role-management': { view: true, create: false, edit: false, delete: false },
      grants: { view: true, create: true, edit: true, delete: false },
      'mobile-features': { view: true, create: false, edit: false, delete: false },

      // Support
      support: { view: true, create: true, edit: true, delete: false },

      // Chat
      chat: { view: true, create: true, edit: true, delete: false },

      // User Settings
      profile: { view: true, create: true, edit: true, delete: false },
      subscriptions: { view: true, create: false, edit: false, delete: false },
      'business-account': { view: true, create: false, edit: false, delete: false },
      faq: { view: true, create: false, edit: false, delete: false }
    },
    farm_manager: {
      // Farm managers can view and edit most things, but can't delete or create new farms
      // Core Farm Management
      farms: { view: true, create: false, edit: false, delete: false },
      fields: { view: true, create: true, edit: true, delete: false },
      crops: { view: true, create: true, edit: true, delete: false },
      'crop-types': { view: true, create: true, edit: true, delete: false },
      livestock: { view: true, create: true, edit: true, delete: false },
      soil: { view: true, create: true, edit: true, delete: false },
      weather: { view: true, create: false, edit: false, delete: false },
      vets: { view: true, create: false, edit: false, delete: false },

      // Resource Management
      equipment: { view: true, create: true, edit: true, delete: false },
      'equipment-sharing': { view: true, create: true, edit: true, delete: false },
      maintenance: { view: true, create: true, edit: true, delete: false },
      inventory: { view: true, create: true, edit: true, delete: false },
      employees: { view: true, create: false, edit: true, delete: false },
      suppliers: { view: true, create: false, edit: false, delete: false },

      // Financial Management
      finances: { view: true, create: true, edit: true, delete: false },
      transactions: { view: true, create: true, edit: true, delete: false },
      'link-account': { view: true, create: false, edit: false, delete: false },
      customers: { view: true, create: true, edit: true, delete: false },
      invoices: { view: true, create: true, edit: true, delete: false },
      receipts: { view: true, create: true, edit: true, delete: false },
      bills: { view: true, create: true, edit: true, delete: false },
      billing: { view: true, create: false, edit: false, delete: false },
      products: { view: true, create: true, edit: true, delete: false },

      // HR & Labor Management
      'hr-management': { view: true, create: true, edit: true, delete: false },
      'labor-management': { view: true, create: true, edit: true, delete: false },

      // Transport Management
      'transport-management': { view: true, create: true, edit: true, delete: false },

      // Market & Analytics
      'market-integration': { view: true, create: false, edit: false, delete: false },
      'market-prices': { view: true, create: false, edit: false, delete: false },
      'field-health': { view: true, create: false, edit: false, delete: false },
      'sustainability-tracking': { view: true, create: false, edit: false, delete: false },

      // Tasks & Workflows
      tasks: { view: true, create: true, edit: true, delete: false },
      workflows: { view: true, create: false, edit: false, delete: false },

      // AI Features
      'ai-assistant': { view: true, create: false, edit: false, delete: false },
      'crop-management': { view: true, create: true, edit: true, delete: false },

      // Document Management
      'document-management': { view: true, create: true, edit: true, delete: false },

      // Reports & Settings
      reports: { view: true, create: true, edit: false, delete: false },
      settings: { view: false, create: false, edit: false, delete: false },
      roles: { view: false, create: false, edit: false, delete: false },
      integrations: { view: false, create: false, edit: false, delete: false },
      iot: { view: true, create: false, edit: false, delete: false },
      alerts: { view: true, create: true, edit: true, delete: false },
      'data-migration': { view: false, create: false, edit: false, delete: false },
      'menu-customization': { view: false, create: false, edit: false, delete: false },
      sessions: { view: false, create: false, edit: false, delete: false },
      'role-management': { view: false, create: false, edit: false, delete: false },
      grants: { view: true, create: false, edit: false, delete: false },
      'mobile-features': { view: false, create: false, edit: false, delete: false },

      // Support
      support: { view: true, create: true, edit: false, delete: false },

      // User Settings
      profile: { view: true, create: false, edit: true, delete: false },
      subscriptions: { view: false, create: false, edit: false, delete: false },
      'business-account': { view: false, create: false, edit: false, delete: false },
      faq: { view: true, create: false, edit: false, delete: false }
    },
    farm_employee: {
      // Farm employees can view most things, but can only edit their own data
      // Core Farm Management
      farms: { view: true, create: false, edit: false, delete: false },
      fields: { view: true, create: false, edit: false, delete: false },
      crops: { view: true, create: false, edit: false, delete: false },
      'crop-types': { view: true, create: false, edit: false, delete: false },
      livestock: { view: true, create: false, edit: false, delete: false },
      soil: { view: true, create: false, edit: false, delete: false },
      weather: { view: true, create: false, edit: false, delete: false },
      vets: { view: true, create: false, edit: false, delete: false },

      // Resource Management
      equipment: { view: true, create: false, edit: false, delete: false },
      'equipment-sharing': { view: true, create: false, edit: false, delete: false },
      maintenance: { view: true, create: false, edit: false, delete: false },
      inventory: { view: true, create: false, edit: false, delete: false },
      employees: { view: false, create: false, edit: false, delete: false },
      suppliers: { view: true, create: false, edit: false, delete: false },

      // Financial Management
      finances: { view: false, create: false, edit: false, delete: false },
      transactions: { view: false, create: false, edit: false, delete: false },
      'link-account': { view: false, create: false, edit: false, delete: false },
      customers: { view: false, create: false, edit: false, delete: false },
      invoices: { view: false, create: false, edit: false, delete: false },
      receipts: { view: false, create: false, edit: false, delete: false },
      billing: { view: false, create: false, edit: false, delete: false },
      products: { view: true, create: false, edit: false, delete: false },

      // HR & Labor Management
      'hr-management': { view: true, create: false, edit: false, delete: false },
      'labor-management': { view: true, create: false, edit: false, delete: false },

      // Transport Management
      'transport-management': { view: true, create: false, edit: false, delete: false },

      // Market & Analytics
      'market-integration': { view: true, create: false, edit: false, delete: false },
      'market-prices': { view: true, create: false, edit: false, delete: false },
      'field-health': { view: true, create: false, edit: false, delete: false },
      'sustainability-tracking': { view: true, create: false, edit: false, delete: false },

      // Tasks & Workflows
      tasks: { view: true, create: false, edit: false, delete: false },
      workflows: { view: true, create: false, edit: false, delete: false },

      // AI Features
      'ai-assistant': { view: true, create: false, edit: false, delete: false },
      'crop-management': { view: true, create: false, edit: false, delete: false },

      // Document Management
      'document-management': { view: true, create: false, edit: false, delete: false },

      // Reports & Settings
      reports: { view: false, create: false, edit: false, delete: false },
      settings: { view: false, create: false, edit: false, delete: false },
      roles: { view: false, create: false, edit: false, delete: false },
      integrations: { view: false, create: false, edit: false, delete: false },
      iot: { view: false, create: false, edit: false, delete: false },
      alerts: { view: true, create: false, edit: false, delete: false },
      'data-migration': { view: false, create: false, edit: false, delete: false },
      'menu-customization': { view: false, create: false, edit: false, delete: false },
      sessions: { view: false, create: false, edit: false, delete: false },
      'role-management': { view: false, create: false, edit: false, delete: false },
      grants: { view: true, create: false, edit: false, delete: false },
      'mobile-features': { view: false, create: false, edit: false, delete: false },

      // Support
      support: { view: true, create: true, edit: false, delete: false },

      // User Settings
      profile: { view: true, create: false, edit: true, delete: false },
      subscriptions: { view: false, create: false, edit: false, delete: false },
      'business-account': { view: false, create: false, edit: false, delete: false },
      faq: { view: true, create: false, edit: false, delete: false }
    },
    accountant: {
      // Accountants can view and edit financial data
      // Core Farm Management
      farms: { view: true, create: false, edit: false, delete: false },
      fields: { view: false, create: false, edit: false, delete: false },
      crops: { view: false, create: false, edit: false, delete: false },
      'crop-types': { view: false, create: false, edit: false, delete: false },
      livestock: { view: false, create: false, edit: false, delete: false },
      soil: { view: false, create: false, edit: false, delete: false },
      weather: { view: false, create: false, edit: false, delete: false },
      vets: { view: false, create: false, edit: false, delete: false },

      // Resource Management
      equipment: { view: false, create: false, edit: false, delete: false },
      'equipment-sharing': { view: false, create: false, edit: false, delete: false },
      maintenance: { view: false, create: false, edit: false, delete: false },
      inventory: { view: true, create: false, edit: false, delete: false },
      employees: { view: true, create: false, edit: false, delete: false },
      suppliers: { view: true, create: false, edit: false, delete: false },

      // Financial Management
      finances: { view: true, create: true, edit: true, delete: false },
      transactions: { view: true, create: true, edit: true, delete: false },
      'link-account': { view: true, create: true, edit: true, delete: false },
      customers: { view: true, create: true, edit: true, delete: false },
      invoices: { view: true, create: true, edit: true, delete: false },
      receipts: { view: true, create: true, edit: true, delete: false },
      bills: { view: true, create: true, edit: true, delete: false },
      billing: { view: true, create: true, edit: true, delete: false },
      products: { view: true, create: false, edit: false, delete: false },

      // HR & Labor Management
      'hr-management': { view: true, create: false, edit: false, delete: false },
      'labor-management': { view: true, create: false, edit: false, delete: false },

      // Transport Management
      'transport-management': { view: false, create: false, edit: false, delete: false },

      // Market & Analytics
      'market-integration': { view: false, create: false, edit: false, delete: false },
      'market-prices': { view: true, create: false, edit: false, delete: false },
      'field-health': { view: false, create: false, edit: false, delete: false },
      'sustainability-tracking': { view: false, create: false, edit: false, delete: false },

      // Tasks & Workflows
      tasks: { view: false, create: false, edit: false, delete: false },
      workflows: { view: false, create: false, edit: false, delete: false },

      // AI Features
      'ai-assistant': { view: false, create: false, edit: false, delete: false },
      'crop-management': { view: false, create: false, edit: false, delete: false },

      // Document Management
      'document-management': { view: true, create: false, edit: false, delete: false },

      // Reports & Settings
      reports: { view: true, create: true, edit: false, delete: false },
      settings: { view: false, create: false, edit: false, delete: false },
      roles: { view: false, create: false, edit: false, delete: false },
      integrations: { view: false, create: false, edit: false, delete: false },
      iot: { view: false, create: false, edit: false, delete: false },
      alerts: { view: false, create: false, edit: false, delete: false },
      'data-migration': { view: false, create: false, edit: false, delete: false },
      'menu-customization': { view: false, create: false, edit: false, delete: false },
      sessions: { view: false, create: false, edit: false, delete: false },
      'role-management': { view: false, create: false, edit: false, delete: false },
      grants: { view: true, create: false, edit: false, delete: false },
      'mobile-features': { view: false, create: false, edit: false, delete: false },

      // Support
      support: { view: true, create: true, edit: false, delete: false },

      // User Settings
      profile: { view: true, create: false, edit: true, delete: false },
      subscriptions: { view: false, create: false, edit: false, delete: false },
      'business-account': { view: false, create: false, edit: false, delete: false },
      faq: { view: true, create: false, edit: false, delete: false }
    }
  };

  // Convert the permissions object to an array of RolePermission objects
  const result: RolePermission[] = [];

  Object.entries(permissions[role]).forEach(([feature, perms]) => {
    result.push({
      id: `mock-${role}-${feature}`,
      farm_id: 'mock-farm-id',
      role_name: role,
      feature,
      can_view: perms.view,
      can_create: perms.create,
      can_edit: perms.edit,
      can_delete: perms.delete
    });
  });

  return result;
};
