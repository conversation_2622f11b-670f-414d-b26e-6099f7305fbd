import BillCategory from '../models/BillCategory.js';
import Bill from '../models/Bill.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

// Get all bill categories for a farm
export const getFarmBillCategories = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all categories for the farm
    const categories = await BillCategory.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ categories });
  } catch (error) {
    console.error('Error getting bill categories:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single bill category by ID
export const getBillCategoryById = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const category = await BillCategory.findByPk(categoryId);
    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }

    return res.status(200).json({ category });
  } catch (error) {
    console.error('Error getting bill category:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new bill category
export const createBillCategory = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId, name, description, color } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Name is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if category with same name already exists for this farm
    const existingCategory = await BillCategory.findOne({
      where: {
        farm_id: farmId,
        name: {
          [Op.iLike]: name
        }
      }
    });

    if (existingCategory) {
      await transaction.rollback();
      return res.status(400).json({ error: 'A category with this name already exists' });
    }

    // Create category
    const category = await BillCategory.create({
      farm_id: farmId,
      name,
      description,
      color
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Category created successfully',
      category
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating bill category:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a bill category
export const updateBillCategory = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { categoryId } = req.params;
    const { name, description, color } = req.body;

    // Find category to ensure it exists
    const category = await BillCategory.findByPk(categoryId);
    if (!category) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Category not found' });
    }

    // If name is being updated, check for duplicates
    if (name && name !== category.name) {
      const existingCategory = await BillCategory.findOne({
        where: {
          farm_id: category.farm_id,
          name: {
            [Op.iLike]: name
          },
          id: {
            [Op.ne]: categoryId
          }
        }
      });

      if (existingCategory) {
        await transaction.rollback();
        return res.status(400).json({ error: 'A category with this name already exists' });
      }
    }

    // Update category
    await category.update({
      name: name || category.name,
      description: description !== undefined ? description : category.description,
      color: color || category.color
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Category updated successfully',
      category
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating bill category:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a bill category
export const deleteBillCategory = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { categoryId } = req.params;

    // Find category to ensure it exists
    const category = await BillCategory.findByPk(categoryId);
    if (!category) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Category not found' });
    }

    // Check if any bills are using this category
    const billsCount = await Bill.count({
      where: { category_id: categoryId }
    });

    if (billsCount > 0) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Cannot delete category because it is being used by bills',
        billsCount
      });
    }

    // Delete category
    await category.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Category deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting bill category:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get bill category usage statistics
export const getCategoryUsageStats = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all categories for the farm
    const categories = await BillCategory.findAll({
      where: { farm_id: farmId },
      attributes: ['id', 'name', 'color']
    });

    // Get usage statistics for each category
    const categoryStats = await Promise.all(categories.map(async (category) => {
      const totalBills = await Bill.count({
        where: { category_id: category.id }
      });

      const totalAmount = await Bill.sum('amount', {
        where: { category_id: category.id }
      });

      const paidAmount = await Bill.sum('amount', {
        where: { 
          category_id: category.id,
          status: 'paid'
        }
      });

      const unpaidAmount = await Bill.sum('amount', {
        where: { 
          category_id: category.id,
          status: 'unpaid'
        }
      });

      const partialAmount = await Bill.sum('amount', {
        where: { 
          category_id: category.id,
          status: 'partial'
        }
      });

      return {
        id: category.id,
        name: category.name,
        color: category.color,
        totalBills: totalBills || 0,
        totalAmount: totalAmount || 0,
        paidAmount: paidAmount || 0,
        unpaidAmount: unpaidAmount || 0,
        partialAmount: partialAmount || 0
      };
    }));

    return res.status(200).json({ categoryStats });
  } catch (error) {
    console.error('Error getting category usage stats:', error);
    return res.status(500).json({ error: error.message });
  }
};