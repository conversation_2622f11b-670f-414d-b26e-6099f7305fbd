-- Migration: Fix column types in users table
-- This migration changes the data types of created_at, updated_at, reset_password_expires, and email_2fa_expires columns in the users table

SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Log the start of the migration
SELECT log_migration_step('Starting fix_users_column_types migration');

-- First, create a backup of the users table
CREATE TABLE users_backup AS SELECT * FROM users;
SELECT log_migration_step('Created backup table users_backup');

-- Update the column types
ALTER TABLE users 
  ALTER COLUMN created_at TYPE timestamp with time zone USING created_at::timestamp with time zone,
  ALTER COLUMN updated_at TYPE timestamp with time zone USING updated_at::timestamp with time zone,
  ALTER COLUMN reset_password_expires TYPE timestamp with time zone USING 
    CASE 
      WHEN reset_password_expires IS NULL THEN NULL
      WHEN reset_password_expires = '' THEN NULL
      ELSE reset_password_expires::timestamp with time zone
    END,
  ALTER COLUMN email_2fa_expires TYPE timestamp with time zone USING 
    CASE 
      WHEN email_2fa_expires IS NULL THEN NULL
      WHEN email_2fa_expires = '' THEN NULL
      ELSE email_2fa_expires::timestamp with time zone
    END;

SELECT log_migration_step('Updated column types in users table');

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status, dependencies)
    VALUES (
      uuid_generate_v4(),
      'Fix column types in users table',
      'webapp/server/db/migrations/fix_users_column_types.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      NOW(),
      'completed',
      ARRAY['fix_user_farms_column_types.sql']
    );
    
    SELECT log_migration_step('Migration recorded in database_migrations table');
  ELSE
    SELECT log_migration_step('database_migrations table does not exist, skipping recording');
  END IF;
END $$;

-- Log the completion of the migration
SELECT log_migration_step('Completed fix_users_column_types migration');

-- Drop the logging function
DROP FUNCTION IF EXISTS log_migration_step;