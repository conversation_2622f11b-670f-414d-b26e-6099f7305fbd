{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"], "@assets/*": ["assets/*"]}}}