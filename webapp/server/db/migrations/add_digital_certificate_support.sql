-- Migration: Add digital certificate support for PDF signatures
-- Depends on: add_document_signing_tables.sql

SET search_path TO site;

-- Add digital_signature field to document_signatures table
ALTER TABLE document_signatures ADD COLUMN IF NOT EXISTS is_digital_certificate_signature BOOLEAN DEFAULT FALSE;
ALTER TABLE document_signatures ADD COLUMN IF NOT EXISTS digital_signature_data JSONB;

-- Create digital_certificates table
CREATE TABLE IF NOT EXISTS digital_certificates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  certificate_name VARCHAR(255) NOT NULL,
  certificate_data BYTEA NOT NULL,
  private_key BYTEA NOT NULL,
  passphrase VARCHAR(255),
  certificate_type VARCHAR(50) NOT NULL,
  issuer VARCHA<PERSON>(255) NOT NULL,
  subject <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  valid_from TIMESTAMP NOT NULL,
  valid_to TIMESTAMP NOT NULL,
  serial_number VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_digital_certificates_farm_id ON digital_certificates(farm_id);
CREATE INDEX IF NOT EXISTS idx_digital_certificates_user_id ON digital_certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_digital_certificates_is_active ON digital_certificates(is_active);

-- Add column to signable_documents to track if document has digital certificate signature
ALTER TABLE signable_documents ADD COLUMN IF NOT EXISTS has_digital_certificate_signature BOOLEAN DEFAULT FALSE;

-- Add comments
COMMENT ON COLUMN document_signatures.is_digital_certificate_signature IS 'Indicates if the signature uses a digital certificate';
COMMENT ON COLUMN document_signatures.digital_signature_data IS 'JSON data related to the digital signature (certificate info, timestamp, etc.)';
COMMENT ON TABLE digital_certificates IS 'Stores digital certificates for PDF signing';
COMMENT ON COLUMN signable_documents.has_digital_certificate_signature IS 'Indicates if the document has at least one digital certificate signature';