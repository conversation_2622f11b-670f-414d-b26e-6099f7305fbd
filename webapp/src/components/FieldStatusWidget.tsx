import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useFarm } from '../context/FarmContext';
import { API_URL } from '../config';

// Define the Field interface
interface Field {
  id: string;
  farm_id: string;
  name: string;
  size: number | null;
  size_unit: string;
  field_type: string;
  status: string;
  current_crop: string | null;
  soil_type: string | null;
  created_at: string;
  updated_at: string;
}

const FieldStatusWidget: React.FC = () => {
  // Get the selected farm from context
  const { selectedFarm } = useFarm();

  // State for fields data
  const [fields, setFields] = useState<Field[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  // Fetch fields for the selected farm
  useEffect(() => {
    const fetchFields = async () => {
      if (!selectedFarm) {
        setFields([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const response = await axios.get(`${API_URL}/fields/farm/${selectedFarm.id}`);
        setFields(response.data);
      } catch (error) {
        console.error('Error fetching fields:', error);
        setError('Failed to load field data');
        setFields([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFields();
  }, [selectedFarm]);

  // Format field type for display
  const formatFieldType = (type: string) => {
    if (!type) return 'N/A';
    return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ');
  };

  // Format size with unit
  const formatSize = (size: number | null, unit: string) => {
    if (size === null) return 'N/A';
    return `${size} ${unit}`;
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'fallow':
        return 'bg-yellow-100 text-yellow-800';
      case 'planted':
        return 'bg-blue-100 text-blue-800';
      case 'harvested':
        return 'bg-purple-100 text-purple-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get unique statuses for filter
  const uniqueStatuses = [...new Set(fields.map(field => field.status))];

  // Filter fields by status
  const filteredFields = statusFilter 
    ? fields.filter(field => field.status === statusFilter)
    : fields;

  // Calculate field statistics
  const totalFields = fields.length;
  const activeFields = fields.filter(field => field.status === 'active').length;
  const fallowFields = fields.filter(field => field.status === 'fallow').length;
  const plantedFields = fields.filter(field => field.status === 'planted').length;
  const harvestedFields = fields.filter(field => field.status === 'harvested').length;

  // Calculate total acreage (assuming size_unit is standardized)
  const totalAcreage = fields.reduce((sum, field) => {
    if (field.size === null) return sum;
    // Convert to acres if needed
    if (field.size_unit.toLowerCase() === 'hectare') {
      return sum + (field.size * 2.47105); // 1 hectare = 2.47105 acres
    }
    return sum + field.size;
  }, 0);

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      {/* Header with farm name */}
      <div className="mb-4">
        <h2 className="text-lg font-bold text-gray-800">Field Status</h2>
        <p className="text-sm text-gray-600">
          {selectedFarm 
            ? `${selectedFarm.name}`
            : 'Please select a farm to view field status'}
        </p>
      </div>

      {/* Field statistics */}
      {!isLoading && !error && fields.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
          <div className="bg-blue-50 p-2 rounded text-center">
            <div className="text-sm text-blue-800">Total Fields</div>
            <div className="text-xl font-bold text-blue-900">{totalFields}</div>
          </div>
          <div className="bg-green-50 p-2 rounded text-center">
            <div className="text-sm text-green-800">Active</div>
            <div className="text-xl font-bold text-green-900">{activeFields}</div>
          </div>
          <div className="bg-yellow-50 p-2 rounded text-center">
            <div className="text-sm text-yellow-800">Fallow</div>
            <div className="text-xl font-bold text-yellow-900">{fallowFields}</div>
          </div>
          <div className="bg-purple-50 p-2 rounded text-center">
            <div className="text-sm text-purple-800">Total Acres</div>
            <div className="text-xl font-bold text-purple-900">{totalAcreage.toFixed(1)}</div>
          </div>
        </div>
      )}

      {/* Status filter */}
      {!isLoading && !error && fields.length > 0 && (
        <div className="mb-4 flex flex-wrap gap-2">
          <button
            onClick={() => setStatusFilter(null)}
            className={`px-2 py-1 text-xs rounded-full ${
              statusFilter === null ? 'bg-gray-800 text-white' : 'bg-gray-200 text-gray-800'
            }`}
          >
            All
          </button>
          {uniqueStatuses.map(status => (
            <button
              key={status}
              onClick={() => setStatusFilter(status)}
              className={`px-2 py-1 text-xs rounded-full ${
                statusFilter === status 
                  ? 'bg-gray-800 text-white' 
                  : `${getStatusBadgeClass(status)} opacity-70`
              }`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </button>
          ))}
        </div>
      )}

      {/* Fields list */}
      <div className="fields-container">
        {isLoading ? (
          <div className="flex justify-center items-center py-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-500 ml-2">Loading fields...</p>
          </div>
        ) : error ? (
          <div className="text-red-500 text-center py-4">{error}</div>
        ) : filteredFields.length === 0 ? (
          <div className="text-gray-500 text-center py-4">
            {statusFilter 
              ? `No fields with status "${statusFilter}" found` 
              : 'No fields found for this farm'}
          </div>
        ) : (
          <div className="overflow-hidden rounded-md border border-gray-200">
            <ul className="divide-y divide-gray-200">
              {filteredFields.map(field => (
                <li key={field.id} className="px-3 py-2 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">{field.name}</span>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(field.status)}`}>
                        {field.status.charAt(0).toUpperCase() + field.status.slice(1)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatSize(field.size, field.size_unit)}
                    </div>
                  </div>
                  <div className="mt-1 flex justify-between text-xs text-gray-500">
                    <div>Type: {formatFieldType(field.field_type)}</div>
                    <div>Crop: {field.current_crop || 'None'}</div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default FieldStatusWidget;