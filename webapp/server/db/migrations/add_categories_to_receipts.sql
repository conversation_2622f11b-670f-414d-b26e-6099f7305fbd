-- Migration: Add categories field to receipts table for better organization and tax classification
-- Depends on:

-- Set the search path to the site schema
SET search_path TO site;

-- Add categories column to receipts table
ALTER TABLE site.receipts ADD COLUMN IF NOT EXISTS categories TEXT[];

-- Add index for categories for faster searching
CREATE INDEX IF NOT EXISTS receipts_categories_idx ON site.receipts USING GIN(categories);

-- Add comment to explain the purpose of the categories field
COMMENT ON COLUMN site.receipts.categories IS 'Array of categories for organizing receipts and tax classification';

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'receipts'
        AND column_name = 'categories'
    ) INTO column_exists;

    IF column_exists THEN
        RAISE NOTICE 'The categories column was successfully added to the receipts table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the categories column to the receipts table.';
    END IF;
END $$;