import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Product from './Product.js';
import Supplier from './Supplier.js';
import dotenv from 'dotenv';

dotenv.config();

const PriceComparison = defineModel('PriceComparison', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Product,
      key: 'id'
    }
  },
  product_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of the product being compared'
  },
  product_category: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Category of the product'
  },
  unit: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Unit of measurement (e.g., kg, lb, each)'
  },
  comparison_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Date when the price comparison was made'
  },
  price_data: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'JSON array of price data from different suppliers'
  },
  best_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'The best price found during comparison'
  },
  best_price_supplier_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Supplier,
      key: 'id'
    },
    comment: 'The supplier offering the best price'
  },
  price_range: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'The difference between highest and lowest price'
  },
  average_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'The average price across all suppliers'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'price_comparisons',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default PriceComparison;
