import React, {useState, useEffect, useRef} from 'react';
import axios from 'axios';
import {API_URL} from '../../config';
import {useAuth} from '../../context/AuthContext';
import TaxPayment from "../../../server/models/TaxPayment";

interface TaxDocument {
    id: string;
    title: string;
    fileName?: string;
    status: string;
}

interface TaxPayment {
    id?: string;
    paymentDate: string;
    amount: number;
    paymentMethod: string;
    paymentReference?: string;
    taxYear: number;
    taxPeriod: string;
    taxType: string;
    description?: string;
    receiptDocumentId?: string;
    receiptDocument?: TaxDocument;
}

interface TaxPaymentFormProps {
    farmId: string,
    paymentId?: string,
    onSave?: (payment: TaxPayment, receiptFile?: File) => void,
    onCancel?: () => void,
    onPaymentAdded?: () => Promise<void>,
    year?: number
}

const TaxPaymentForm: React.FC<TaxPaymentFormProps> = ({
                                                           farmId,
                                                           paymentId,
                                                           onSave,
                                                           onCancel,
                                                           onPaymentAdded,
                                                           year
                                                       }) => {
    const {user, token} = useAuth();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [payment, setPayment] = useState<TaxPayment>({
        paymentDate: new Date().toISOString().split('T')[0],
        amount: 0,
        paymentMethod: 'electronic',
        paymentReference: '',
        taxYear: new Date().getFullYear(),
        taxPeriod: getCurrentTaxPeriod(),
        taxType: 'income',
        description: ''
    });

    const [receiptFile, setReceiptFile] = useState<File | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const paymentMethods = [
        {value: 'electronic', label: 'Electronic Payment'},
        {value: 'check', label: 'Check'},
        {value: 'credit_card', label: 'Credit Card'},
        {value: 'other', label: 'Other'}
    ];

    const taxPeriods = [
        {value: 'q1', label: 'Q1 (Jan-Mar)'},
        {value: 'q2', label: 'Q2 (Apr-Jun)'},
        {value: 'q3', label: 'Q3 (Jul-Sep)'},
        {value: 'q4', label: 'Q4 (Oct-Dec)'},
        {value: 'annual', label: 'Annual'}
    ];

    const taxTypes = [
        {value: 'income', label: 'Income Tax'},
        {value: 'property', label: 'Property Tax'},
        {value: 'payroll', label: 'Payroll Tax'},
        {value: 'sales', label: 'Sales Tax'},
        {value: 'other', label: 'Other'}
    ];

    // Helper function to determine current tax period based on date
    function getCurrentTaxPeriod(): string {
        const month = new Date().getMonth() + 1; // getMonth() is 0-indexed
        if (month <= 3) return 'q1';
        if (month <= 6) return 'q2';
        if (month <= 9) return 'q3';
        return 'q4';
    }

    // Fetch tax payment if editing
    useEffect(() => {
        const fetchPayment = async () => {
            if (!paymentId) return;

            setLoading(true);
            setError(null);

            try {
                const response = await axios.get(`${API_URL}/api/tax/tax-payments/${paymentId}`, {
                    headers: {Authorization: `Bearer ${token}`}
                });

                if (response.data && response.data.taxPayment) {
                    const fetchedPayment = response.data.taxPayment;
                    setPayment({
                        id: fetchedPayment.id,
                        paymentDate: new Date(fetchedPayment.paymentDate).toISOString().split('T')[0],
                        amount: fetchedPayment.amount,
                        paymentMethod: fetchedPayment.paymentMethod,
                        paymentReference: fetchedPayment.paymentReference || '',
                        taxYear: fetchedPayment.taxYear,
                        taxPeriod: fetchedPayment.taxPeriod,
                        taxType: fetchedPayment.taxType,
                        description: fetchedPayment.description || '',
                        receiptDocumentId: fetchedPayment.receiptDocumentId,
                        receiptDocument: fetchedPayment.receiptDocument
                    });
                } else {
                    throw new Error('Invalid response format from server');
                }
            } catch (err) {
                console.error('Error fetching tax payment:', err);
                setError('Failed to load tax payment. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (paymentId) {
            fetchPayment();
        }
    }, [paymentId, user, token]);

    // Handle form input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const {name, value} = e.target;

        if (name === 'amount') {
            setPayment(prev => ({...prev, [name]: parseFloat(value) || 0}));
        } else if (name === 'taxYear') {
            setPayment(prev => ({...prev, [name]: parseInt(value) || new Date().getFullYear()}));
        } else {
            setPayment(prev => ({...prev, [name]: value}));
        }
    };

    // Handle file input changes
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            setReceiptFile(e.target.files[0]);
        }
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!payment.paymentDate || payment.amount <= 0 || !payment.paymentMethod || !payment.taxPeriod || !payment.taxType) {
            setError('Please fill in all required fields with valid values.');
            return;
        }

        if (onSave) {
            // Use the provided onSave callback
            onSave(payment, receiptFile || undefined);
        } else if (onPaymentAdded) {
            // If onPaymentAdded is provided, handle the API call ourselves
            setLoading(true);
            setError(null);

            try {
                const formData = new FormData();
                formData.append('payment', JSON.stringify({
                    ...payment,
                    farmId,
                    taxYear: payment.taxYear || year || new Date().getFullYear()
                }));

                if (receiptFile) {
                    formData.append('receipt', receiptFile);
                }

                await axios.post(`${API_URL}/api/tax/farms/${farmId}/tax-payments`, formData, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'multipart/form-data'
                    }
                });

                // Reset form
                setPayment({
                    paymentDate: new Date().toISOString().split('T')[0],
                    amount: 0,
                    paymentMethod: 'electronic',
                    paymentReference: '',
                    taxYear: year || new Date().getFullYear(),
                    taxPeriod: getCurrentTaxPeriod(),
                    taxType: 'income',
                    description: ''
                });
                setReceiptFile(null);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }

                // Call the callback to refresh data
                await onPaymentAdded();
            } catch (err: any) {
                console.error('Error saving tax payment:', err);
                setError(err.response?.data?.message || 'Failed to save tax payment. Please try again later.');
            } finally {
                setLoading(false);
            }
        }
    };

    // Format file size for display
    const formatFileSize = (bytes?: number): string => {
        if (!bytes) return 'N/A';

        if (bytes < 1024) return bytes + ' bytes';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    };

    // Format currency for display
    const formatCurrency = (amount: number): string => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {paymentId ? 'Edit Tax Payment' : 'Record Tax Payment'}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    {paymentId
                        ? 'Update the details of this tax payment.'
                        : 'Record a new tax payment for your farm business.'}
                </p>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-6 mb-4"
                     role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <form onSubmit={handleSubmit} className="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Payment Date */}
                    <div>
                        <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700 mb-1">
                            Payment Date <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="date"
                            id="paymentDate"
                            name="paymentDate"
                            value={payment.paymentDate}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Amount */}
                    <div>
                        <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                            Amount ($) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="amount"
                            name="amount"
                            value={payment.amount}
                            onChange={handleChange}
                            min="0.01"
                            step="0.01"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Payment Method */}
                    <div>
                        <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
                            Payment Method <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="paymentMethod"
                            name="paymentMethod"
                            value={payment.paymentMethod}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {paymentMethods.map(method => (
                                <option key={method.value} value={method.value}>
                                    {method.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Payment Reference */}
                    <div>
                        <label htmlFor="paymentReference" className="block text-sm font-medium text-gray-700 mb-1">
                            Payment Reference
                        </label>
                        <input
                            type="text"
                            id="paymentReference"
                            name="paymentReference"
                            value={payment.paymentReference}
                            onChange={handleChange}
                            placeholder="Check #, Confirmation #, etc."
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        />
                    </div>

                    {/* Tax Year */}
                    <div>
                        <label htmlFor="taxYear" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Year <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxYear"
                            name="taxYear"
                            value={payment.taxYear}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {[...Array(5)].map((_, i) => {
                                const year = new Date().getFullYear() - 2 + i;
                                return (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                );
                            })}
                        </select>
                    </div>

                    {/* Tax Period */}
                    <div>
                        <label htmlFor="taxPeriod" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Period <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxPeriod"
                            name="taxPeriod"
                            value={payment.taxPeriod}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {taxPeriods.map(period => (
                                <option key={period.value} value={period.value}>
                                    {period.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Tax Type */}
                    <div>
                        <label htmlFor="taxType" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Type <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxType"
                            name="taxType"
                            value={payment.taxType}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {taxTypes.map(type => (
                                <option key={type.value} value={type.value}>
                                    {type.label}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                {/* Description */}
                <div className="mt-6">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                    </label>
                    <textarea
                        id="description"
                        name="description"
                        value={payment.description}
                        onChange={handleChange}
                        rows={3}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    ></textarea>
                </div>

                {/* Receipt Upload */}
                <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Receipt
                    </label>

                    {payment.receiptDocument ? (
                        <div className="mt-2 flex items-center space-x-2">
                            <div className="flex-1 bg-gray-100 rounded-md p-3">
                                <p className="text-sm font-medium text-gray-900">{payment.receiptDocument.title}</p>
                                {payment.receiptDocument.fileName && (
                                    <p className="text-xs text-gray-500">{payment.receiptDocument.fileName}</p>
                                )}
                            </div>
                            <button
                                type="button"
                                onClick={() => fileInputRef.current?.click()}
                                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                            >
                                Replace
                            </button>
                        </div>
                    ) : (
                        <div
                            className="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div className="space-y-1 text-center">
                                <svg
                                    className="mx-auto h-12 w-12 text-gray-400"
                                    stroke="currentColor"
                                    fill="none"
                                    viewBox="0 0 48 48"
                                    aria-hidden="true"
                                >
                                    <path
                                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                        strokeWidth={2}
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                                <div className="flex text-sm text-gray-600">
                                    <label
                                        htmlFor="file-upload"
                                        className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                                    >
                                        <span>Upload a receipt</span>
                                        <input
                                            id="file-upload"
                                            name="file-upload"
                                            type="file"
                                            className="sr-only"
                                            ref={fileInputRef}
                                            onChange={handleFileChange}
                                            accept=".pdf,.jpg,.jpeg,.png"
                                        />
                                    </label>
                                    <p className="pl-1">or drag and drop</p>
                                </div>
                                <p className="text-xs text-gray-500">PDF or image files up to 10MB</p>
                            </div>
                        </div>
                    )}
                    <input
                        type="file"
                        className="hidden"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept=".pdf,.jpg,.jpeg,.png"
                    />
                    {receiptFile && (
                        <div className="mt-2 text-sm text-gray-500">
                            Selected file: {receiptFile.name} ({formatFileSize(receiptFile.size)})
                        </div>
                    )}
                </div>

                {/* Form Actions */}
                <div className="mt-8 flex justify-end space-x-3">
                    {onCancel && (
                        <button
                            type="button"
                            onClick={onCancel}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                            Cancel
                        </button>
                    )}
                    <button
                        type="submit"
                        disabled={loading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                            loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                    >
                        {loading ? 'Saving...' : paymentId ? 'Update Payment' : 'Record Payment'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default TaxPaymentForm;
