/**
 * Development utility for debugging React errors
 * This helps convert minified React errors to readable messages
 */

// React error URL for decoding minified errors
const REACT_ERROR_DECODER_URL = 'https://reactjs.org/docs/error-decoder.html';

/**
 * Enhanced console logging for React errors in development
 */
export function setupReactErrorDebugging(): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // Override console.error to catch React errors
  const originalConsoleError = console.error;
  
  console.error = (...args: any[]) => {
    const errorMessage = args.join(' ');
    
    // Check for minified React errors
    const minifiedErrorMatch = errorMessage.match(/Minified React error #(\d+);/);
    if (minifiedErrorMatch) {
      const errorCode = minifiedErrorMatch[1];
      console.group('🚨 Minified React Error Detected');
      console.error('Original Error:', errorMessage);
      console.log('📋 Error Code:', errorCode);
      console.log('🔗 Decode at:', `${REACT_ERROR_DECODER_URL}?invariant=${errorCode}`);
      console.log('💡 This error occurred because React is running in production mode.');
      console.log('💡 To see the full error message, run the app in development mode.');
      console.groupEnd();
    }
    
    // Check for common React 19 issues
    if (errorMessage.includes('Cannot read properties of undefined')) {
      console.group('⚠️ Potential React 19 Issue');
      console.error('Original Error:', errorMessage);
      console.log('💡 This might be related to React 19 changes in ref handling or component lifecycle.');
      console.log('💡 Check that all refs are properly initialized and components are mounted.');
      console.groupEnd();
    }
    
    // Call original console.error
    originalConsoleError.apply(console, args);
  };

  // Set up unhandled error catching
  window.addEventListener('error', (event) => {
    const error = event.error;
    if (error && error.message) {
      console.group('🚨 Unhandled JavaScript Error');
      console.error('Error:', error);
      console.log('File:', event.filename);
      console.log('Line:', event.lineno);
      console.log('Column:', event.colno);
      
      // Check if it's a React-related error
      if (error.stack && error.stack.includes('react')) {
        console.log('🔍 This appears to be a React-related error');
        console.log('💡 Check component rendering, props, and state management');
      }
      
      console.groupEnd();
    }
  });

  // Set up unhandled promise rejection catching
  window.addEventListener('unhandledrejection', (event) => {
    console.group('🚨 Unhandled Promise Rejection');
    console.error('Reason:', event.reason);
    
    if (event.reason && typeof event.reason === 'object' && event.reason.message) {
      const message = event.reason.message;
      if (message.includes('react') || message.includes('React')) {
        console.log('🔍 This appears to be a React-related promise rejection');
        console.log('💡 Check async operations in components and useEffect hooks');
      }
    }
    
    console.groupEnd();
  });

  console.log('🔧 React error debugging enabled for development mode');
}

/**
 * Log component render information for debugging
 */
export function logComponentRender(componentName: string, props?: any, state?: any): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.group(`🔄 ${componentName} Render`);
  
  if (props) {
    console.log('Props:', props);
  }
  
  if (state) {
    console.log('State:', state);
  }
  
  console.log('Timestamp:', new Date().toISOString());
  console.groupEnd();
}

/**
 * Log component lifecycle events
 */
export function logComponentLifecycle(componentName: string, event: string, data?: any): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.log(`🔄 ${componentName} - ${event}`, data || '');
}

/**
 * Check for potential memory leaks in React components
 */
export function checkForMemoryLeaks(): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // Check for common memory leak patterns
  const checkInterval = setInterval(() => {
    // Check for excessive event listeners
    const eventListenerCount = (window as any)._eventListenerCount || 0;
    if (eventListenerCount > 100) {
      console.warn('⚠️ High number of event listeners detected:', eventListenerCount);
      console.log('💡 Check for event listeners that are not being cleaned up');
    }

    // Check for excessive timers
    const timerCount = (window as any)._timerCount || 0;
    if (timerCount > 50) {
      console.warn('⚠️ High number of active timers detected:', timerCount);
      console.log('💡 Check for timers that are not being cleared');
    }
  }, 10000); // Check every 10 seconds

  // Clean up the check interval after 5 minutes
  setTimeout(() => {
    clearInterval(checkInterval);
  }, 300000);
}

/**
 * Performance monitoring for React components
 */
export function monitorComponentPerformance(componentName: string, renderTime: number): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  if (renderTime > 100) {
    console.warn(`⚠️ Slow render detected in ${componentName}: ${renderTime}ms`);
    console.log('💡 Consider optimizing this component with React.memo, useMemo, or useCallback');
  }
}

/**
 * Debug React Suspense issues
 */
export function debugSuspense(componentName: string, isLoading: boolean): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  if (isLoading) {
    console.log(`⏳ ${componentName} is suspended (loading)`);
  } else {
    console.log(`✅ ${componentName} suspense resolved`);
  }
}

/**
 * Initialize all debugging utilities
 */
export function initializeReactDebugging(): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  setupReactErrorDebugging();
  checkForMemoryLeaks();
  
  console.log('🔧 React debugging utilities initialized');
  console.log('💡 Use logComponentRender(), logComponentLifecycle(), etc. for detailed debugging');
}
