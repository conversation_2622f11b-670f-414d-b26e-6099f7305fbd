import { useState, useEffect, useContext } from 'react';
import { use<PERSON>arams, Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { Vet, getVetById, deleteVet } from '../../services/vetService';

const VetDetail = () => {
  const { vetId } = useParams<{ vetId: string }>();
  const [vet, setVet] = useState<Vet | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const isAdmin = user?.is_global_admin === true;

  useEffect(() => {
    const fetchVet = async () => {
      if (!vetId) return;

      setLoading(true);
      setError(null);

      try {
        const vetData = await getVetById(vetId);
        setVet(vetData);
      } catch (err: any) {
        console.error('Error fetching vet details:', err);
        setError('Failed to load vet details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchVet();
  }, [vetId]);

  const handleDelete = async () => {
    if (!vet) return;

    // Only admins can delete global vets
    if (vet.is_global && !isAdmin) {
      alert('Only administrators can delete global vets.');
      return;
    }

    if (!window.confirm('Are you sure you want to delete this vet?')) {
      return;
    }

    try {
      await deleteVet(vet.id);
      navigate('/vets');
    } catch (err: any) {
      console.error('Error deleting vet:', err);
      setError('Failed to delete vet. Please try again later.');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Vet Details</h1>
        <div className="flex space-x-2">
          <Link
            to="/vets"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to List
          </Link>
          {vet && (!vet.is_global || isAdmin) && (
            <>
              <Link
                to={`/vets/${vetId}/edit`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Edit
              </Link>
              <button
                onClick={handleDelete}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading vet details...</p>
        </div>
      ) : vet ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900">{vet.name}</h3>
              {vet.specialization && (
                <p className="mt-1 max-w-2xl text-sm text-gray-500">Specialization: {vet.specialization}</p>
              )}
            </div>
            {vet.is_global && (
              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                Global Database
              </span>
            )}
          </div>
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Contact Information</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {vet.phone && <div>Phone: {vet.phone}</div>}
                  {vet.email && <div>Email: {vet.email}</div>}
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Location</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {vet.address && <div>{vet.address}</div>}
                  {(vet.city || vet.state || vet.zip_code) && (
                    <div>
                      {[vet.city, vet.state, vet.zip_code].filter(Boolean).join(', ')}
                    </div>
                  )}
                </dd>
              </div>
              {vet.license_number && (
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">License Number</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {vet.license_number}
                  </dd>
                </div>
              )}
              {vet.notes && (
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Notes</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {vet.notes}
                  </dd>
                </div>
              )}
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Added On</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {new Date(vet.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {new Date(vet.updated_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">Vet not found.</p>
          <Link
            to="/vets"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Vets List
          </Link>
        </div>
      )}
    </Layout>
  );
};

export default VetDetail;
