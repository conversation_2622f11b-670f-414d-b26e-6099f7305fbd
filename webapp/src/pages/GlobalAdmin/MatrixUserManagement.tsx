import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface MatrixUser {
  name: string;
  user_id: string;
  displayname: string;
  avatar_url?: string;
  is_admin: boolean;
  deactivated: boolean;
  creation_ts: number;
}

const MatrixUserManagement: React.FC = () => {
  const [users, setUsers] = useState<MatrixUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<MatrixUser | null>(null);
  const [syncing, setSyncing] = useState<boolean>(false);
  const [syncResult, setSyncResult] = useState<any>(null);
  const [formData, setFormData] = useState({
    username: '',
    displayname: '',
    password: '',
    admin: false
  });
  const [pagination, setPagination] = useState({
    from: 0,
    limit: 50,
    total: 0
  });

  useEffect(() => {
    fetchUsers();
  }, [pagination.from, pagination.limit]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/admin/matrix/users?from=${pagination.from}&limit=${pagination.limit}`);

      if (response.data && response.data.users) {
        setUsers(response.data.users);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0
        }));
      } else {
        setUsers([]);
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching Matrix users:', err);
      setError(err.response?.data?.error || 'Failed to load Matrix users');
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    setFormData({
      username: '',
      displayname: '',
      password: '',
      admin: false
    });
    setShowAddModal(true);
  };

  const handleEditUser = (user: MatrixUser) => {
    setCurrentUser(user);
    setFormData({
      username: user.name,
      displayname: user.displayname || '',
      password: '',
      admin: user.is_admin
    });
    setShowEditModal(true);
  };

  const handleDeleteUser = (user: MatrixUser) => {
    setCurrentUser(user);
    setShowDeleteModal(true);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);

      const response = await axios.post(`${API_URL}/admin/matrix/users`, {
        username: formData.username,
        password: formData.password,
        displayname: formData.displayname,
        admin: formData.admin
      });

      // Refresh the user list
      fetchUsers();
      setShowAddModal(false);
    } catch (err: any) {
      console.error('Error adding Matrix user:', err);
      setError(err.response?.data?.error || 'Failed to add Matrix user');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;

    try {
      setError(null);

      // Only include password in the request if it's not empty
      const userData: any = {
        displayname: formData.displayname,
        admin: formData.admin
      };

      if (formData.password) {
        userData.password = formData.password;
      }

      // Extract user ID from the Matrix ID
      const userId = currentUser.user_id.substring(1); // Remove the @ prefix

      const response = await axios.put(`${API_URL}/admin/matrix/users/${userId}`, userData);

      // Refresh the user list
      fetchUsers();
      setShowEditModal(false);
    } catch (err: any) {
      console.error('Error updating Matrix user:', err);
      setError(err.response?.data?.error || 'Failed to update Matrix user');
    }
  };

  const confirmDeleteUser = async () => {
    if (!currentUser) return;

    try {
      setError(null);

      // Extract user ID from the Matrix ID
      const userId = currentUser.user_id.substring(1); // Remove the @ prefix

      await axios.delete(`${API_URL}/admin/matrix/users/${userId}`);

      // Refresh the user list
      fetchUsers();
      setShowDeleteModal(false);
    } catch (err: any) {
      console.error('Error deleting Matrix user:', err);
      setError(err.response?.data?.error || 'Failed to delete Matrix user');
    }
  };

  const handleNextPage = () => {
    if (pagination.from + pagination.limit < pagination.total) {
      setPagination(prev => ({
        ...prev,
        from: prev.from + prev.limit
      }));
    }
  };

  const handlePrevPage = () => {
    if (pagination.from > 0) {
      setPagination(prev => ({
        ...prev,
        from: Math.max(0, prev.from - prev.limit)
      }));
    }
  };

  const handleSyncUsers = async () => {
    try {
      setSyncing(true);
      setError(null);
      setSyncResult(null);

      const response = await axios.post(`${API_URL}/admin/matrix/sync-users`);

      console.log('Sync response:', response.data);
      setSyncResult(response.data.results);

      // Refresh the user list after sync
      fetchUsers();
    } catch (err: any) {
      console.error('Error syncing users with Matrix:', err);
      setError(err.response?.data?.error || 'Failed to sync users with Matrix');
    } finally {
      setSyncing(false);
    }
  };

  if (loading && users.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Matrix Users</h2>
        <div className="flex space-x-2">
          <button 
            onClick={handleSyncUsers}
            disabled={syncing}
            className={`bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center ${
              syncing ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {syncing ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Syncing...
              </>
            ) : (
              'Auto Sync Users'
            )}
          </button>
          <button 
            onClick={handleAddUser}
            className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
          >
            Add Matrix User
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      {syncResult && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Sync Results:</strong>
          <div className="mt-2">
            <p>Total users processed: {syncResult.total}</p>
            <p>Users created in Matrix: {syncResult.created}</p>
            <p>Users updated in Matrix: {syncResult.updated}</p>
            {syncResult.errors && syncResult.errors.length > 0 && (
              <div className="mt-2">
                <p className="font-bold">Errors:</p>
                <ul className="list-disc pl-5">
                  {syncResult.errors.map((error: any, index: number) => (
                    <li key={index}>
                      {error.user}: {error.error}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Display Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Admin
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {users.map(user => (
              <tr key={user.user_id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{user.user_id}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">{user.displayname || user.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    user.is_admin ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {user.is_admin ? 'Admin' : 'User'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    user.deactivated ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {user.deactivated ? 'Deactivated' : 'Active'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(user.creation_ts).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => handleEditUser(user)}
                    className="text-primary-600 hover:text-primary-900 mr-4"
                  >
                    Edit
                  </button>
                  <button 
                    onClick={() => handleDeleteUser(user)}
                    className="text-red-600 hover:text-red-900"
                  >
                    {user.deactivated ? 'Erase' : 'Deactivate'}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-700">
          Showing <span className="font-medium">{pagination.from + 1}</span> to{' '}
          <span className="font-medium">{Math.min(pagination.from + pagination.limit, pagination.total)}</span> of{' '}
          <span className="font-medium">{pagination.total}</span> users
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handlePrevPage}
            disabled={pagination.from === 0}
            className={`px-3 py-1 rounded ${
              pagination.from === 0
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            Previous
          </button>
          <button
            onClick={handleNextPage}
            disabled={pagination.from + pagination.limit >= pagination.total}
            className={`px-3 py-1 rounded ${
              pagination.from + pagination.limit >= pagination.total
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add User Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Add New Matrix User</h3>
              <button 
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitAdd} className="p-5">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="username">
                  Username
                </label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="displayname">
                  Display Name
                </label>
                <input
                  type="text"
                  id="displayname"
                  name="displayname"
                  value={formData.displayname}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="password">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="admin"
                    checked={formData.admin}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Matrix Admin</span>
                </label>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && currentUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Edit Matrix User</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-5">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-username">
                  Username
                </label>
                <input
                  type="text"
                  id="edit-username"
                  name="username"
                  value={formData.username}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100"
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">Username cannot be changed</p>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-displayname">
                  Display Name
                </label>
                <input
                  type="text"
                  id="edit-displayname"
                  name="displayname"
                  value={formData.displayname}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-password">
                  Password (leave blank to keep current)
                </label>
                <input
                  type="password"
                  id="edit-password"
                  name="password"
                  value={formData.password}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="admin"
                    checked={formData.admin}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Matrix Admin</span>
                </label>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete User Confirmation Modal */}
      {showDeleteModal && currentUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm {currentUser.deactivated ? 'Erase' : 'Deactivate'}</h3>
              <button 
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="text-gray-700 mb-4">
                Are you sure you want to {currentUser.deactivated ? 'erase' : 'deactivate'} the user <span className="font-semibold">{currentUser.displayname || currentUser.name}</span> ({currentUser.user_id})?
              </p>
              {currentUser.deactivated ? (
                <p className="text-red-600 mb-4">
                  This will permanently erase all user data and cannot be undone.
                </p>
              ) : (
                <p className="text-yellow-600 mb-4">
                  This will deactivate the user account. The user will no longer be able to log in, but their data will be preserved.
                </p>
              )}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={confirmDeleteUser}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                >
                  {currentUser.deactivated ? 'Erase User' : 'Deactivate User'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MatrixUserManagement;
