import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface Farm {
  id: string;
  name: string;
  stripe_customer_id: string | null;
  billing_email: string | null;
  created_at: string;
}

interface SyncStatus {
  farmId: string;
  inSync: boolean;
  message: string;
}

const StripeDebugger: React.FC = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [syncStatuses, setSyncStatuses] = useState<Record<string, SyncStatus>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [syncingAll, setSyncingAll] = useState<boolean>(false);
  const [syncingSingle, setSyncingSingle] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch farms
      const farmsResponse = await axios.get(`${API_URL}/admin/farms`);
      const farmsData = Array.isArray(farmsResponse.data.farms) ? farmsResponse.data.farms : [];
      setFarms(farmsData);

      // Fetch sync status for all farms
      const syncStatusResponse = await axios.get(`${API_URL}/admin/stripe/sync-status`);
      setSyncStatuses(syncStatusResponse.data.statuses || {});

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.error || 'Failed to load data');
      setLoading(false);
    }
  };

  const handleSyncAll = async () => {
    try {
      setSyncingAll(true);
      setError(null);
      setSuccessMessage(null);

      const response = await axios.post(`${API_URL}/admin/stripe/sync-all`);
      
      // Update the sync statuses with the new data
      setSyncStatuses(response.data.statuses || {});
      setSuccessMessage('All farms have been synced with Stripe successfully.');
      
      // Refresh the farms data to get updated stripe_customer_id values
      const farmsResponse = await axios.get(`${API_URL}/admin/farms`);
      setFarms(Array.isArray(farmsResponse.data.farms) ? farmsResponse.data.farms : []);
      
      setSyncingAll(false);
    } catch (err: any) {
      console.error('Error syncing all farms:', err);
      setError(err.response?.data?.error || 'Failed to sync farms with Stripe');
      setSyncingAll(false);
    }
  };

  const handleSyncFarm = async (farmId: string) => {
    try {
      setSyncingSingle(farmId);
      setError(null);
      setSuccessMessage(null);

      const response = await axios.post(`${API_URL}/admin/stripe/sync/${farmId}`);
      
      // Update the sync status for this farm
      setSyncStatuses(prev => ({
        ...prev,
        [farmId]: response.data.status
      }));
      
      // Refresh the farms data to get updated stripe_customer_id value
      const farmsResponse = await axios.get(`${API_URL}/admin/farms`);
      setFarms(Array.isArray(farmsResponse.data.farms) ? farmsResponse.data.farms : []);
      
      setSuccessMessage(`Farm "${farms.find(f => f.id === farmId)?.name}" has been synced with Stripe successfully.`);
      setSyncingSingle(null);
    } catch (err: any) {
      console.error('Error syncing farm:', err);
      setError(err.response?.data?.error || 'Failed to sync farm with Stripe');
      setSyncingSingle(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Stripe Integration Debugger</h2>
        <button 
          onClick={handleSyncAll}
          disabled={syncingAll}
          className={`${
            syncingAll 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-primary-600 hover:bg-primary-700'
          } text-white font-bold py-2 px-4 rounded flex items-center`}
        >
          {syncingAll && (
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          )}
          Sync All Farms with Stripe
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Success!</strong>
          <span className="block sm:inline"> {successMessage}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Farm Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stripe Customer ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Billing Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sync Status
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {farms.length > 0 ? farms.map(farm => (
              <tr key={farm.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{farm.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {farm.stripe_customer_id || 'Not connected'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {farm.billing_email || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {syncStatuses[farm.id] ? (
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      syncStatuses[farm.id].inSync 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {syncStatuses[farm.id].inSync ? 'In Sync' : 'Out of Sync'}
                    </span>
                  ) : (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                      Unknown
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => handleSyncFarm(farm.id)}
                    disabled={syncingSingle === farm.id}
                    className={`${
                      syncingSingle === farm.id 
                        ? 'text-gray-400 cursor-not-allowed' 
                        : 'text-primary-600 hover:text-primary-900'
                    } flex items-center justify-end`}
                  >
                    {syncingSingle === farm.id && (
                      <svg className="animate-spin -ml-1 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    Sync with Stripe
                  </button>
                </td>
              </tr>
            )) : (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center">
                  No farms available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-6 bg-white shadow-md rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">About Stripe Integration</h3>
        <p className="text-gray-700 mb-2">
          This tool helps you ensure that all farms in the NxtAcre system are properly connected to Stripe for payment processing.
        </p>
        <p className="text-gray-700 mb-2">
          When a farm is "In Sync", it means:
        </p>
        <ul className="list-disc pl-5 mb-4 text-gray-700">
          <li>The farm has a valid Stripe customer ID</li>
          <li>The Stripe customer exists in Stripe</li>
          <li>The farm's billing information matches the Stripe customer's information</li>
        </ul>
        <p className="text-gray-700">
          If a farm is "Out of Sync", click the "Sync with Stripe" button to create or update the Stripe customer for that farm.
        </p>
      </div>
    </div>
  );
};

export default StripeDebugger;