import express from 'express';
import {
  getFarmInvoices,
  getInvoiceById,
  getInvoiceWithAuthCode,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  cancelInvoice,
  respondToInvoiceQuestion,
  getNextInvoiceNumber,
  sendInvoiceToCustomer,
  sendInvoiceReminder,
  getInvoiceEmailHistory,
  generateInvoicePdf,
  uploadInvoiceDocument,
  getInvoiceDocument,
  deleteInvoiceDocument,
  trackInvoiceEmailView
} from '../controllers/invoiceController.js';
import { authenticate } from '../middleware/authMiddleware.js';
import { uploadInvoiceDocument as uploadInvoiceDocumentMiddleware, handleFileUploadErrors } from '../middleware/fileUploadMiddleware.js';

const router = express.Router();

// Get all invoices for a farm
router.get('/farm/:farmId', authenticate, getFarmInvoices);

// Get the next invoice number for a farm
router.get('/next-number/:farmId', authenticate, getNextInvoiceNumber);

// Get a single invoice by ID (authenticated)
router.get('/:invoiceId', authenticate, getInvoiceById);

// Get a single invoice by ID with auth code (unauthenticated access)
router.get('/:invoiceId/public', getInvoiceWithAuthCode);

// Track when an invoice email is viewed (unauthenticated access)
router.get('/track/:trackingId', trackInvoiceEmailView);

// Get the email history for an invoice
router.get('/:invoiceId/emails', authenticate, getInvoiceEmailHistory);

// Generate a PDF for an invoice
router.get('/:invoiceId/pdf', authenticate, generateInvoicePdf);

// Download a PDF for an invoice
router.get('/:invoiceId/download', authenticate, generateInvoicePdf);

// Upload a document for an invoice
router.post('/:invoiceId/document', authenticate, uploadInvoiceDocumentMiddleware.single('document'), handleFileUploadErrors, uploadInvoiceDocument);

// Get the document for an invoice
router.get('/:invoiceId/document', authenticate, getInvoiceDocument);

// Delete the document for an invoice
router.delete('/:invoiceId/document', authenticate, deleteInvoiceDocument);

// Create a new invoice
router.post('/', authenticate, createInvoice);

// Send an invoice to a customer
router.post('/:invoiceId/send', authenticate, sendInvoiceToCustomer);

// Send a reminder for an invoice
router.post('/:invoiceId/remind', authenticate, sendInvoiceReminder);

// Update an invoice
router.put('/:invoiceId', authenticate, updateInvoice);

// Cancel an invoice
router.post('/:invoiceId/cancel', authenticate, cancelInvoice);

// Delete an invoice
router.delete('/:invoiceId', authenticate, deleteInvoice);

// Respond to an invoice question
router.post('/questions/:questionId/respond', authenticate, respondToInvoiceQuestion);

export default router;
