import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { useAuth } from '../../context/AuthContext';
import LoadingSpinner from '../LoadingSpinner';

interface CurrentData {
  income: number;
  expenses: number;
  deductions: number;
  taxPayments: number;
}

interface AnnualForecast {
  income: number;
  expenses: number;
  deductions: number;
  taxableIncome: number;
  taxLiability: number;
  remainingTaxLiability: number;
}

interface QuarterlyEstimates {
  remainingQuarters: number[];
  paymentPerQuarter: number;
}

interface PreviousYearData {
  income: number;
  expenses: number;
  deductions: number;
}

interface TaxForecastData {
  year: number;
  currentData: CurrentData;
  annualForecast: AnnualForecast;
  quarterlyEstimates: QuarterlyEstimates;
  previousYearData: PreviousYearData;
}

interface TaxForecastProps {
  farmId: string;
  year?: number;
  onClose?: () => void;
}

const TaxForecast: React.FC<TaxForecastProps> = ({
  farmId,
  year = new Date().getFullYear(),
  onClose
}) => {
  const { user, token } = useAuth();
  const [forecast, setForecast] = useState<TaxForecastData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(year);

  useEffect(() => {
    if (user && farmId) {
      fetchTaxForecast();
    }
  }, [user, farmId, selectedYear]);

  const fetchTaxForecast = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/api/tax/farms/${farmId}/tax-forecast`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year: selectedYear }
      });

      if (response.data && response.data.forecast) {
        setForecast(response.data.forecast);
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Error fetching tax forecast:', err);

      // Handle authentication errors without causing logout
      if (err.response && err.response.status === 401) {
        setError('Authentication error. Please refresh the page and try again.');
      } else {
        setError('Failed to load tax forecast. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedYear(parseInt(e.target.value));
  };

  const getQuarterName = (quarter: number) => {
    switch (quarter) {
      case 1: return 'Q1 (Jan-Mar)';
      case 2: return 'Q2 (Apr-Jun)';
      case 3: return 'Q3 (Jul-Sep)';
      case 4: return 'Q4 (Oct-Dec)';
      default: return `Q${quarter}`;
    }
  };

  const getQuarterDueDate = (quarter: number, year: number) => {
    switch (quarter) {
      case 1: return `April 15, ${year}`;
      case 2: return `June 15, ${year}`;
      case 3: return `September 15, ${year}`;
      case 4: return `January 15, ${year + 1}`;
      default: return '';
    }
  };

  if (loading && !forecast) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">Tax Liability Forecast</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Estimated tax liability and quarterly payment projections
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div>
            <select
              id="year-select"
              name="year-select"
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              value={selectedYear}
              onChange={handleYearChange}
            >
              {[...Array(5)].map((_, i) => {
                const yearOption = new Date().getFullYear() - 2 + i;
                return (
                  <option key={yearOption} value={yearOption}>
                    {yearOption}
                  </option>
                );
              })}
            </select>
          </div>
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Close
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {forecast && (
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          {/* Annual Forecast Summary */}
          <div className="mb-8">
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              Annual Tax Forecast for {forecast.year}
            </h4>
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Forecasted Annual Income</dt>
                  <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(forecast.annualForecast.income)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Forecasted Annual Expenses</dt>
                  <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(forecast.annualForecast.expenses)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Forecasted Deductions</dt>
                  <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(forecast.annualForecast.deductions)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Forecasted Taxable Income</dt>
                  <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(forecast.annualForecast.taxableIncome)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Estimated Tax Liability</dt>
                  <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(forecast.annualForecast.taxLiability)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Remaining Tax Liability</dt>
                  <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(forecast.annualForecast.remainingTaxLiability)}</dd>
                </div>
              </div>
            </div>
          </div>

          {/* Current Year Progress */}
          <div className="mb-8">
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              Current Year Progress
            </h4>
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Current Income</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(forecast.currentData.income)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Current Expenses</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(forecast.currentData.expenses)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Current Deductions</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(forecast.currentData.deductions)}</dd>
                </div>
              </div>
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <dt className="text-sm font-medium text-gray-500 truncate">Tax Payments Made</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(forecast.currentData.taxPayments)}</dd>
                </div>
              </div>
            </div>
          </div>

          {/* Quarterly Estimated Payments */}
          {forecast.quarterlyEstimates.remainingQuarters.length > 0 && (
            <div className="mb-8">
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Quarterly Estimated Payments
              </h4>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quarter
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Due Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Estimated Payment
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {forecast.quarterlyEstimates.remainingQuarters.map((quarter) => (
                      <tr key={quarter}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {getQuarterName(quarter)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {getQuarterDueDate(quarter, forecast.year)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(forecast.quarterlyEstimates.paymentPerQuarter)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Year-over-Year Comparison */}
          <div className="mb-8">
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              Year-over-Year Comparison
            </h4>
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {forecast.year - 1} (Previous Year)
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {forecast.year} (Forecast)
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Change
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Income
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(forecast.previousYearData.income)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(forecast.annualForecast.income)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {forecast.previousYearData.income > 0 
                        ? `${((forecast.annualForecast.income / forecast.previousYearData.income - 1) * 100).toFixed(1)}%` 
                        : 'N/A'}
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Expenses
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(forecast.previousYearData.expenses)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(forecast.annualForecast.expenses)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {forecast.previousYearData.expenses > 0 
                        ? `${((forecast.annualForecast.expenses / forecast.previousYearData.expenses - 1) * 100).toFixed(1)}%` 
                        : 'N/A'}
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Deductions
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(forecast.previousYearData.deductions)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(forecast.annualForecast.deductions)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {forecast.previousYearData.deductions > 0 
                        ? `${((forecast.annualForecast.deductions / forecast.previousYearData.deductions - 1) * 100).toFixed(1)}%` 
                        : 'N/A'}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Tax Planning Tips */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              Tax Planning Tips
            </h4>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Quarterly Estimated Payments</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>Make sure to pay your quarterly estimated taxes on time to avoid penalties. The IRS requires quarterly payments if you expect to owe $1,000 or more in taxes.</p>
                    </div>
                  </div>
                </div>
              </div>
              {forecast.annualForecast.income > forecast.previousYearData.income * 1.1 && (
                <div className="bg-yellow-50 p-4 rounded-md">
                  <div className="flex">
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Income Increase Alert</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>Your income is projected to increase significantly this year. Consider increasing your tax-deductible expenses or contributions to retirement accounts to reduce your tax liability.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {forecast.annualForecast.deductions < forecast.annualForecast.income * 0.1 && (
                <div className="bg-green-50 p-4 rounded-md">
                  <div className="flex">
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Deduction Opportunity</h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>Your deductions are relatively low compared to your income. Consider exploring additional farm-related deductions such as equipment purchases, conservation expenses, or farm improvements.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaxForecast;
