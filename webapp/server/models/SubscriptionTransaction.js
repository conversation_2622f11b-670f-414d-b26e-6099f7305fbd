import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js'; // Using Farm directly instead of Tenant (which is now an alias for Farm)
import SubscriptionPlan from './SubscriptionPlan.js';
import dotenv from 'dotenv';

dotenv.config();

const SubscriptionTransaction = defineModel('SubscriptionTransaction', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  subscription_plan_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: SubscriptionPlan,
      key: 'id'
    }
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  payment_method: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  payment_reference: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  transaction_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  billing_period_start: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  billing_period_end: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'subscription_transactions',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
SubscriptionTransaction.belongsTo(Farm, { foreignKey: 'farm_id', as: 'Farm' });
Farm.hasMany(SubscriptionTransaction, { foreignKey: 'farm_id', as: 'SubscriptionTransactions' });

SubscriptionTransaction.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
SubscriptionPlan.hasMany(SubscriptionTransaction, { foreignKey: 'subscription_plan_id' });

export default SubscriptionTransaction;
