const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Path to the migration file
const migrationFile = path.join(__dirname, '../db/migrations/add_transport_and_receipt_tables.sql');

// Check if the migration file exists
if (!fs.existsSync(migrationFile)) {
  console.error(`Migration file not found: ${migrationFile}`);
  process.exit(1);
}

console.log('Applying transport and receipt tables migration...');

// Run the migration using the run-migration.js script
const runMigrationScript = path.join(__dirname, 'run-migration.js');
const command = `node ${runMigrationScript} add_transport_and_receipt_tables.sql`;

exec(command, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error applying migration: ${error.message}`);
    console.error(stderr);
    process.exit(1);
  }
  
  console.log(stdout);
  console.log('Migration applied successfully!');
  console.log('The transport and receipt management features should now work correctly.');
  console.log('Please refresh the application and try accessing the Transport and Receipts pages again.');
});