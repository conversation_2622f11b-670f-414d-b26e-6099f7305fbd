import React, { useState, useEffect } from 'react';
import { useDashboard, Widget } from '../context/DashboardContext';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';
import { checkPermission } from '../services/rolePermissionService';

interface WidgetSelectorProps {
  onClose: () => void;
}

interface WidgetTemplate {
  type: string;
  title: string;
  description: string;
  defaultSize: { w: number; h: number };
  feature?: string; // The feature this widget is associated with for permission checking
}

const WidgetSelector: React.FC<WidgetSelectorProps> = ({ onClose }) => {
  const { addWidget, dashboardLayout } = useDashboard();
  const { user, token } = useAuth();
  const { currentFarm } = useFarm();
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);
  const [availableWidgets, setAvailableWidgets] = useState<WidgetTemplate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // All widget templates with their associated features
  const allWidgetTemplates: WidgetTemplate[] = [
    {
      type: 'weather',
      title: 'Weather',
      description: 'Display weather information for your farm location',
      defaultSize: { w: 12, h: 4 },
      feature: 'weather' // Everyone has access to weather
    },
    {
      type: 'accounts',
      title: 'Accounts',
      description: 'Display your financial accounts and balances',
      defaultSize: { w: 8, h: 4 },
      feature: 'finances'
    },
    {
      type: 'quick-actions',
      title: 'Quick Actions',
      description: 'Quick access to common actions',
      defaultSize: { w: 4, h: 4 }
      // No feature specified, everyone has access
    },
    {
      type: 'recent-transactions',
      title: 'Recent Transactions',
      description: 'Display your most recent financial transactions',
      defaultSize: { w: 12, h: 4 },
      feature: 'finances'
    },
    {
      type: 'task-status',
      title: 'Task Status',
      description: 'View upcoming and overdue tasks',
      defaultSize: { w: 6, h: 4 },
      feature: 'tasks'
    },
    {
      type: 'inventory-alerts',
      title: 'Inventory Alerts',
      description: 'Monitor low inventory items that need reordering',
      defaultSize: { w: 6, h: 4 },
      feature: 'inventory'
    },
    // Chart widgets
    {
      type: 'financial-summary',
      title: 'Financial Summary Chart',
      description: 'Visualize income and expenses over time',
      defaultSize: { w: 8, h: 6 },
      feature: 'finances'
    },
    {
      type: 'inventory-levels',
      title: 'Inventory Levels Chart',
      description: 'Track inventory levels over time for selected products',
      defaultSize: { w: 8, h: 6 },
      feature: 'inventory'
    },
    {
      type: 'task-completion',
      title: 'Task Completion Chart',
      description: 'View task completion statistics and progress',
      defaultSize: { w: 6, h: 6 },
      feature: 'tasks'
    },
    {
      type: 'weather-trends',
      title: 'Weather Trends Chart',
      description: 'Analyze weather patterns and trends over time',
      defaultSize: { w: 8, h: 6 },
      feature: 'weather'
    }
    // Field Health and Market Prices widgets are now loaded from plugins
  ];

  // Filter widgets based on user permissions
  useEffect(() => {
    const filterWidgetsByPermission = async () => {
      if (!user || !token || !currentFarm) {
        setAvailableWidgets([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Filter widgets based on permissions
        const filteredWidgets = [];

        for (const widget of allWidgetTemplates) {
          // If no feature is specified, everyone has access
          if (!widget.feature) {
            filteredWidgets.push(widget);
            continue;
          }

          // Check if user has permission to view this feature
          try {
            const hasPermission = await checkPermission(
              currentFarm.id,
              user.id,
              widget.feature,
              'view'
            );

            if (hasPermission) {
              filteredWidgets.push(widget);
            }
          } catch (err) {
            console.error(`Error checking permission for ${widget.feature}:`, err);
            // If there's an error checking permission, assume the user has access
            // This is a fallback to ensure widgets aren't hidden incorrectly
            filteredWidgets.push(widget);
          }
        }

        setAvailableWidgets(filteredWidgets);
      } catch (err) {
        console.error('Error filtering widgets by permission:', err);
        // If there's an error, show all widgets as a fallback
        setAvailableWidgets(allWidgetTemplates);
      } finally {
        setLoading(false);
      }
    };

    filterWidgetsByPermission();
  }, [user, token, currentFarm]);

  const handleAddWidget = () => {
    if (!selectedWidget || !dashboardLayout) return;

    const template = availableWidgets.find(t => t.type === selectedWidget);
    if (!template) return;

    // Create a new widget - GridStack will automatically place it in the next available position
    const newWidget: Widget = {
      id: uuidv4(),
      type: template.type,
      title: template.title,
      position: {
        x: 0, // GridStack will determine the actual position
        y: 0, // GridStack will determine the actual position
        w: template.defaultSize.w,
        h: template.defaultSize.h
      }
    };

    // Add widget-specific properties
    if (template.type === 'quick-actions') {
      newWidget.actions = [
        { id: '1', label: 'View Transactions', link: '/transactions' },
        { id: '2', label: 'Manage Invoices', link: '/invoices' },
        { id: '3', label: 'View Customers', link: '/customers' },
        { id: '4', label: 'Manage Farms', link: '/farms' }
      ];
    }

    // Add the widget to the dashboard
    addWidget(newWidget);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">Add Widget</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-3 text-gray-500">Loading available widgets...</span>
          </div>
        ) : availableWidgets.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No widgets available for your permission level.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4 mb-6">
            {availableWidgets.map((template) => (
              <div
                key={template.type}
                className={`border rounded-lg p-4 cursor-pointer ${
                  selectedWidget === template.type
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedWidget(template.type)}
              >
                <h3 className="text-md font-medium text-gray-900 mb-1">{template.title}</h3>
                <p className="text-sm text-gray-500">{template.description}</p>
              </div>
            ))}
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="btn btn-outline"
          >
            Cancel
          </button>
          <button
            onClick={handleAddWidget}
            disabled={!selectedWidget}
            className={`btn ${
              selectedWidget
                ? 'btn-primary'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            Add Widget
          </button>
        </div>
      </div>
    </div>
  );
};

export default WidgetSelector;
