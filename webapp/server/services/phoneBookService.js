import Customer from '../models/Customer.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import logger from '../utils/logger.js';

/**
 * Service for handling phone book integration
 */
class PhoneBookService {
  /**
   * Subscribe a customer to phone book updates
   * @param {string} customerId - The ID of the customer
   * @param {Object} options - Subscription options
   * @param {boolean} options.ios - Whether to sync with iOS phone book
   * @param {boolean} options.android - Whether to sync with Android phone book
   * @returns {Promise<Object>} The updated customer
   */
  async subscribeCustomer(customerId, options = {}) {
    const transaction = await sequelize.transaction();

    try {
      const customer = await Customer.findByPk(customerId);

      if (!customer) {
        await transaction.rollback();
        throw new Error('Customer not found');
      }

      // Update customer subscription settings
      await customer.update({
        phone_book_subscription: true,
        ios_phone_book_sync: options.ios === true,
        android_phone_book_sync: options.android === true,
      }, { transaction });

      // If customer is subscribing to any platform, perform initial sync
      if (options.ios || options.android) {
        await this.syncCustomerToPhoneBook(customer, transaction);
      }

      await transaction.commit();
      return customer;
    } catch (error) {
      await transaction.rollback();
      logger.error('Error subscribing customer to phone book:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe a customer from phone book updates
   * @param {string} customerId - The ID of the customer
   * @returns {Promise<Object>} The updated customer
   */
  async unsubscribeCustomer(customerId) {
    const transaction = await sequelize.transaction();

    try {
      const customer = await Customer.findByPk(customerId);

      if (!customer) {
        await transaction.rollback();
        throw new Error('Customer not found');
      }

      // If customer was previously synced, remove from phone books
      if (customer.phone_book_subscription && 
          (customer.ios_phone_book_sync || customer.android_phone_book_sync)) {
        await this.removeCustomerFromPhoneBook(customer, transaction);
      }

      // Update customer subscription settings
      await customer.update({
        phone_book_subscription: false,
        ios_phone_book_sync: false,
        android_phone_book_sync: false,
        external_phone_book_id: null,
      }, { transaction });

      await transaction.commit();
      return customer;
    } catch (error) {
      await transaction.rollback();
      logger.error('Error unsubscribing customer from phone book:', error);
      throw error;
    }
  }

  /**
   * Sync a customer to phone books
   * @param {Object} customer - The customer to sync
   * @param {Object} transaction - The database transaction
   * @returns {Promise<Object>} The updated customer
   */
  async syncCustomerToPhoneBook(customer, transaction) {
    try {
      // In a real implementation, this would call platform-specific APIs
      // to add or update the contact in the phone book

      let externalId = null;

      // For iOS
      if (customer.ios_phone_book_sync) {
        logger.info(`Syncing customer ${customer.id} to iOS phone book`);

        // Import the iOS Phone Book API client
        const iOSPhoneBookAPI = (await import('./iOSPhoneBookAPI.js')).default;

        // Call the iOS Phone Book API to add/update the contact
        const iosResult = await iOSPhoneBookAPI.addContact({
          name: customer.name,
          contactName: customer.contact_name,
          phone: customer.phone,
          email: customer.email,
          address: customer.address,
          city: customer.city,
          state: customer.state,
          zipCode: customer.zip_code,
          country: customer.country,
          customerId: customer.id,
          farmId: customer.farm_id,
          externalId: customer.external_phone_book_id
        });

        // Store the external ID returned by the API
        if (iosResult.success && iosResult.id) {
          externalId = iosResult.id;
        }
      }

      // For Android
      if (customer.android_phone_book_sync) {
        logger.info(`Syncing customer ${customer.id} to Android phone book`);

        // Import the Android Phone Book API client
        const AndroidPhoneBookAPI = (await import('./AndroidPhoneBookAPI.js')).default;

        // Call the Android Phone Book API to add/update the contact
        const androidResult = await AndroidPhoneBookAPI.addContact({
          name: customer.name,
          contactName: customer.contact_name,
          phone: customer.phone,
          email: customer.email,
          address: customer.address,
          city: customer.city,
          state: customer.state,
          zipCode: customer.zip_code,
          country: customer.country,
          customerId: customer.id,
          farmId: customer.farm_id,
          externalId: customer.external_phone_book_id
        });

        // Store the external ID returned by the API
        if (androidResult.success && androidResult.id) {
          externalId = androidResult.id;
        }
      }

      // Update sync information
      await customer.update({
        phone_book_last_sync: new Date(),
        phone_book_sync_id: customer.phone_book_sync_id || `pb-${Date.now()}`,
        external_phone_book_id: externalId || customer.external_phone_book_id
      }, { transaction: transaction || null });

      return customer;
    } catch (error) {
      logger.error('Error syncing customer to phone book:', error);
      throw error;
    }
  }

  /**
   * Remove a customer from phone books
   * @param {Object} customer - The customer to remove
   * @param {Object} transaction - The database transaction
   * @returns {Promise<boolean>} Success status
   */
  async removeCustomerFromPhoneBook(customer, transaction) {
    try {
      // In a real implementation, this would call platform-specific APIs
      // to remove the contact from the phone book

      // For iOS
      if (customer.ios_phone_book_sync && customer.external_phone_book_id) {
        logger.info(`Removing customer ${customer.id} from iOS phone book`);

        // Import the iOS Phone Book API client
        const iOSPhoneBookAPI = (await import('./iOSPhoneBookAPI.js')).default;

        // Call the iOS Phone Book API to remove the contact
        await iOSPhoneBookAPI.removeContact(customer.external_phone_book_id);
      }

      // For Android
      if (customer.android_phone_book_sync && customer.external_phone_book_id) {
        logger.info(`Removing customer ${customer.id} from Android phone book`);

        // Import the Android Phone Book API client
        const AndroidPhoneBookAPI = (await import('./AndroidPhoneBookAPI.js')).default;

        // Call the Android Phone Book API to remove the contact
        await AndroidPhoneBookAPI.removeContact(customer.external_phone_book_id);
      }

      return true;
    } catch (error) {
      logger.error('Error removing customer from phone book:', error);
      throw error;
    }
  }

  /**
   * Handle updates from phone books (two-way sync)
   * @param {Object} data - The updated contact data from phone book
   * @returns {Promise<Object>} The updated customer
   */
  async handlePhoneBookUpdate(data) {
    const transaction = await sequelize.transaction();

    try {
      // Find customer by external phone book ID
      const customer = await Customer.findOne({
        where: { 
          external_phone_book_id: data.externalId,
          phone_book_subscription: true
        }
      });

      if (!customer) {
        await transaction.rollback();
        throw new Error('Customer not found or not subscribed');
      }

      // Update customer with data from phone book
      await customer.update({
        name: data.name || customer.name,
        contact_name: data.contactName || customer.contact_name,
        email: data.email || customer.email,
        phone: data.phone || customer.phone,
        address: data.address || customer.address,
        city: data.city || customer.city,
        state: data.state || customer.state,
        zip_code: data.zipCode || customer.zip_code,
        phone_book_last_sync: new Date()
      }, { transaction });

      await transaction.commit();
      return customer;
    } catch (error) {
      await transaction.rollback();
      logger.error('Error handling phone book update:', error);
      throw error;
    }
  }

  /**
   * Sync all subscribed customers to phone books
   * @returns {Promise<Object>} Sync results
   */
  async syncAllCustomers() {
    try {
      // Find all customers subscribed to phone book updates
      const customers = await Customer.findAll({
        where: {
          phone_book_subscription: true,
          [Op.or]: [
            { ios_phone_book_sync: true },
            { android_phone_book_sync: true }
          ]
        }
      });

      logger.info(`Found ${customers.length} customers to sync with phone books`);

      const results = {
        total: customers.length,
        success: 0,
        failed: 0,
        errors: []
      };

      // Sync each customer
      for (const customer of customers) {
        try {
          await this.syncCustomerToPhoneBook(customer);
          results.success++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            customerId: customer.id,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      logger.error('Error syncing all customers:', error);
      throw error;
    }
  }
}

export default new PhoneBookService();
