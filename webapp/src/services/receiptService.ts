import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';
import { handleApiError, StructuredError } from '../utils/errorHandler';

// Define Receipt interface
export interface Receipt {
  id: string;
  receipt_number: string | null;
  vendor_name: string | null;
  amount: number | null;
  currency: string;
  receipt_date: string;
  description: string | null;
  status: 'pending' | 'approved' | 'rejected';
  categories: string[] | null;
  file_path: string | null;
  file_size: number | null;
  file_type: string | null;
  mime_type: string | null;
  email_source: string | null;
  email_subject: string | null;
  email_received_at: string | null;
  tenant_id: string;
  farm_id: string;
  expense_id: string | null;
  uploaded_by: string | null;
  created_at: string;
  updated_at: string;
  uploader?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  expense?: {
    id: string;
    amount: number;
    description: string;
    status: string;
    expense_date: string;
  };
}

// Define Receipt Summary interface
export interface ReceiptSummary {
  total_amount: number;
  total_count: number;
  by_status: {
    pending: { count: number; amount: number };
    approved: { count: number; amount: number };
    rejected: { count: number; amount: number };
  };
  by_vendor: Record<string, { count: number; amount: number }>;
}

// Get all receipts for a farm with optional filtering
export const getReceipts = async (
  farmId: string,
  params: {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    vendor?: string;
    category?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.search) queryParams.append('search', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.vendor) queryParams.append('vendor', params.vendor);
    if (params.category) queryParams.append('category', params.category);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/receipts/farm/${farmId}${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching receipts:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch receipts');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get receipt by ID
export const getReceiptById = async (id: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.get(`${API_URL}/receipts/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching receipt:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch receipt details');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Upload a new receipt
export const uploadReceipt = async (
  farmId: string,
  data: FormData
) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/receipts/farm/${farmId}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data'
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error uploading receipt:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to upload receipt');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update a receipt
export const updateReceipt = async (
  id: string,
  data: {
    receipt_number?: string;
    vendor_name?: string;
    amount?: number;
    currency?: string;
    receipt_date?: string;
    description?: string;
    status?: 'pending' | 'approved' | 'rejected';
    expense_id?: string | null;
  }
) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.put(`${API_URL}/receipts/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating receipt:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update receipt');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete a receipt
export const deleteReceipt = async (id: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.delete(`${API_URL}/receipts/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting receipt:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete receipt');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Download a receipt
export const downloadReceipt = (id: string) => {
  const token = localStorage.getItem('token');

  // Open in a new tab
  window.open(`${API_URL}/receipts/${id}/download?token=${token}`, '_blank');
};

// Link a receipt to an expense
export const linkReceiptToExpense = async (receiptId: string, expenseId: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/receipts/${receiptId}/link/${expenseId}`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error linking receipt to expense:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to link receipt to expense');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get receipt summary
export const getReceiptSummary = async (
  farmId: string,
  params: {
    startDate?: string;
    endDate?: string;
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const queryString = queryParams.toString();
    const url = `${API_URL}/receipts/farm/${farmId}/summary${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data as ReceiptSummary;
  } catch (error: unknown) {
    console.error('Error fetching receipt summary:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch receipt summary');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Format file size for display
export const formatFileSize = (bytes: number | null) => {
  if (bytes === null || bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// Enhanced OCR processing for receipts
export const processReceiptWithOcr = async (id: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/receipts/${id}/ocr`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error processing receipt with OCR:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to process receipt with OCR');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Categorize receipt using AI
export const categorizeReceipt = async (id: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/receipts/${id}/categorize`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error categorizing receipt:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to categorize receipt');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Match receipt to transactions
export const matchReceiptToTransactions = async (id: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/receipts/${id}/match`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error matching receipt to transactions:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to match receipt to transactions');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Generate expense report
export const generateExpenseReport = async (
  farmId: string,
  params: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'category' | 'vendor' | 'date' | 'status';
    format?: 'json' | 'csv' | 'pdf';
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.groupBy) queryParams.append('groupBy', params.groupBy);
    if (params.format) queryParams.append('format', params.format);

    const queryString = queryParams.toString();
    const url = `${API_URL}/receipts/farm/${farmId}/report${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error generating expense report:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to generate expense report');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};
