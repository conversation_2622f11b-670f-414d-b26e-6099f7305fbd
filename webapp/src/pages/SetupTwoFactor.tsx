import { useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import Layout from '../components/Layout';

const SetupTwoFactor = () => {
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [secret, setSecret] = useState<string | null>(null);
  const [token, setToken] = useState('');
  const [step, setStep] = useState<'loading' | 'setup' | 'verify'>('loading');
  
  const { setupTwoFactor, confirmTwoFactor, user, loading, error, clearError } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    // Check if 2FA is already enabled
    if (user.twoFactorEnabled) {
      navigate('/dashboard');
      return;
    }

    // Initialize 2FA setup
    const initSetup = async () => {
      try {
        const result = await setupTwoFactor();
        setQrCode(result.qrCode);
        setSecret(result.secret);
        setStep('setup');
      } catch (err) {
        console.error('2FA setup error:', err);
        // Stay on loading state with error
      }
    };

    initSetup();
  }, [user, setupTwoFactor, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    try {
      await confirmTwoFactor(token);
      setStep('verify');
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
    } catch (err) {
      // Error is handled by the context
      console.error('2FA confirmation error:', err);
    }
  };

  if (step === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <p className="ml-3 text-gray-500">Loading two-factor authentication setup...</p>
        </div>
      </Layout>
    );
  }

  if (step === 'verify') {
    return (
      <Layout>
        <div className="max-w-md mx-auto">
          <div className="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-md">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">Success!</p>
                <p className="text-sm text-green-700">Two-factor authentication has been enabled for your account.</p>
              </div>
            </div>
            <p className="text-xs text-green-600 mt-3 text-center">Redirecting to dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-900">Set Up Two-Factor Authentication</h2>
          </div>
          
          <div className="p-6">
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span className="block sm:inline">{error}</span>
              </div>
            )}
            
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-900 mb-2">Step 1: Scan QR Code</h3>
              <p className="text-sm text-gray-600 mb-4">
                Scan this QR code with your authenticator app (like Google Authenticator, Authy, or Microsoft Authenticator).
              </p>
              
              {qrCode && (
                <div className="flex justify-center mb-4">
                  <img src={qrCode} alt="QR Code for 2FA" className="border border-gray-300 rounded-md" />
                </div>
              )}
              
              {secret && (
                <div className="mb-6">
                  <p className="text-sm text-gray-600 mb-2">
                    If you can't scan the QR code, enter this code manually in your app:
                  </p>
                  <div className="bg-gray-100 p-2 rounded-md font-mono text-center break-all">
                    {secret}
                  </div>
                </div>
              )}
            </div>
            
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-900 mb-2">Step 2: Verify Setup</h3>
              <p className="text-sm text-gray-600 mb-4">
                Enter the 6-digit code from your authenticator app to verify the setup.
              </p>
              
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-1">Authentication Code</label>
                  <input
                    id="token"
                    name="token"
                    type="text"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Enter 6-digit code"
                    value={token}
                    onChange={(e) => setToken(e.target.value)}
                    autoComplete="off"
                    maxLength={6}
                    pattern="[0-9]*"
                    inputMode="numeric"
                  />
                </div>
                
                <button
                  type="submit"
                  disabled={loading || token.length !== 6}
                  className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(loading || token.length !== 6) ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {loading ? 'Verifying...' : 'Verify and Enable 2FA'}
                </button>
              </form>
            </div>
            
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    Two-factor authentication adds an extra layer of security to your account by requiring a code from your phone in addition to your password.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SetupTwoFactor;