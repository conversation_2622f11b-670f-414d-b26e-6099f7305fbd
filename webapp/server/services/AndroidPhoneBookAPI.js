/**
 * Android Phone Book API Client
 * This module provides methods to interact with the Android Contacts API
 */

import axios from 'axios';
import logger from '../utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

// Android Contacts API configuration
const ANDROID_CONTACTS_API_URL = process.env.ANDROID_CONTACTS_API_URL || 'https://api.android.com/contacts';
const ANDROID_CONTACTS_API_KEY = process.env.ANDROID_CONTACTS_API_KEY;

// Validate required environment variables
if (!ANDROID_CONTACTS_API_KEY) {
  logger.warn('Android Contacts API key is not set. Android phone book integration will not work properly.');
}

/**
 * Android Phone Book API Client
 */
class AndroidPhoneBookAPI {
  /**
   * Create an instance of the Android Phone Book API client
   */
  constructor() {
    this.client = axios.create({
      baseURL: ANDROID_CONTACTS_API_URL,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': ANDROID_CONTACTS_API_KEY,
        'User-Agent': 'NxtAcre Farm Management/1.0'
      }
    });
  }

  /**
   * Add or update a contact in the Android phone book
   * @param {Object} contactData - The contact data
   * @param {string} contactData.name - The contact name
   * @param {string} contactData.phone - The contact phone number
   * @param {string} contactData.email - The contact email
   * @param {string} contactData.address - The contact address
   * @param {string} contactData.city - The contact city
   * @param {string} contactData.state - The contact state
   * @param {string} contactData.zipCode - The contact zip code
   * @param {string} contactData.country - The contact country
   * @param {string} [contactData.externalId] - The existing contact ID (for updates)
   * @returns {Promise<Object>} The created or updated contact
   */
  async addContact(contactData) {
    try {
      // Format the contact data according to Android Contacts API requirements
      const formattedContact = this.formatContactData(contactData);
      
      let response;
      
      // If external ID exists, update the contact, otherwise create a new one
      if (contactData.externalId) {
        logger.info(`Updating Android contact: ${contactData.externalId}`);
        response = await this.client.patch(`/people/${contactData.externalId}`, formattedContact);
      } else {
        logger.info('Creating new Android contact');
        response = await this.client.post('/people', formattedContact);
      }
      
      // Return the contact data with the external ID
      return {
        id: response.data.resourceName || response.data.id,
        success: true,
        message: contactData.externalId ? 'Contact updated successfully' : 'Contact created successfully'
      };
    } catch (error) {
      logger.error('Error adding/updating Android contact:', error);
      
      // Handle specific API errors
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        if (status === 401 || status === 403) {
          throw new Error('Authentication failed with Android Contacts API. Check your API key.');
        } else if (status === 400) {
          throw new Error(`Invalid contact data: ${errorData.error?.message || 'Unknown error'}`);
        } else if (status === 404 && contactData.externalId) {
          // If contact not found during update, try to create a new one
          logger.warn(`Contact ${contactData.externalId} not found in Android, creating new contact`);
          return this.addContact({ ...contactData, externalId: null });
        }
      }
      
      throw new Error(`Android Contacts API error: ${error.message}`);
    }
  }

  /**
   * Remove a contact from the Android phone book
   * @param {string} contactId - The contact ID
   * @returns {Promise<Object>} The result of the operation
   */
  async removeContact(contactId) {
    try {
      if (!contactId) {
        throw new Error('Contact ID is required');
      }
      
      logger.info(`Removing Android contact: ${contactId}`);
      await this.client.delete(`/people/${contactId}`);
      
      return {
        success: true,
        message: 'Contact removed successfully'
      };
    } catch (error) {
      logger.error('Error removing Android contact:', error);
      
      // Handle specific API errors
      if (error.response) {
        const status = error.response.status;
        
        if (status === 401 || status === 403) {
          throw new Error('Authentication failed with Android Contacts API. Check your API key.');
        } else if (status === 404) {
          // If contact not found, consider it already removed
          logger.warn(`Contact ${contactId} not found in Android, considering it already removed`);
          return {
            success: true,
            message: 'Contact already removed or not found'
          };
        }
      }
      
      throw new Error(`Android Contacts API error: ${error.message}`);
    }
  }

  /**
   * Format contact data according to Android Contacts API requirements
   * @param {Object} contactData - The raw contact data
   * @returns {Object} The formatted contact data
   */
  formatContactData(contactData) {
    // Create a properly formatted contact object for Android Contacts API
    // Note: Android Contacts API has a different structure than iOS
    return {
      names: [
        {
          displayName: contactData.name,
          givenName: contactData.name.split(' ')[0] || '',
          familyName: contactData.name.split(' ').slice(1).join(' ') || ''
        }
      ],
      organizations: [
        {
          name: 'NxtAcre Farm Management',
          type: 'work'
        }
      ],
      phoneNumbers: [
        {
          value: contactData.phone || '',
          type: 'work'
        }
      ],
      emailAddresses: [
        {
          value: contactData.email || '',
          type: 'work'
        }
      ],
      addresses: [
        {
          streetAddress: contactData.address || '',
          city: contactData.city || '',
          region: contactData.state || '',
          postalCode: contactData.zipCode || '',
          country: contactData.country || 'USA',
          type: 'work'
        }
      ],
      // Add metadata to identify this contact as managed by our system
      userDefined: [
        {
          key: 'source',
          value: 'nxtacre-farm-management'
        },
        {
          key: 'customerId',
          value: contactData.customerId || ''
        },
        {
          key: 'farmId',
          value: contactData.farmId || ''
        }
      ]
    };
  }
}

export default new AndroidPhoneBookAPI();