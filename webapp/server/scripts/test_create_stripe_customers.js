#!/usr/bin/env node

/**
 * Test script for create_stripe_customers_for_existing_farms.js
 * 
 * This script runs the create_stripe_customers_for_existing_farms.js script
 * in dry-run mode with a limit of 5 farms to verify that it works correctly.
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the main script
const scriptPath = path.join(__dirname, 'create_stripe_customers_for_existing_farms.js');

console.log('Running test for create_stripe_customers_for_existing_farms.js');
console.log('Script path:', scriptPath);
console.log('Running in dry-run mode with a limit of 5 farms');

// Spawn the main script as a child process
const child = spawn('node', [scriptPath, '--dry-run', '--limit=5'], {
  stdio: 'inherit' // Pipe the child's stdout and stderr to the parent
});

// Handle the child process exit
child.on('close', (code) => {
  console.log(`Test script exited with code ${code}`);
  process.exit(code);
});