import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';
import Driver from './Driver.js';
import Customer from './Customer.js';
import Order from './Order.js';

dotenv.config();

const Delivery = defineModel('Delivery', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  driver_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Driver,
      key: 'id'
    },
    comment: 'The driver assigned to this delivery'
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Customer,
      key: 'id'
    },
    comment: 'The customer receiving this delivery'
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Order,
      key: 'id'
    },
    comment: 'The order associated with this delivery'
  },
  delivery_type: {
    type: DataTypes.ENUM('product', 'equipment', 'other'),
    allowNull: false,
    defaultValue: 'product'
  },
  status: {
    type: DataTypes.ENUM('scheduled', 'in_transit', 'delivered', 'failed', 'cancelled'),
    allowNull: false,
    defaultValue: 'scheduled'
  },
  scheduled_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  estimated_arrival: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_delivery_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delivery_address: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  delivery_city: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  delivery_state: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  delivery_zip: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  delivery_country: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: 'USA'
  },
  delivery_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  signature_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  signature_image: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Path to the signature image file'
  },
  proof_of_delivery_image: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Path to the proof of delivery image file'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'deliveries',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default Delivery;
