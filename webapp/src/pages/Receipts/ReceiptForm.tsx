import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import Layout from '../../components/Layout';
import { useFarm } from '../../context/FarmContext';
import { 
  getReceiptById, 
  uploadReceipt, 
  updateReceipt, 
  formatDate, 
  Receipt 
} from '../../services/receiptService';

const ReceiptForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const isEditMode = !!id;

  // Form state
  const [receipt, setReceipt] = useState<Partial<Receipt>>({
    receipt_number: '',
    vendor_name: '',
    amount: null,
    currency: 'USD',
    receipt_date: new Date().toISOString().split('T')[0],
    description: '',
    status: 'pending',
    categories: []
  });
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load receipt data if in edit mode
  useEffect(() => {
    const fetchReceipt = async () => {
      if (!isEditMode) return;

      setLoading(true);
      try {
        const data = await getReceiptById(id);
        setReceipt({
          ...data,
          receipt_date: data.receipt_date ? new Date(data.receipt_date).toISOString().split('T')[0] : '',
        });
      } catch (err: any) {
        console.error('Error fetching receipt:', err);
        setError(err.response?.data?.error || 'Failed to load receipt. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchReceipt();
  }, [id, isEditMode]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setReceipt(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle amount input change (convert to number)
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setReceipt(prev => ({
      ...prev,
      amount: value === '' ? null : parseFloat(value)
    }));
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Handle category input change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Split by commas and trim whitespace
    const categoryArray = value.split(',').map(cat => cat.trim()).filter(cat => cat !== '');
    setReceipt(prev => ({
      ...prev,
      categories: categoryArray
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (!currentFarm?.id) {
        throw new Error('No farm selected');
      }

      if (isEditMode) {
        // Update existing receipt
        const { id: receiptId, ...receiptData } = receipt;
        await updateReceipt(id, receiptData);
        setSuccess('Receipt updated successfully');
      } else {
        // Create new receipt
        const formData = new FormData();

        // Add receipt data to form data
        Object.entries(receipt).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            formData.append(key, value.toString());
          }
        });

        // Add file to form data if provided
        if (file) {
          formData.append('file', file);
        }

        await uploadReceipt(currentFarm.id, formData);
        setSuccess('Receipt created successfully');

        // Reset form if creating a new receipt
        if (!isEditMode) {
          setReceipt({
            receipt_number: '',
            vendor_name: '',
            amount: null,
            currency: 'USD',
            receipt_date: new Date().toISOString().split('T')[0],
            description: '',
            status: 'pending',
            categories: []
          });
          setFile(null);

          // Reset file input
          const fileInput = document.getElementById('file') as HTMLInputElement;
          if (fileInput) {
            fileInput.value = '';
          }
        }
      }

      // Navigate back to receipts list after a short delay
      setTimeout(() => {
        navigate('/receipts');
      }, 2000);
    } catch (err: any) {
      console.error('Error saving receipt:', err);
      setError(err.response?.data?.error || 'Failed to save receipt. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-3xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            {isEditMode ? 'Edit Receipt' : 'Upload Receipt'}
          </h1>
          <Link
            to="/receipts"
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </Link>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
            {error}
          </div>
        )}

        {/* Success message */}
        {success && (
          <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div className="col-span-2">
              <label htmlFor="vendor_name" className="block text-sm font-medium text-gray-700">
                Vendor Name *
              </label>
              <input
                type="text"
                id="vendor_name"
                name="vendor_name"
                value={receipt.vendor_name || ''}
                onChange={handleInputChange}
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="receipt_number" className="block text-sm font-medium text-gray-700">
                Receipt Number
              </label>
              <input
                type="text"
                id="receipt_number"
                name="receipt_number"
                value={receipt.receipt_number || ''}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="receipt_date" className="block text-sm font-medium text-gray-700">
                Receipt Date *
              </label>
              <input
                type="date"
                id="receipt_date"
                name="receipt_date"
                value={receipt.receipt_date || ''}
                onChange={handleInputChange}
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                Amount *
              </label>
              <input
                type="number"
                id="amount"
                name="amount"
                value={receipt.amount === null ? '' : receipt.amount}
                onChange={handleAmountChange}
                step="0.01"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
                Currency
              </label>
              <select
                id="currency"
                name="currency"
                value={receipt.currency || 'USD'}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="CAD">CAD</option>
                <option value="AUD">AUD</option>
              </select>
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={receipt.status || 'pending'}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <div className="col-span-2">
              <label htmlFor="categories" className="block text-sm font-medium text-gray-700">
                Categories
              </label>
              <input
                type="text"
                id="categories"
                name="categories"
                value={receipt.categories ? receipt.categories.join(', ') : ''}
                onChange={handleCategoryChange}
                placeholder="Enter categories separated by commas (e.g., Office Supplies, Travel, Meals)"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
              <p className="mt-1 text-sm text-gray-500">
                Categories help organize receipts and classify them for tax purposes
              </p>
            </div>

            <div className="col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={receipt.description || ''}
                onChange={handleInputChange}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {!isEditMode && (
              <div className="col-span-2">
                <label htmlFor="file" className="block text-sm font-medium text-gray-700">
                  Receipt File
                </label>
                <input
                  type="file"
                  id="file"
                  name="file"
                  onChange={handleFileChange}
                  accept=".pdf,.png,.jpg,.jpeg"
                  className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Accepted file types: PDF, PNG, JPG, JPEG
                </p>
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                loading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Receipt' : 'Upload Receipt'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default ReceiptForm;
