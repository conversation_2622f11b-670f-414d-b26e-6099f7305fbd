import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import Receipt from '../models/Receipt.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import Expense from '../models/Expense.js';
import UserFarm from '../models/UserFarm.js';
import Transaction from '../models/Transaction.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  validateFileType,
  generateStoragePath,
  saveFile,
  deleteFile,
  checkStorageQuota,
  updateStorageUsage
} from '../utils/fileUtils.js';
import { downloadFromSpaces, fileExistsInSpaces } from '../utils/spacesUtils.js';

// Helper function to check if a user has access to a farm
const checkUserFarmAccess = async (userId, farmId) => {
  if (!userId || !farmId) return false;

  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  return !!userFarm;
};

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

/**
 * Get all receipts for a farm with filtering and pagination
 */
export const getAllReceipts = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      search, 
      status, 
      startDate, 
      endDate, 
      vendor,
      category,
      page = 1, 
      limit = 50, 
      sortBy = 'receipt_date', 
      sortOrder = 'desc' 
    } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId };

    // Filter by status if provided
    if (status && status !== 'all') {
      where.status = status;
    }

    // Filter by vendor if provided
    if (vendor) {
      where.vendor_name = { [Op.iLike]: `%${vendor}%` };
    }

    // Filter by category if provided
    if (category) {
      where.categories = { [Op.contains]: [category] };
    }

    // Filter by date range if provided
    if (startDate || endDate) {
      where.receipt_date = {};

      if (startDate) {
        where.receipt_date[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.receipt_date[Op.lte] = endDateTime;
      }
    }

    // Add search condition if provided
    if (search) {
      where[Op.or] = [
        { receipt_number: { [Op.iLike]: `%${search}%` } },
        { vendor_name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { email_subject: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    const order = [[sortBy, sortOrder.toUpperCase()]];

    // Get receipts
    const { count, rows: receipts } = await Receipt.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Expense,
          as: 'expense',
          attributes: ['id', 'amount', 'description', 'status']
        }
      ],
      order,
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      receipts,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error getting receipts:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a receipt by ID
 */
export const getReceiptById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get receipt with associations
    const receipt = await Receipt.findByPk(id, {
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        },
        {
          model: Expense,
          as: 'expense',
          attributes: ['id', 'amount', 'description', 'status', 'expense_date']
        }
      ]
    });

    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    return res.status(200).json(receipt);
  } catch (error) {
    console.error('Error getting receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Upload a new receipt
 */
export const uploadReceipt = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const { 
      receipt_number, 
      vendor_name, 
      amount, 
      currency, 
      receipt_date, 
      description,
      expense_id,
      categories
    } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if expense exists if provided
    if (expense_id) {
      const expense = await Expense.findOne({
        where: {
          id: expense_id,
          farm_id: farmId
        }
      });

      if (!expense) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Expense not found' });
      }
    }

    // Create receipt data object
    const receiptData = {
      receipt_number,
      vendor_name,
      amount,
      currency: currency || 'USD',
      receipt_date: receipt_date ? new Date(receipt_date) : new Date(),
      description,
      status: 'pending',
      farm_id: farmId,
      tenant_id: farmId, // Using farm_id as tenant_id for now
      expense_id: expense_id || null,
      uploaded_by: req.user.id,
      categories: categories || []
    };

    // Check if file was uploaded
    if (req.files && req.files.file) {
      const file = req.files.file;

      // Validate file type
      const fileBuffer = Buffer.from(file.data);
      const fileTypeValidation = await validateFileType(file.name, fileBuffer);

      if (!fileTypeValidation.valid) {
        await transaction.rollback();
        return res.status(400).json({ 
          error: 'Invalid file type', 
          reason: fileTypeValidation.reason 
        });
      }

      // Check storage quota
      const quotaCheck = await checkStorageQuota(farmId, file.size, req.user.is_global_admin);

      if (!quotaCheck.allowed) {
        await transaction.rollback();
        return res.status(400).json({ 
          error: quotaCheck.reason,
          currentUsage: quotaCheck.currentUsage,
          quota: quotaCheck.quota
        });
      }

      // Generate storage path
      const storagePath = generateStoragePath(farmId, req.user.id, file.name);

      // Save file to disk
      const fullPath = await saveFile(file.tempFilePath || file.data, storagePath);

      // Add file information to receipt data
      receiptData.file_path = storagePath;
      receiptData.file_size = file.size;
      receiptData.file_type = path.extname(file.name).substring(1) || 'unknown';
      receiptData.mime_type = fileTypeValidation.detectedType || file.mimetype;

      // Update storage usage
      await updateStorageUsage(farmId, file.size, false, 1);
    }

    // Create receipt record
    const receipt = await Receipt.create(receiptData, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Receipt uploaded successfully',
      receipt
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error uploading receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Download a receipt
 */
export const downloadReceipt = async (req, res) => {
  try {
    const { id } = req.params;

    // Get receipt
    const receipt = await Receipt.findByPk(id);

    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // Check if receipt has a file
    if (!receipt.file_path) {
      return res.status(404).json({ error: 'No file attached to this receipt' });
    }

    try {
      // First try to get the file from Spaces
      const fileExists = await fileExistsInSpaces(receipt.file_path);

      if (fileExists) {
        // File exists in Spaces, download it
        const fileBuffer = await downloadFromSpaces(receipt.file_path);

        // Set headers
        res.setHeader('Content-Type', receipt.mime_type);
        res.setHeader('Content-Disposition', `attachment; filename="${receipt.receipt_number || 'receipt'}.${receipt.file_type}"`);

        // Send file
        return res.send(fileBuffer);
      } else {
        // Fallback to local filesystem
        const filePath = path.join(uploadsDir, receipt.file_path);

        // Check if file exists locally
        if (!fs.existsSync(filePath)) {
          return res.status(404).json({ error: 'File not found in storage' });
        }

        // Set headers
        res.setHeader('Content-Type', receipt.mime_type);
        res.setHeader('Content-Disposition', `attachment; filename="${receipt.receipt_number || 'receipt'}.${receipt.file_type}"`);

        // Stream file to response
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);
      }
    } catch (error) {
      console.error('Error downloading receipt:', error);
      return res.status(500).json({ error: 'Error downloading receipt' });
    }
  } catch (error) {
    console.error('Error downloading receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Update a receipt
 */
export const updateReceipt = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { 
      receipt_number, 
      vendor_name, 
      amount, 
      currency, 
      receipt_date, 
      description,
      status,
      expense_id,
      categories
    } = req.body;

    // Get receipt
    const receipt = await Receipt.findByPk(id);

    if (!receipt) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // Check if expense exists if provided
    if (expense_id) {
      const expense = await Expense.findOne({
        where: {
          id: expense_id,
          farm_id: receipt.farm_id
        }
      });

      if (!expense) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Expense not found' });
      }
    }

    // Update receipt
    const updates = {};

    if (receipt_number !== undefined) updates.receipt_number = receipt_number;
    if (vendor_name !== undefined) updates.vendor_name = vendor_name;
    if (amount !== undefined) updates.amount = amount;
    if (currency !== undefined) updates.currency = currency;
    if (receipt_date !== undefined) updates.receipt_date = new Date(receipt_date);
    if (description !== undefined) updates.description = description;
    if (status !== undefined) updates.status = status;
    if (expense_id !== undefined) {
      // Convert string "null" to actual null value
      updates.expense_id = (expense_id === "null" || expense_id === null) ? null : expense_id;
    }

    if (categories !== undefined) {
      updates.categories = categories;
    }

    await receipt.update(updates, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Receipt updated successfully',
      receipt
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a receipt
 */
export const deleteReceipt = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Get receipt
    const receipt = await Receipt.findByPk(id);

    if (!receipt) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // Delete file if it exists
    if (receipt.file_path) {
      await deleteFile(receipt.file_path);

      // Update storage usage
      await updateStorageUsage(
        receipt.farm_id,
        -receipt.file_size,
        false,
        -1
      );
    }

    // Delete receipt record
    await receipt.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Receipt deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Process an email receipt
 * This endpoint will be called by the email service webhook
 */
export const processEmailReceipt = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      from, 
      subject, 
      text, 
      html, 
      attachments, 
      recipient,
      timestamp 
    } = req.body;

    // Extract farm subdomain from recipient
    // Example: <EMAIL> -> farm1
    const recipientParts = recipient.split('@');
    if (recipientParts.length !== 2) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid recipient email format' });
    }

    const domainParts = recipientParts[1].split('.');
    if (domainParts.length < 3 || domainParts[domainParts.length - 2] !== 'nxtacre') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid domain format' });
    }

    const subdomain = domainParts[0];

    // Find farm by subdomain
    const farm = await Farm.findOne({
      where: {
        subdomain: subdomain
      }
    });

    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found for the given subdomain' });
    }

    // Create receipt data
    const receiptData = {
      email_source: from,
      email_subject: subject,
      email_received_at: timestamp ? new Date(timestamp) : new Date(),
      description: text || '',
      status: 'pending',
      farm_id: farm.id,
      tenant_id: farm.id, // Using farm_id as tenant_id for now
      receipt_date: new Date(),
      categories: [] // Initialize with empty categories array
    };

    // Try to extract vendor name and amount from subject or text
    // This is a simple example - in a real implementation, you might use
    // more sophisticated parsing or OCR for attachments
    const vendorMatch = subject.match(/from\s+([^$]+)/i) || text?.match(/from\s+([^$]+)/i);
    if (vendorMatch && vendorMatch[1]) {
      receiptData.vendor_name = vendorMatch[1].trim();
    }

    const amountMatch = subject.match(/\$\s*(\d+(\.\d{1,2})?)/i) || text?.match(/\$\s*(\d+(\.\d{1,2})?)/i);
    if (amountMatch && amountMatch[1]) {
      receiptData.amount = parseFloat(amountMatch[1]);
    }

    // Handle attachments
    if (attachments && attachments.length > 0) {
      // Process the first attachment only for simplicity
      const attachment = attachments[0];

      // Generate a unique filename
      const filename = `${Date.now()}-${attachment.filename || 'attachment'}`;

      // Generate storage path
      const storagePath = generateStoragePath(farm.id, 'email', filename);

      // Save file to disk
      const fullPath = await saveFile(Buffer.from(attachment.content, 'base64'), storagePath);

      // Add file information to receipt data
      receiptData.file_path = storagePath;
      receiptData.file_size = attachment.size || Buffer.from(attachment.content, 'base64').length;
      receiptData.file_type = path.extname(attachment.filename || 'attachment').substring(1) || 'unknown';
      receiptData.mime_type = attachment.contentType || 'application/octet-stream';

      // Update storage usage
      await updateStorageUsage(farm.id, receiptData.file_size, false, 1);
    }

    // Create receipt record
    const receipt = await Receipt.create(receiptData, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Email receipt processed successfully',
      receipt
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing email receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Link a receipt to an expense
 */
export const linkReceiptToExpense = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { receiptId, expenseId } = req.params;

    // Get receipt
    const receipt = await Receipt.findByPk(receiptId);

    if (!receipt) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // Get expense
    const expense = await Expense.findByPk(expenseId);

    if (!expense) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Expense not found' });
    }

    // Check if expense belongs to the same farm
    if (expense.farm_id !== receipt.farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Expense does not belong to the same farm as the receipt' });
    }

    // Update receipt with expense ID
    await receipt.update({ expense_id: expenseId }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Receipt linked to expense successfully',
      receipt
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error linking receipt to expense:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get receipt summary statistics
 */
export const getReceiptSummary = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId };

    // Filter by date range if provided
    if (startDate || endDate) {
      where.receipt_date = {};

      if (startDate) {
        where.receipt_date[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.receipt_date[Op.lte] = endDateTime;
      }
    }

    // Get receipts
    const receipts = await Receipt.findAll({
      where,
      attributes: [
        'status',
        'vendor_name',
        [sequelize.fn('SUM', sequelize.cast(sequelize.col('amount'), 'DECIMAL')), 'total_amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status', 'vendor_name']
    });

    // Calculate summary
    const summary = {
      total_amount: 0,
      total_count: 0,
      by_status: {
        pending: { count: 0, amount: 0 },
        approved: { count: 0, amount: 0 },
        rejected: { count: 0, amount: 0 }
      },
      by_vendor: {}
    };

    receipts.forEach(receipt => {
      const amount = parseFloat(receipt.dataValues.total_amount) || 0;
      const count = parseInt(receipt.dataValues.count) || 0;

      summary.total_amount += amount;
      summary.total_count += count;

      // Summarize by status
      if (receipt.status && summary.by_status[receipt.status]) {
        summary.by_status[receipt.status].count += count;
        summary.by_status[receipt.status].amount += amount;
      }

      // Summarize by vendor
      if (receipt.vendor_name) {
        if (!summary.by_vendor[receipt.vendor_name]) {
          summary.by_vendor[receipt.vendor_name] = { count: 0, amount: 0 };
        }
        summary.by_vendor[receipt.vendor_name].count += count;
        summary.by_vendor[receipt.vendor_name].amount += amount;
      }
    });

    return res.status(200).json(summary);
  } catch (error) {
    console.error('Error generating receipt summary:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Enhanced OCR processing for receipts
 * This function uses more advanced OCR to extract data from receipt images
 */
export const enhancedOcrProcessing = async (req, res) => {
  try {
    const { id } = req.params;

    // Get receipt
    const receipt = await Receipt.findByPk(id);

    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // Check if receipt has a file
    if (!receipt.file_path) {
      return res.status(400).json({ error: 'No file attached to this receipt' });
    }

    // In a real implementation, this would call an OCR service API
    // For this example, we'll simulate OCR results
    const ocrResults = {
      vendor_name: receipt.vendor_name || 'Auto-detected Vendor',
      amount: receipt.amount || 0,
      receipt_date: receipt.receipt_date,
      receipt_number: receipt.receipt_number || `OCR-${Date.now()}`,
      items: [
        { description: 'Item 1', quantity: 1, price: receipt.amount ? receipt.amount * 0.7 : 10 },
        { description: 'Item 2', quantity: 1, price: receipt.amount ? receipt.amount * 0.3 : 5 }
      ],
      tax: receipt.amount ? receipt.amount * 0.08 : 1.2,
      total: receipt.amount || 15,
      confidence_score: 0.85
    };

    // Update receipt with OCR results if they're not already set
    const updates = {};
    if (!receipt.vendor_name) updates.vendor_name = ocrResults.vendor_name;
    if (!receipt.amount) updates.amount = ocrResults.total;
    if (!receipt.receipt_number) updates.receipt_number = ocrResults.receipt_number;

    if (Object.keys(updates).length > 0) {
      await receipt.update(updates);
    }

    return res.status(200).json({
      message: 'OCR processing completed',
      receipt: await Receipt.findByPk(id),
      ocr_results: ocrResults
    });
  } catch (error) {
    console.error('Error processing OCR:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Categorize receipts using AI
 * This function uses AI to categorize receipts based on vendor, amount, and description
 */
export const categorizeReceipt = async (req, res) => {
  try {
    const { id } = req.params;

    // Get receipt
    const receipt = await Receipt.findByPk(id);

    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // In a real implementation, this would call an AI service API
    // For this example, we'll simulate AI categorization based on vendor name
    let category = 'Uncategorized';
    let subcategory = 'General';
    let confidence = 0.7;

    const vendorLower = (receipt.vendor_name || '').toLowerCase();

    if (vendorLower.includes('farm') || vendorLower.includes('agri') || vendorLower.includes('seed')) {
      category = 'Farm Supplies';
      subcategory = vendorLower.includes('seed') ? 'Seeds' : 'General Supplies';
      confidence = 0.9;
    } else if (vendorLower.includes('fuel') || vendorLower.includes('gas') || vendorLower.includes('petrol')) {
      category = 'Fuel';
      subcategory = 'Vehicle Fuel';
      confidence = 0.95;
    } else if (vendorLower.includes('repair') || vendorLower.includes('parts') || vendorLower.includes('equipment')) {
      category = 'Equipment';
      subcategory = 'Repairs & Maintenance';
      confidence = 0.85;
    } else if (vendorLower.includes('office') || vendorLower.includes('supply') || vendorLower.includes('paper')) {
      category = 'Office';
      subcategory = 'Office Supplies';
      confidence = 0.8;
    } else if (vendorLower.includes('food') || vendorLower.includes('restaurant') || vendorLower.includes('cafe')) {
      category = 'Food & Dining';
      subcategory = 'Meals';
      confidence = 0.85;
    }

    // Update receipt with category information
    await receipt.update({
      description: receipt.description ? 
        `${receipt.description} [Category: ${category}/${subcategory}]` : 
        `[Category: ${category}/${subcategory}]`
    });

    return res.status(200).json({
      message: 'Receipt categorized successfully',
      receipt: await Receipt.findByPk(id),
      categorization: {
        category,
        subcategory,
        confidence
      }
    });
  } catch (error) {
    console.error('Error categorizing receipt:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Match receipts to transactions
 * This function attempts to match receipts to transactions based on amount, date, and vendor
 */
export const matchReceiptToTransactions = async (req, res) => {
  try {
    const { id } = req.params;

    // Get receipt
    const receipt = await Receipt.findByPk(id);

    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, receipt.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this receipt' });
    }

    // Check if receipt has amount and date
    if (!receipt.amount || !receipt.receipt_date) {
      return res.status(400).json({ 
        error: 'Receipt must have amount and date for matching',
        missing_fields: !receipt.amount ? ['amount'] : !receipt.receipt_date ? ['receipt_date'] : []
      });
    }

    // Define date range for matching (3 days before and after receipt date)
    const receiptDate = new Date(receipt.receipt_date);
    const startDate = new Date(receiptDate);
    startDate.setDate(startDate.getDate() - 3);

    const endDate = new Date(receiptDate);
    endDate.setDate(endDate.getDate() + 3);

    // Find potential matching transactions
    const potentialMatches = await Transaction.findAll({
      where: {
        farm_id: receipt.farm_id,
        amount: {
          // Allow for small differences in amount (e.g., due to rounding)
          [Op.between]: [receipt.amount * 0.99, receipt.amount * 1.01]
        },
        transaction_date: {
          [Op.between]: [startDate, endDate]
        }
      },
      order: [
        [sequelize.literal(`ABS(EXTRACT(EPOCH FROM (transaction_date - '${receiptDate.toISOString()}')))`), 'ASC']
      ],
      limit: 5
    });

    // Calculate match score for each potential match
    const matchesWithScores = potentialMatches.map(transaction => {
      // Base score starts at 0.5 (50%)
      let score = 0.5;

      // Exact amount match adds 0.3 (30%)
      if (transaction.amount === receipt.amount) {
        score += 0.3;
      }

      // Date proximity adds up to 0.2 (20%)
      const dateDiff = Math.abs(new Date(transaction.transaction_date).getTime() - receiptDate.getTime());
      const daysDiff = dateDiff / (1000 * 60 * 60 * 24);

      if (daysDiff < 1) {
        score += 0.2; // Same day
      } else if (daysDiff < 2) {
        score += 0.1; // Within 1 day
      } else if (daysDiff < 3) {
        score += 0.05; // Within 2 days
      }

      // Vendor name match adds up to 0.3 (30%)
      if (receipt.vendor_name && transaction.description) {
        const vendorLower = receipt.vendor_name.toLowerCase();
        const descLower = transaction.description.toLowerCase();

        if (descLower.includes(vendorLower) || vendorLower.includes(descLower)) {
          score += 0.3;
        } else {
          // Check for partial matches
          const vendorWords = vendorLower.split(/\s+/);
          let wordMatches = 0;

          vendorWords.forEach(word => {
            if (word.length > 3 && descLower.includes(word)) {
              wordMatches++;
            }
          });

          if (wordMatches > 0) {
            score += Math.min(0.3, wordMatches * 0.1);
          }
        }
      }

      return {
        transaction,
        score,
        is_best_match: false
      };
    });

    // Sort by score and mark best match
    matchesWithScores.sort((a, b) => b.score - a.score);

    if (matchesWithScores.length > 0) {
      matchesWithScores[0].is_best_match = true;

      // If best match has high confidence, link it automatically
      if (matchesWithScores[0].score > 0.8) {
        // In a real implementation, you might want to link the receipt to the transaction
        // For this example, we'll just return the match information
      }
    }

    return res.status(200).json({
      message: 'Receipt matching completed',
      receipt,
      matches: matchesWithScores,
      best_match: matchesWithScores.length > 0 ? matchesWithScores[0] : null
    });
  } catch (error) {
    console.error('Error matching receipt to transactions:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Generate expense report from receipts
 * This function generates a comprehensive expense report from receipt data
 */
export const generateExpenseReport = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      startDate, 
      endDate, 
      groupBy = 'category', // category, vendor, date, status
      format = 'json' // json, csv, pdf (in a real implementation)
    } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId };

    // Filter by date range if provided
    if (startDate || endDate) {
      where.receipt_date = {};

      if (startDate) {
        where.receipt_date[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.receipt_date[Op.lte] = endDateTime;
      }
    }

    // Get receipts
    const receipts = await Receipt.findAll({
      where,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Expense,
          as: 'expense',
          attributes: ['id', 'amount', 'description', 'status', 'expense_date']
        }
      ],
      order: [['receipt_date', 'DESC']]
    });

    // Extract categories from description field
    const extractCategory = (description) => {
      if (!description) return { category: 'Uncategorized', subcategory: 'General' };

      const categoryMatch = description.match(/\[Category: ([^/]+)\/([^\]]+)\]/);
      if (categoryMatch) {
        return {
          category: categoryMatch[1],
          subcategory: categoryMatch[2]
        };
      }

      return { category: 'Uncategorized', subcategory: 'General' };
    };

    // Process receipts for report
    const processedReceipts = receipts.map(receipt => {
      const { category, subcategory } = extractCategory(receipt.description);

      return {
        id: receipt.id,
        receipt_number: receipt.receipt_number,
        vendor_name: receipt.vendor_name,
        amount: receipt.amount,
        currency: receipt.currency,
        receipt_date: receipt.receipt_date,
        status: receipt.status,
        category,
        subcategory,
        uploader: receipt.uploader ? `${receipt.uploader.first_name} ${receipt.uploader.last_name}` : 'Unknown',
        has_file: !!receipt.file_path,
        expense_id: receipt.expense_id
      };
    });

    // Group receipts based on groupBy parameter
    const groupedReceipts = {};

    processedReceipts.forEach(receipt => {
      let groupKey;

      switch (groupBy) {
        case 'category':
          groupKey = receipt.category;
          break;
        case 'vendor':
          groupKey = receipt.vendor_name || 'Unknown Vendor';
          break;
        case 'date':
          // Group by month and year
          const date = new Date(receipt.receipt_date);
          groupKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
          break;
        case 'status':
          groupKey = receipt.status;
          break;
        default:
          groupKey = 'All Receipts';
      }

      if (!groupedReceipts[groupKey]) {
        groupedReceipts[groupKey] = {
          receipts: [],
          total_amount: 0,
          count: 0
        };
      }

      groupedReceipts[groupKey].receipts.push(receipt);
      groupedReceipts[groupKey].total_amount += parseFloat(receipt.amount) || 0;
      groupedReceipts[groupKey].count += 1;
    });

    // Calculate overall totals
    const totalAmount = processedReceipts.reduce((sum, receipt) => sum + (parseFloat(receipt.amount) || 0), 0);
    const totalCount = processedReceipts.length;

    // Prepare report data
    const reportData = {
      farm_id: farmId,
      report_period: {
        start_date: startDate ? new Date(startDate).toISOString() : null,
        end_date: endDate ? new Date(endDate).toISOString() : null
      },
      generated_at: new Date().toISOString(),
      generated_by: `${req.user.first_name} ${req.user.last_name}`,
      totals: {
        amount: totalAmount,
        count: totalCount
      },
      grouped_by: groupBy,
      groups: groupedReceipts
    };

    // In a real implementation, you would handle different output formats here
    // For this example, we'll just return JSON
    return res.status(200).json(reportData);
  } catch (error) {
    console.error('Error generating expense report:', error);
    return res.status(500).json({ error: error.message });
  }
};
