import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import CameraViewer from '../../components/Camera/CameraViewer';
import { getFarmIoTDevices } from '../../services/iotService';
import { IoTDevice } from '../../services/iotService';

const CameraDashboard = () => {
  const [cameras, setCameras] = useState<IoTDevice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [gridLayout, setGridLayout] = useState<'1x1' | '2x2' | '3x3'>('2x2');

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch cameras for the selected farm
  useEffect(() => {
    const fetchCameras = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await getFarmIoTDevices(selectedFarm.id);
        // Filter devices to only include cameras
        const cameraDevices = response.devices.filter(device => 
          device.device_type === 'camera' && 
          device.configuration && 
          device.configuration.serialNumber
        );
        setCameras(cameraDevices);
      } catch (err: any) {
        console.error('Error fetching cameras:', err);
        setError('Failed to load cameras. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCameras();
  }, [selectedFarm]);

  // Calculate grid classes based on layout
  const getGridClasses = () => {
    switch (gridLayout) {
      case '1x1':
        return 'grid-cols-1';
      case '2x2':
        return 'grid-cols-1 md:grid-cols-2';
      case '3x3':
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      default:
        return 'grid-cols-1 md:grid-cols-2';
    }
  };

  // Calculate camera dimensions based on layout
  const getCameraDimensions = () => {
    const baseWidth = 640;
    const baseHeight = 480;
    
    switch (gridLayout) {
      case '1x1':
        return { width: baseWidth, height: baseHeight };
      case '2x2':
        return { width: baseWidth / 1.5, height: baseHeight / 1.5 };
      case '3x3':
        return { width: baseWidth / 2, height: baseHeight / 2 };
      default:
        return { width: baseWidth / 1.5, height: baseHeight / 1.5 };
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Camera Dashboard</h1>
        <div className="flex space-x-2">
          <div className="inline-flex shadow-sm rounded-md">
            <button
              type="button"
              onClick={() => setGridLayout('1x1')}
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                gridLayout === '1x1'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } border border-gray-300 focus:z-10 focus:outline-none focus:ring-1 focus:ring-primary-500`}
            >
              1×1
            </button>
            <button
              type="button"
              onClick={() => setGridLayout('2x2')}
              className={`px-4 py-2 text-sm font-medium ${
                gridLayout === '2x2'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } border-t border-b border-gray-300 focus:z-10 focus:outline-none focus:ring-1 focus:ring-primary-500`}
            >
              2×2
            </button>
            <button
              type="button"
              onClick={() => setGridLayout('3x3')}
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                gridLayout === '3x3'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } border border-gray-300 focus:z-10 focus:outline-none focus:ring-1 focus:ring-primary-500`}
            >
              3×3
            </button>
          </div>
          <Link
            to="/iot/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Camera
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {!selectedFarm ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">Please select a farm to view cameras.</p>
          <p className="text-sm text-gray-400 mb-6">
            Use the farm selector in the header to choose a farm.
          </p>
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading cameras...</p>
        </div>
      ) : cameras.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No cameras found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first camera to start monitoring your farm remotely.
          </p>
          <Link
            to="/iot/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Camera
          </Link>
        </div>
      ) : (
        <div className={`grid ${getGridClasses()} gap-6`}>
          {cameras.map((camera) => {
            const { width, height } = getCameraDimensions();
            const config = camera.configuration || {};
            
            return (
              <div key={camera.id} className="bg-white shadow rounded-lg overflow-hidden">
                <div className="p-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900">{camera.name}</h2>
                  <p className="text-sm text-gray-500">{camera.location_description || 'No location specified'}</p>
                </div>
                <div className="p-4">
                  {config.serialNumber && config.username && config.password ? (
                    <CameraViewer
                      serialNumber={config.serialNumber}
                      username={config.username}
                      password={config.password}
                      width={width}
                      height={height}
                    />
                  ) : (
                    <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative">
                      <span className="block sm:inline">
                        Camera configuration is incomplete. Please edit the camera settings.
                      </span>
                    </div>
                  )}
                </div>
                <div className="px-4 py-3 bg-gray-50 text-right">
                  <Link
                    to={`/iot/${camera.id}/edit`}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Edit Camera
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </Layout>
  );
};

export default CameraDashboard;