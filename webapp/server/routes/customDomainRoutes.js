import express from 'express';
import { setupCustomDomain, verifyCustomDomain, removeCustomDomain, getCustomDomain } from '../controllers/customDomainController.js';
import { authenticateToken, checkFarmAccess } from '../middleware/authMiddleware.js';

const router = express.Router();

// GET /api/farms/:farmId/custom-domain
// Get the custom domain for a farm
// Private (farm owner or admin)
router.get('/:farmId/custom-domain', authenticateToken, checkFarmAccess, getCustomDomain);

// POST /api/farms/:farmId/custom-domain
// Set up a custom domain for a farm
// Private (farm owner or admin)
router.post('/:farmId/custom-domain', authenticateToken, checkFarmAccess, setupCustomDomain);

// PUT /api/farms/:farmId/custom-domain/verify
// Verify a custom domain for a farm
// Private (farm owner or admin)
router.put('/:farmId/custom-domain/verify', authenticateToken, checkFarmAccess, verifyCustomDomain);

// DELETE /api/farms/:farmId/custom-domain
// Remove a custom domain from a farm
// Private (farm owner or admin)
router.delete('/:farmId/custom-domain', authenticateToken, checkFarmAccess, removeCustomDomain);

export default router;