import express from 'express';
import {
  generateRegistrationChallenge,
  registerPasskey,
  authenticateWith<PERSON>asskey,
  removePasskey
} from '../controllers/passkeyController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public route for passkey authentication
router.post('/authenticate', authenticateWithPasskey);

// Protected routes (require authentication)
router.get('/challenge/:userId', authenticate, generateRegistrationChallenge);
router.post('/register/:userId', authenticate, registerPasskey);
router.delete('/:userId', authenticate, removePasskey);

export default router;