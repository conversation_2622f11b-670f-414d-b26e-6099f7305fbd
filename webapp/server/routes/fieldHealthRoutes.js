import express from 'express';
import { authenticate } from '../middleware/index.js';
import { sequelize } from '../config/database.js';
import Field from '../models/Field.js';
import Farm from '../models/Farm.js';
import FieldSoilData from '../models/FieldSoilData.js';
import SoilSample from '../models/SoilSample.js';
import Weather from '../models/Weather.js';
import { getFieldSoilData, generateSoilRecommendations, fetchSoilData } from '../controllers/soilDataController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get field health data for a specific field
router.get('/:fieldId', async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field soil data
    const fieldSoilData = await FieldSoilData.findOne({
      where: { field_id: fieldId },
      order: [['updated_at', 'DESC']]
    });

    // Calculate NDVI score based on soil health index or use a default
    let ndviScore = 0.5; // Default score
    let status = 'warning';

    if (fieldSoilData && fieldSoilData.soil_health_index) {
      // Convert soil health index (0-100) to NDVI score (0-1)
      ndviScore = fieldSoilData.soil_health_index / 100;

      // Determine status based on NDVI score
      if (ndviScore > 0.7) {
        status = 'good';
      } else if (ndviScore > 0.4) {
        status = 'warning';
      } else {
        status = 'critical';
      }
    }

    // Get detailed soil data from soilDataController
    const soilDataResponse = await getFieldSoilData(req, res);

    // If the controller sent a response directly, return
    if (res.headersSent) {
      return;
    }

    // Combine field data with soil data
    const fieldHealthData = {
      fieldId,
      fieldName: field.name,
      ndviScore,
      lastUpdated: fieldSoilData ? fieldSoilData.updated_at : new Date().toISOString(),
      status,
      satelliteProvider: 'sentinel',
      coveragePercent: Math.floor(ndviScore * 100), // Convert NDVI to percentage
      healthTrend: fieldSoilData && fieldSoilData.soil_health_index > 50 ? 'improving' : 'declining',
      soilData: soilDataResponse ? soilDataResponse.soilData : null,
      recommendations: soilDataResponse ? soilDataResponse.recommendations : []
    };

    res.json(fieldHealthData);
  } catch (error) {
    console.error('Error fetching field health data:', error);
    res.status(500).json({ 
      error: 'Failed to fetch field health data',
      message: error.message
    });
  }
});

// Get field health history for a specific field
router.get('/:fieldId/history', async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { startDate, endDate } = req.query;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Set date range
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to 30 days ago
    const end = endDate ? new Date(endDate) : new Date();

    // Get soil samples for this field within the date range
    const soilSamples = await SoilSample.findAll({
      where: {
        field_id: fieldId,
        sample_date: {
          [sequelize.Op.between]: [start, end]
        }
      },
      order: [['sample_date', 'ASC']]
    });

    // Get field soil data for this field
    const fieldSoilData = await FieldSoilData.findOne({
      where: { field_id: fieldId },
      order: [['updated_at', 'DESC']]
    });

    // Get weather data for this field's location
    let weatherData = [];
    if (field.location_data && field.location_data.center) {
      const { latitude, longitude } = field.location_data.center;

      // Get weather data from the Weather model
      weatherData = await Weather.findAll({
        where: {
          latitude: {
            [sequelize.Op.between]: [parseFloat(latitude) - 0.1, parseFloat(latitude) + 0.1]
          },
          longitude: {
            [sequelize.Op.between]: [parseFloat(longitude) - 0.1, parseFloat(longitude) + 0.1]
          },
          timestamp: {
            [sequelize.Op.between]: [start, end]
          }
        },
        order: [['timestamp', 'ASC']]
      });
    }

    // Create a map of dates to weather data
    const weatherByDate = {};
    weatherData.forEach(weather => {
      const date = weather.timestamp.toISOString().split('T')[0];
      if (!weatherByDate[date]) {
        weatherByDate[date] = {
          rainfall: 0,
          temperature: 0,
          count: 0
        };
      }

      // Add rainfall and temperature data
      if (weather.precipitation !== null) {
        weatherByDate[date].rainfall += parseFloat(weather.precipitation);
      }

      if (weather.temperature !== null) {
        weatherByDate[date].temperature += parseFloat(weather.temperature);
        weatherByDate[date].count++;
      }
    });

    // Calculate daily averages
    Object.keys(weatherByDate).forEach(date => {
      if (weatherByDate[date].count > 0) {
        weatherByDate[date].temperature /= weatherByDate[date].count;
      }
    });

    // Create a map of dates to soil sample data
    const samplesByDate = {};
    soilSamples.forEach(sample => {
      const date = sample.sample_date;
      samplesByDate[date] = sample;
    });

    // Generate history data for each day in the range
    const historyData = [];
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));

    // Use soil health index as base for NDVI score if available
    let baseScore = 0.5; // Default score
    if (fieldSoilData && fieldSoilData.soil_health_index) {
      baseScore = fieldSoilData.soil_health_index / 100;
    }

    for (let i = 0; i <= daysDiff; i++) {
      const currentDate = new Date(start);
      currentDate.setDate(currentDate.getDate() + i);
      const dateStr = currentDate.toISOString().split('T')[0];

      // Check if we have a soil sample for this date
      if (samplesByDate[dateStr]) {
        // If we have a soil sample, use its data to influence the NDVI score
        // In a real implementation, this would use actual soil sample results
        // For now, we'll just use the sample's existence as a positive indicator
        baseScore = Math.min(0.9, baseScore + 0.05);
      } else {
        // Simulate some natural variation in the score
        baseScore = Math.max(0.1, Math.min(0.9, baseScore + (Math.random() * 0.02 - 0.01)));
      }

      // Get weather data for this date
      const weather = weatherByDate[dateStr] || { rainfall: 0, temperature: 70 };

      historyData.push({
        fieldId,
        date: dateStr,
        ndviScore: parseFloat(baseScore.toFixed(2)),
        status: baseScore > 0.7 ? 'good' : baseScore > 0.5 ? 'warning' : 'critical',
        rainfall: parseFloat(weather.rainfall.toFixed(2)),
        temperature: parseFloat(weather.temperature.toFixed(1))
      });
    }

    res.json(historyData);
  } catch (error) {
    console.error('Error fetching field health history:', error);
    res.status(500).json({ 
      error: 'Failed to fetch field health history',
      message: error.message
    });
  }
});

// Get fertilizer recommendations for a specific field
router.get('/:fieldId/recommendations', async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field soil data
    const fieldSoilData = await FieldSoilData.findOne({
      where: { field_id: fieldId },
      order: [['updated_at', 'DESC']]
    });

    // Get the most recent soil sample for this field
    const soilSample = await SoilSample.findOne({
      where: { field_id: fieldId },
      order: [['sample_date', 'DESC']]
    });

    // Get field location data
    let latitude, longitude;
    if (field.location_data && field.location_data.center) {
      latitude = field.location_data.center.latitude;
      longitude = field.location_data.center.longitude;
    } else {
      // Default to farm location if field location is not available
      const farm = await Farm.findByPk(field.farm_id);
      if (farm && farm.location_data) {
        latitude = farm.location_data.latitude;
        longitude = farm.location_data.longitude;
      } else {
        return res.status(400).json({ error: 'Location data is missing or invalid' });
      }
    }

    // Get rainfall data for this location
    let rainfallData = null;
    try {
      // Get weather data from the Weather model
      const weatherData = await Weather.findAll({
        where: {
          latitude: {
            [sequelize.Op.between]: [parseFloat(latitude) - 0.1, parseFloat(latitude) + 0.1]
          },
          longitude: {
            [sequelize.Op.between]: [parseFloat(longitude) - 0.1, parseFloat(longitude) + 0.1]
          },
          timestamp: {
            [sequelize.Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          },
          precipitation: {
            [sequelize.Op.not]: null
          }
        },
        order: [['timestamp', 'ASC']]
      });

      if (weatherData && weatherData.length > 0) {
        // Process weather data to get rainfall information
        const historicalRainfall = weatherData.map(record => ({
          time: record.timestamp.toISOString(),
          amount: parseFloat(record.precipitation)
        }));

        // Calculate total and average rainfall
        const totalRainfall = historicalRainfall.reduce((sum, hour) => sum + hour.amount, 0);
        const averageRainfall = totalRainfall / historicalRainfall.length;

        rainfallData = {
          historical: historicalRainfall,
          total: totalRainfall,
          average: averageRainfall
        };
      }
    } catch (error) {
      console.error('Error fetching rainfall data:', error);
      // Continue without rainfall data
    }

    // Create soil data object
    const soilData = {
      soilType: fieldSoilData ? fieldSoilData.soil_type : 'Unknown',
      soilProperties: {
        ph: fieldSoilData ? 6.5 : null, // Default pH if not available
        organicMatter: fieldSoilData ? 2.5 : null, // Default organic matter if not available
        drainageClass: fieldSoilData ? fieldSoilData.drainage_class : 'Unknown',
        erosionClass: fieldSoilData ? fieldSoilData.erosion_risk : 'Unknown'
      },
      rainfall: rainfallData
    };

    // Generate recommendations based on soil data
    const recommendations = generateSoilRecommendations(soilData);

    // Add field-specific information to recommendations
    const enhancedRecommendations = recommendations.map((rec, index) => ({
      id: `rec_${Date.now()}_${index + 1}`,
      fieldId,
      ...rec,
      applicationRate: rec.type === 'pH' ? '2-3 tons/acre' : 
                      rec.type === 'Organic Matter' ? '2-4 tons/acre' : 
                      rec.type === 'Drainage' ? 'N/A' : 
                      rec.type === 'Erosion' ? 'N/A' : 
                      rec.type === 'Water' ? 'N/A' : 
                      rec.type === 'Soil Type' ? 'N/A' : 
                      '50-100 lbs/acre',
      timing: rec.type === 'pH' ? 'Pre-planting' : 
              rec.type === 'Organic Matter' ? 'Fall application' : 
              rec.type === 'Drainage' ? 'Before growing season' : 
              rec.type === 'Erosion' ? 'Immediately' : 
              rec.type === 'Water' ? 'As needed' : 
              rec.type === 'Soil Type' ? 'Ongoing' : 
              'Pre-planting'
    }));

    // If no recommendations were generated, provide some default ones
    if (enhancedRecommendations.length === 0) {
      enhancedRecommendations.push({
        id: `rec_${Date.now()}_1`,
        fieldId,
        type: 'General',
        issue: 'Insufficient soil data',
        recommendation: 'Consider conducting a comprehensive soil test to get detailed recommendations',
        priority: 'High',
        applicationRate: 'N/A',
        timing: 'As soon as possible'
      });
    }

    res.json(enhancedRecommendations);
  } catch (error) {
    console.error('Error fetching field recommendations:', error);
    res.status(500).json({ 
      error: 'Failed to fetch field recommendations',
      message: error.message
    });
  }
});

// Get soil analysis for a specific field
router.get('/:fieldId/soil-analysis', async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field soil data
    const fieldSoilData = await FieldSoilData.findOne({
      where: { field_id: fieldId },
      order: [['updated_at', 'DESC']]
    });

    // Get the most recent soil sample for this field
    const soilSample = await SoilSample.findOne({
      where: { field_id: fieldId },
      order: [['sample_date', 'DESC']]
    });

    // Get field location data
    let latitude, longitude;
    if (field.location_data && field.location_data.center) {
      latitude = field.location_data.center.latitude;
      longitude = field.location_data.center.longitude;
    } else {
      // Default to farm location if field location is not available
      const farm = await Farm.findByPk(field.farm_id);
      if (farm && farm.location_data) {
        latitude = farm.location_data.latitude;
        longitude = farm.location_data.longitude;
      } else {
        return res.status(400).json({ error: 'Location data is missing or invalid' });
      }
    }

    // Fetch soil data from external APIs
    let soilData = null;
    try {
      soilData = await fetchSoilData(latitude, longitude);
    } catch (error) {
      console.error('Error fetching soil data from external APIs:', error);
      // Continue without external soil data
    }

    // Combine data from all sources, prioritizing the most specific data
    const analysis = {
      fieldId,
      lastSampleDate: soilSample ? soilSample.sample_date : (fieldSoilData ? fieldSoilData.updated_at : new Date().toISOString()),
      ph: soilData && soilData.soilProperties.ph !== null ? soilData.soilProperties.ph : 6.5, // Default pH if not available
      organicMatter: soilData && soilData.soilProperties.organicMatter !== null ? soilData.soilProperties.organicMatter : 2.5, // Default organic matter if not available
      cec: soilData && soilData.soilProperties.cec !== null ? soilData.soilProperties.cec : 10, // Default CEC if not available
      nutrients: {
        nitrogen: 20, // Default values if not available
        phosphorus: 15,
        potassium: 120,
        calcium: 1200,
        magnesium: 80,
        sulfur: 10,
        zinc: 1.5,
        manganese: 5,
        iron: 30,
        copper: 0.5,
        boron: 0.5
      },
      textureClass: soilData && soilData.soilType ? soilData.soilType : (fieldSoilData ? fieldSoilData.soil_type : 'Unknown'),
      composition: {
        sand: soilData && soilData.soilProperties.sandContent !== undefined ? soilData.soilProperties.sandContent : 30,
        silt: soilData && soilData.soilProperties.siltContent !== undefined ? soilData.soilProperties.siltContent : 40,
        clay: soilData && soilData.soilProperties.clayContent !== undefined ? soilData.soilProperties.clayContent : 30
      },
      drainageClass: soilData && soilData.soilProperties.drainageClass ? soilData.soilProperties.drainageClass : (fieldSoilData ? fieldSoilData.drainage_class : 'Unknown'),
      erosionRisk: soilData && soilData.soilProperties.erosionClass ? soilData.soilProperties.erosionClass : (fieldSoilData ? fieldSoilData.erosion_risk : 'Unknown'),
      waterAvailability: soilData && soilData.soilProperties.availableWaterCapacity !== undefined ? soilData.soilProperties.availableWaterCapacity : (fieldSoilData ? fieldSoilData.water_availability : 'Medium'),
      soilDepth: soilData && soilData.soilProperties.soilDepth !== undefined ? soilData.soilProperties.soilDepth : 'Unknown',
      soilLimitations: soilData && soilData.soilLimitations ? soilData.soilLimitations : [],
      soilSuitability: soilData && soilData.soilSuitability ? soilData.soilSuitability : {
        cropProduction: 'Unknown',
        grazing: 'Unknown',
        irrigation: 'Unknown'
      }
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error fetching soil analysis:', error);
    res.status(500).json({ 
      error: 'Failed to fetch soil analysis',
      message: error.message
    });
  }
});

export default router;
