import express from 'express';
import { authenticateUser } from '../middleware/auth.js';
import PasswordGroupController from '../controllers/PasswordGroupController.js';
import PasswordController from '../controllers/PasswordController.js';
import PasswordGroupPermissionController from '../controllers/PasswordGroupPermissionController.js';
import RecoveryKeyController from '../controllers/RecoveryKeyController.js';

const router = express.Router();

// All routes require authentication
router.use(authenticateUser);

// Password Group routes
router.get('/farms/:farmId/password-groups', PasswordGroupController.getPasswordGroups);
router.post('/farms/:farmId/password-groups', PasswordGroupController.createPasswordGroup);
router.get('/password-groups/:id', PasswordGroupController.getPasswordGroup);
router.put('/password-groups/:id', PasswordGroupController.updatePasswordGroup);
router.delete('/password-groups/:id', PasswordGroupController.deletePasswordGroup);

// Password routes
router.get('/password-groups/:groupId/passwords', PasswordController.getPasswords);
router.post('/password-groups/:groupId/passwords', PasswordController.createPassword);
router.get('/passwords/:id', PasswordController.getPassword);
router.put('/passwords/:id', PasswordController.updatePassword);
router.delete('/passwords/:id', PasswordController.deletePassword);

// Password Group Permission routes
router.get('/password-groups/:groupId/permissions', PasswordGroupPermissionController.getPermissions);
router.post('/password-groups/:groupId/permissions', PasswordGroupPermissionController.addPermission);
router.put('/permissions/:id', PasswordGroupPermissionController.updatePermission);
router.delete('/permissions/:id', PasswordGroupPermissionController.deletePermission);

// Recovery Key routes
router.post('/recovery-keys', RecoveryKeyController.generateRecoveryKey);
router.get('/recovery-keys', RecoveryKeyController.hasRecoveryKey);
router.post('/recovery-keys/recover', RecoveryKeyController.recoverAccess);
router.delete('/recovery-keys', RecoveryKeyController.deleteRecoveryKey);

export default router;