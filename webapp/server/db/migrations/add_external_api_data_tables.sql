-- Migration to add external API data tables
-- This migration creates tables for storing external API data in an organized way
-- to provide easier caching, more reliable results, and enable analysis of API data

-- Enable the uuid-ossp extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Create the api_providers table to store information about different API providers

CREATE TABLE IF NOT EXISTS site.api_providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    base_url VARCHAR(255),
    requires_key BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.api_providers IS 'Stores information about external API providers';
COMMENT ON COLUMN site.api_providers.id IS 'Unique identifier for the API provider';
COMMENT ON COLUMN site.api_providers.name IS 'Name of the API provider (e.g., National Weather Service, OpenWeatherMap)';
COMMENT ON COLUMN site.api_providers.category IS 'Category of the API (e.g., Weather, Financial, Agricultural)';
COMMENT ON COLUMN site.api_providers.base_url IS 'Base URL for the API';
COMMENT ON COLUMN site.api_providers.requires_key IS 'Whether the API requires an API key';
COMMENT ON COLUMN site.api_providers.description IS 'Description of the API provider and its services';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_providers_name ON site.api_providers(name);
CREATE INDEX IF NOT EXISTS idx_api_providers_category ON site.api_providers(category);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_api_providers_timestamp
BEFORE UPDATE ON site.api_providers
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Step 2: Create the api_endpoints table to store information about specific API endpoints

CREATE TABLE IF NOT EXISTS site.api_endpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider_id UUID NOT NULL REFERENCES site.api_providers(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    path VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL DEFAULT 'GET',
    description TEXT,
    request_format JSONB,
    response_format JSONB,
    cache_duration_seconds INTEGER DEFAULT 3600,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.api_endpoints IS 'Stores information about specific API endpoints';
COMMENT ON COLUMN site.api_endpoints.id IS 'Unique identifier for the API endpoint';
COMMENT ON COLUMN site.api_endpoints.provider_id IS 'ID of the API provider this endpoint belongs to';
COMMENT ON COLUMN site.api_endpoints.name IS 'Name of the API endpoint (e.g., Current Weather, Forecast)';
COMMENT ON COLUMN site.api_endpoints.path IS 'Path for the API endpoint';
COMMENT ON COLUMN site.api_endpoints.method IS 'HTTP method for the API endpoint (GET, POST, etc.)';
COMMENT ON COLUMN site.api_endpoints.description IS 'Description of the API endpoint and its purpose';
COMMENT ON COLUMN site.api_endpoints.request_format IS 'JSON schema for the request format';
COMMENT ON COLUMN site.api_endpoints.response_format IS 'JSON schema for the response format';
COMMENT ON COLUMN site.api_endpoints.cache_duration_seconds IS 'Default cache duration in seconds for this endpoint';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_endpoints_provider_id ON site.api_endpoints(provider_id);
CREATE INDEX IF NOT EXISTS idx_api_endpoints_name ON site.api_endpoints(name);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_api_endpoints_timestamp
BEFORE UPDATE ON site.api_endpoints
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Step 3: Create the api_requests table to store information about API requests

CREATE TABLE IF NOT EXISTS site.api_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint_id UUID NOT NULL REFERENCES site.api_endpoints(id) ON DELETE CASCADE,
    farm_id UUID REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
    user_id UUID REFERENCES site.users(id) ON DELETE SET NULL,
    request_params JSONB NOT NULL,
    request_headers JSONB,
    request_body JSONB,
    response_status INTEGER,
    response_headers JSONB,
    response_body JSONB,
    error_message TEXT,
    request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    response_time TIMESTAMP,
    duration_ms INTEGER,
    cache_hit BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.api_requests IS 'Stores information about API requests and responses';
COMMENT ON COLUMN site.api_requests.id IS 'Unique identifier for the API request';
COMMENT ON COLUMN site.api_requests.endpoint_id IS 'ID of the API endpoint this request was made to';
COMMENT ON COLUMN site.api_requests.farm_id IS 'ID of the farm this request is related to (if applicable)';
COMMENT ON COLUMN site.api_requests.field_id IS 'ID of the field this request is related to (if applicable)';
COMMENT ON COLUMN site.api_requests.user_id IS 'ID of the user who made the request (if applicable)';
COMMENT ON COLUMN site.api_requests.request_params IS 'Query parameters for the request';
COMMENT ON COLUMN site.api_requests.request_headers IS 'Headers for the request';
COMMENT ON COLUMN site.api_requests.request_body IS 'Body of the request (for POST, PUT, etc.)';
COMMENT ON COLUMN site.api_requests.response_status IS 'HTTP status code of the response';
COMMENT ON COLUMN site.api_requests.response_headers IS 'Headers of the response';
COMMENT ON COLUMN site.api_requests.response_body IS 'Body of the response';
COMMENT ON COLUMN site.api_requests.error_message IS 'Error message if the request failed';
COMMENT ON COLUMN site.api_requests.request_time IS 'Time when the request was made';
COMMENT ON COLUMN site.api_requests.response_time IS 'Time when the response was received';
COMMENT ON COLUMN site.api_requests.duration_ms IS 'Duration of the request in milliseconds';
COMMENT ON COLUMN site.api_requests.cache_hit IS 'Whether the response was served from cache';
COMMENT ON COLUMN site.api_requests.expires_at IS 'Time when the cached response expires';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_requests_endpoint_id ON site.api_requests(endpoint_id);
CREATE INDEX IF NOT EXISTS idx_api_requests_farm_id ON site.api_requests(farm_id);
CREATE INDEX IF NOT EXISTS idx_api_requests_field_id ON site.api_requests(field_id);
CREATE INDEX IF NOT EXISTS idx_api_requests_user_id ON site.api_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_api_requests_request_time ON site.api_requests(request_time);
CREATE INDEX IF NOT EXISTS idx_api_requests_expires_at ON site.api_requests(expires_at);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_api_requests_timestamp
BEFORE UPDATE ON site.api_requests
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Step 4: Create the api_cache table to store cached API responses

CREATE TABLE IF NOT EXISTS site.api_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint_id UUID NOT NULL REFERENCES site.api_endpoints(id) ON DELETE CASCADE,
    cache_key VARCHAR(255) NOT NULL,
    request_params JSONB NOT NULL,
    response_body JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.api_cache IS 'Stores cached API responses';
COMMENT ON COLUMN site.api_cache.id IS 'Unique identifier for the cached response';
COMMENT ON COLUMN site.api_cache.endpoint_id IS 'ID of the API endpoint this cache is for';
COMMENT ON COLUMN site.api_cache.cache_key IS 'Unique key for the cached response';
COMMENT ON COLUMN site.api_cache.request_params IS 'Query parameters for the request';
COMMENT ON COLUMN site.api_cache.response_body IS 'Body of the response';
COMMENT ON COLUMN site.api_cache.created_at IS 'Time when the cache entry was created';
COMMENT ON COLUMN site.api_cache.expires_at IS 'Time when the cache entry expires';
COMMENT ON COLUMN site.api_cache.last_accessed_at IS 'Time when the cache entry was last accessed';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_cache_endpoint_id ON site.api_cache(endpoint_id);
CREATE INDEX IF NOT EXISTS idx_api_cache_cache_key ON site.api_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_api_cache_expires_at ON site.api_cache(expires_at);

-- Step 5: Create the api_analytics table to store analytics data for API usage

CREATE TABLE IF NOT EXISTS site.api_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider_id UUID NOT NULL REFERENCES site.api_providers(id) ON DELETE CASCADE,
    endpoint_id UUID REFERENCES site.api_endpoints(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    request_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    cache_hit_count INTEGER DEFAULT 0,
    avg_response_time_ms INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.api_analytics IS 'Stores analytics data for API usage';
COMMENT ON COLUMN site.api_analytics.id IS 'Unique identifier for the analytics entry';
COMMENT ON COLUMN site.api_analytics.provider_id IS 'ID of the API provider';
COMMENT ON COLUMN site.api_analytics.endpoint_id IS 'ID of the API endpoint (if applicable)';
COMMENT ON COLUMN site.api_analytics.date IS 'Date for the analytics data';
COMMENT ON COLUMN site.api_analytics.request_count IS 'Number of requests made';
COMMENT ON COLUMN site.api_analytics.error_count IS 'Number of requests that resulted in errors';
COMMENT ON COLUMN site.api_analytics.cache_hit_count IS 'Number of requests served from cache';
COMMENT ON COLUMN site.api_analytics.avg_response_time_ms IS 'Average response time in milliseconds';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_analytics_provider_id ON site.api_analytics(provider_id);
CREATE INDEX IF NOT EXISTS idx_api_analytics_endpoint_id ON site.api_analytics(endpoint_id);
CREATE INDEX IF NOT EXISTS idx_api_analytics_date ON site.api_analytics(date);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_api_analytics_timestamp
BEFORE UPDATE ON site.api_analytics
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Step 6: Insert initial data for known API providers

-- Weather API providers
INSERT INTO site.api_providers (id, name, category, base_url, requires_key, description)
VALUES 
(uuid_generate_v4(), 'National Weather Service', 'Weather', 'https://api.weather.gov', FALSE, 'US National Weather Service API for weather data'),
(uuid_generate_v4(), 'OpenWeatherMap', 'Weather', 'https://api.openweathermap.org', TRUE, 'OpenWeatherMap API for global weather data'),
(uuid_generate_v4(), 'Tomorrow.io', 'Weather', 'https://api.tomorrow.io', TRUE, 'Tomorrow.io API for weather data and forecasts'),
(uuid_generate_v4(), 'Open-Meteo', 'Weather', 'https://api.open-meteo.com', FALSE, 'Open-Meteo API for weather forecasts');

-- Financial API providers
INSERT INTO site.api_providers (id, name, category, base_url, requires_key, description)
VALUES 
(uuid_generate_v4(), 'QuickBooks', 'Financial', 'https://quickbooks.api.intuit.com', TRUE, 'QuickBooks API for accounting data');

-- Agricultural API providers
INSERT INTO site.api_providers (id, name, category, base_url, requires_key, description)
VALUES 
(uuid_generate_v4(), 'Grants.gov', 'Agricultural', 'https://www.grants.gov/grantsws/rest', TRUE, 'Grants.gov API for agricultural grants'),
(uuid_generate_v4(), 'Farmers.gov', 'Agricultural', 'https://www.farmers.gov/api', TRUE, 'Farmers.gov API for USDA programs'),
(uuid_generate_v4(), 'USDA ARMS', 'Agricultural', 'https://api.ers.usda.gov/arms', TRUE, 'USDA Agricultural Resource Management Survey API'),
(uuid_generate_v4(), 'Farm Service Agency', 'Agricultural', 'https://api.fsa.usda.gov', TRUE, 'USDA Farm Service Agency API'),
(uuid_generate_v4(), 'Rural Development', 'Agricultural', 'https://api.rd.usda.gov/v1', TRUE, 'USDA Rural Development API'),
(uuid_generate_v4(), 'NRCS', 'Agricultural', 'https://api.nrcs.usda.gov/v1', TRUE, 'USDA Natural Resources Conservation Service API'),
(uuid_generate_v4(), 'NIFA', 'Agricultural', 'https://api.nifa.usda.gov/v1', TRUE, 'USDA National Institute of Food and Agriculture API'),
(uuid_generate_v4(), 'RMA', 'Agricultural', 'https://api.rma.usda.gov/v1', TRUE, 'USDA Risk Management Agency API'),
(uuid_generate_v4(), 'AMS', 'Agricultural', 'https://api.ams.usda.gov/v1', TRUE, 'USDA Agricultural Marketing Service API'),
(uuid_generate_v4(), 'Data.gov', 'Agricultural', 'https://api.data.gov', TRUE, 'Data.gov API for government data'),
(uuid_generate_v4(), 'USDA NRCS Soil', 'Agricultural', 'https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', FALSE, 'USDA NRCS Soil Data Access API');

-- Maps and Location API providers
INSERT INTO site.api_providers (id, name, category, base_url, requires_key, description)
VALUES 
(uuid_generate_v4(), 'Google Maps', 'Maps', 'https://maps.googleapis.com', TRUE, 'Google Maps API for mapping and location services');

-- Storage API providers
INSERT INTO site.api_providers (id, name, category, base_url, requires_key, description)
VALUES 
(uuid_generate_v4(), 'Google Drive', 'Storage', 'https://www.googleapis.com/drive', TRUE, 'Google Drive API for file storage'),
(uuid_generate_v4(), 'Dropbox', 'Storage', 'https://api.dropboxapi.com', TRUE, 'Dropbox API for file storage'),
(uuid_generate_v4(), 'Digital Ocean Spaces', 'Storage', 'https://nyc3.digitaloceanspaces.com', TRUE, 'Digital Ocean Spaces API for object storage');

-- Other API providers
INSERT INTO site.api_providers (id, name, category, base_url, requires_key, description)
VALUES 
(uuid_generate_v4(), 'OpenAI', 'AI', 'https://api.openai.com', TRUE, 'OpenAI API for AI services'),
(uuid_generate_v4(), 'Twilio', 'Communication', 'https://api.twilio.com', TRUE, 'Twilio API for communication services');

-- Step 7: Insert initial data for common API endpoints

-- National Weather Service API endpoints
INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Points', '/points/{lat},{lon}', 'GET', 'Get grid points for a location', 86400
FROM site.api_providers WHERE name = 'National Weather Service';

INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Forecast', '/gridpoints/{gridId}/{gridX},{gridY}/forecast', 'GET', 'Get forecast for a grid point', 3600
FROM site.api_providers WHERE name = 'National Weather Service';

INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Hourly Forecast', '/gridpoints/{gridId}/{gridX},{gridY}/forecast/hourly', 'GET', 'Get hourly forecast for a grid point', 1800
FROM site.api_providers WHERE name = 'National Weather Service';

-- OpenWeatherMap API endpoints
INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Current Weather', '/data/2.5/weather', 'GET', 'Get current weather data', 1800
FROM site.api_providers WHERE name = 'OpenWeatherMap';

INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Forecast', '/data/2.5/forecast', 'GET', 'Get 5-day forecast data', 3600
FROM site.api_providers WHERE name = 'OpenWeatherMap';

-- Open-Meteo API endpoints
INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Forecast', '/forecast', 'GET', 'Get weather forecast data', 3600
FROM site.api_providers WHERE name = 'Open-Meteo';

-- USDA NRCS Soil API endpoints
INSERT INTO site.api_endpoints (id, provider_id, name, path, method, description, cache_duration_seconds)
SELECT uuid_generate_v4(), id, 'Soil Data', '/Tabular/SDMTabularService.asmx', 'POST', 'Get soil data for a location', 604800
FROM site.api_providers WHERE name = 'USDA NRCS Soil';
