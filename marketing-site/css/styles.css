/* nxtAcre Marketing Website Styles */

/* Global Styles */
:root {
    --primary-color: #0ea5e9;
    --secondary-color: #0284c7;
    --accent-color: #38bdf8;
    --text-color: #333333;
    --light-color: #F8F9FA;
    --dark-color: #212529;
    --gray-color: #6C757D;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    overflow-x: hidden;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.navbar-brand img {
    max-height: 40px;
}

/* Hero Section */
.hero-section {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section h1 {
    color: var(--dark-color);
    font-weight: 700;
}

.hero-section .lead {
    font-size: 1.25rem;
    color: var(--gray-color);
}

.hero-section img {
    max-width: 100%;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Features Section */
#features {
    padding: 80px 0;
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(14, 165, 233, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.feature-icon i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

/* Testimonials Section */
.testimonial-img {
    width: 60px;
    height: 60px;
    object-fit: cover;
}

/* CTA Section */
.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 60px 0 30px;
}

footer h5 {
    font-weight: 600;
    margin-bottom: 20px;
}

footer ul li {
    margin-bottom: 10px;
}

.social-icons a {
    color: white;
    font-size: 1.5rem;
    margin-right: 15px;
}

.social-icons a:hover {
    color: var(--accent-color);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .hero-section {
        padding: 100px 0 60px;
        text-align: center;
    }

    .hero-section img {
        margin-top: 40px;
    }

    .cta-section {
        text-align: center;
    }

    .cta-section .btn {
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .navbar .btn {
        margin-top: 10px;
    }

    .navbar-nav {
        margin-bottom: 15px;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }
}

/* Animation Enhancements */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
