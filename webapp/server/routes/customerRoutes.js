import express from 'express';
import {
  getFarmCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  subscribeToPhoneBook,
  unsubscribeFromPhoneBook,
  syncCustomerWithPhoneBook,
  handlePhoneBookWebhook,
  syncAllCustomers
} from '../controllers/customerController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all customers for a farm
router.get('/farm/:farmId', authenticate, getFarmCustomers);

// Get a single customer by ID
router.get('/:customerId', authenticate, getCustomerById);

// Create a new customer
router.post('/', authenticate, createCustomer);

// Update a customer
router.put('/:customerId', authenticate, updateCustomer);

// Delete a customer
router.delete('/:customerId', authenticate, deleteCustomer);

// Phone book integration routes

// Subscribe a customer to phone book updates
router.post('/:customerId/phone-book/subscribe', authenticate, subscribeToPhoneBook);

// Unsubscribe a customer from phone book updates
router.post('/:customerId/phone-book/unsubscribe', authenticate, unsubscribeFromPhoneBook);

// Sync a customer with phone books
router.post('/:customerId/phone-book/sync', authenticate, syncCustomerWithPhoneBook);

// Sync all subscribed customers with phone books
router.post('/phone-book/sync-all', authenticate, syncAllCustomers);

// Webhook for phone book updates (two-way sync)
router.post('/phone-book/webhook', handlePhoneBookWebhook);

export default router;
