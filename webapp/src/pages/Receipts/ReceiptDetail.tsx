import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import Layout from '../../components/Layout';
import { 
  getReceiptById, 
  downloadReceipt, 
  deleteReceipt, 
  formatDate, 
  Receipt,
  processReceiptWithOcr,
  categorizeReceipt,
  matchReceiptToTransactions
} from '../../services/receiptService';

const ReceiptDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [receipt, setReceipt] = useState<Receipt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

  // States for new features
  const [ocrLoading, setOcrLoading] = useState(false);
  const [ocrResults, setOcrResults] = useState<any>(null);
  const [ocrModalOpen, setOcrModalOpen] = useState(false);

  const [categorizationLoading, setCategorizationLoading] = useState(false);
  const [categorizationResults, setCategorizationResults] = useState<any>(null);
  const [categorizationModalOpen, setCategorizationModalOpen] = useState(false);

  const [matchingLoading, setMatchingLoading] = useState(false);
  const [matchingResults, setMatchingResults] = useState<any>(null);
  const [matchingModalOpen, setMatchingModalOpen] = useState(false);

  useEffect(() => {
    const fetchReceipt = async () => {
      setLoading(true);
      try {
        if (!id) {
          setError('Receipt ID is required');
          setLoading(false);
          return;
        }

        const data = await getReceiptById(id);
        setReceipt(data);
      } catch (err: any) {
        console.error('Error fetching receipt:', err);
        setError(err.response?.data?.error || 'Failed to load receipt. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchReceipt();
  }, [id]);

  const handleDownload = () => {
    if (receipt?.file_path) {
      downloadReceipt(receipt.id);
    } else {
      setError('This receipt does not have an attached file.');
    }
  };

  const handleDelete = async () => {
    try {
      if (!receipt) return;

      await deleteReceipt(receipt.id);
      navigate('/receipts');
    } catch (err: any) {
      console.error('Error deleting receipt:', err);
      setError(err.response?.data?.error || 'Failed to delete receipt. Please try again later.');
    }
  };

  // Handler for OCR processing
  const handleOcrProcessing = async () => {
    if (!receipt) return;

    setOcrLoading(true);
    setError(null);

    try {
      const result = await processReceiptWithOcr(receipt.id);
      setOcrResults(result);
      setOcrModalOpen(true);

      // Refresh receipt data to show updated values
      const updatedReceipt = await getReceiptById(receipt.id);
      setReceipt(updatedReceipt);
    } catch (err: any) {
      console.error('Error processing OCR:', err);
      setError(err.response?.data?.error || 'Failed to process OCR. Please try again later.');
    } finally {
      setOcrLoading(false);
    }
  };

  // Handler for receipt categorization
  const handleCategorization = async () => {
    if (!receipt) return;

    setCategorizationLoading(true);
    setError(null);

    try {
      const result = await categorizeReceipt(receipt.id);
      setCategorizationResults(result);
      setCategorizationModalOpen(true);

      // Refresh receipt data to show updated values
      const updatedReceipt = await getReceiptById(receipt.id);
      setReceipt(updatedReceipt);
    } catch (err: any) {
      console.error('Error categorizing receipt:', err);
      setError(err.response?.data?.error || 'Failed to categorize receipt. Please try again later.');
    } finally {
      setCategorizationLoading(false);
    }
  };

  // Handler for receipt matching
  const handleMatching = async () => {
    if (!receipt) return;

    setMatchingLoading(true);
    setError(null);

    try {
      const result = await matchReceiptToTransactions(receipt.id);
      setMatchingResults(result);
      setMatchingModalOpen(true);
    } catch (err: any) {
      console.error('Error matching receipt:', err);
      setError(err.response?.data?.error || 'Failed to match receipt. Please try again later.');
    } finally {
      setMatchingLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-600">Loading receipt details...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
        <div className="flex justify-center">
          <Link
            to="/receipts"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
          >
            Back to Receipts
          </Link>
        </div>
      </Layout>
    );
  }

  if (!receipt) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <p>Receipt not found</p>
        </div>
        <div className="flex justify-center">
          <Link
            to="/receipts"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
          >
            Back to Receipts
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Receipt Details</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              {receipt.receipt_number ? `Receipt #${receipt.receipt_number}` : 'Receipt Information'}
            </p>
          </div>
          <div className="flex space-x-2">
            {receipt.file_path && (
              <button
                onClick={handleDownload}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Download
              </button>
            )}
            <Link
              to={`/receipts/edit/${receipt.id}`}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Edit
            </Link>
            <button
              onClick={() => setDeleteConfirmOpen(true)}
              className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Delete
            </button>
          </div>

          {/* Advanced Receipt Processing Tools */}
          <div className="mt-4 flex flex-wrap gap-2">
            <button
              onClick={handleOcrProcessing}
              disabled={ocrLoading || !receipt.file_path}
              className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md ${
                ocrLoading || !receipt.file_path
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
              }`}
            >
              {ocrLoading ? 'Processing...' : 'Enhanced OCR'}
            </button>

            <button
              onClick={handleCategorization}
              disabled={categorizationLoading || !receipt.vendor_name}
              className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md ${
                categorizationLoading || !receipt.vendor_name
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
              }`}
            >
              {categorizationLoading ? 'Categorizing...' : 'AI Categorization'}
            </button>

            <button
              onClick={handleMatching}
              disabled={matchingLoading || !receipt.amount || !receipt.receipt_date}
              className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md ${
                matchingLoading || !receipt.amount || !receipt.receipt_date
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
              }`}
            >
              {matchingLoading ? 'Matching...' : 'Match Transactions'}
            </button>
          </div>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Vendor</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {receipt.vendor_name || 'Unknown Vendor'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Amount</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {receipt.amount ? `$${receipt.amount.toFixed(2)} ${receipt.currency}` : 'N/A'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Receipt Date</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(receipt.receipt_date)}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  receipt.status === 'approved' ? 'bg-green-100 text-green-800' :
                  receipt.status === 'rejected' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}
                </span>
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {receipt.description || 'No description provided'}
              </dd>
            </div>
            {receipt.email_source && (
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Email Source</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {receipt.email_source}
                </dd>
              </div>
            )}
            {receipt.email_subject && (
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Email Subject</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {receipt.email_subject}
                </dd>
              </div>
            )}
            {receipt.email_received_at && (
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Email Received</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {formatDate(receipt.email_received_at)}
                </dd>
              </div>
            )}
            {receipt.file_path && (
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Attachment</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div className="flex items-center">
                    <button
                      onClick={handleDownload}
                      className="text-primary-600 hover:text-primary-900"
                    >
                      Download Receipt File
                    </button>
                  </div>
                </dd>
              </div>
            )}
            {receipt.expense && (
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Linked Expense</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <Link 
                    to={`/expenses/${receipt.expense.id}`} 
                    className="text-primary-600 hover:text-primary-900"
                  >
                    {receipt.expense.description || `Expense #${receipt.expense.id}`} - ${receipt.expense.amount.toFixed(2)}
                  </Link>
                </dd>
              </div>
            )}
          </dl>
        </div>
      </div>

      <div className="mt-6">
        <Link
          to="/receipts"
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Receipts
        </Link>
      </div>

      {/* Delete confirmation modal */}
      {deleteConfirmOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Delete Receipt
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this receipt? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setDeleteConfirmOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* OCR Results Modal */}
      {ocrModalOpen && ocrResults && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      OCR Results
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-2">
                        Enhanced OCR processing completed with {ocrResults.ocr_results.confidence_score * 100}% confidence.
                      </p>

                      <div className="mt-4 border border-gray-200 rounded-md p-4">
                        <h4 className="text-md font-medium text-gray-700 mb-2">Extracted Data</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Vendor:</span>
                            <span className="text-sm font-medium">{ocrResults.ocr_results.vendor_name}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Amount:</span>
                            <span className="text-sm font-medium">${ocrResults.ocr_results.total.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Receipt #:</span>
                            <span className="text-sm font-medium">{ocrResults.ocr_results.receipt_number}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Date:</span>
                            <span className="text-sm font-medium">{new Date(ocrResults.ocr_results.receipt_date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Tax:</span>
                            <span className="text-sm font-medium">${ocrResults.ocr_results.tax.toFixed(2)}</span>
                          </div>
                        </div>

                        <h4 className="text-md font-medium text-gray-700 mt-4 mb-2">Line Items</h4>
                        <div className="space-y-2">
                          {ocrResults.ocr_results.items.map((item, index) => (
                            <div key={index} className="flex justify-between">
                              <span className="text-sm text-gray-500">{item.description}</span>
                              <span className="text-sm font-medium">${item.price.toFixed(2)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setOcrModalOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Categorization Results Modal */}
      {categorizationModalOpen && categorizationResults && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Receipt Categorization
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-2">
                        AI-based categorization completed with {categorizationResults.categorization.confidence * 100}% confidence.
                      </p>

                      <div className="mt-4 border border-gray-200 rounded-md p-4">
                        <h4 className="text-md font-medium text-gray-700 mb-2">Category Information</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Main Category:</span>
                            <span className="text-sm font-medium">{categorizationResults.categorization.category}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Subcategory:</span>
                            <span className="text-sm font-medium">{categorizationResults.categorization.subcategory}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Confidence:</span>
                            <span className="text-sm font-medium">{(categorizationResults.categorization.confidence * 100).toFixed(1)}%</span>
                          </div>
                        </div>

                        <div className="mt-4 bg-yellow-50 border border-yellow-100 rounded-md p-3">
                          <p className="text-sm text-yellow-800">
                            The receipt has been updated with category information. This will help with expense reporting and analysis.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setCategorizationModalOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Transaction Matching Modal */}
      {matchingModalOpen && matchingResults && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Transaction Matching
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-2">
                        {matchingResults.matches.length > 0 
                          ? `Found ${matchingResults.matches.length} potential matching transactions.` 
                          : 'No matching transactions found.'}
                      </p>

                      {matchingResults.matches.length > 0 && (
                        <div className="mt-4 border border-gray-200 rounded-md p-4">
                          <h4 className="text-md font-medium text-gray-700 mb-2">Potential Matches</h4>
                          <div className="space-y-4">
                            {matchingResults.matches.map((match, index) => (
                              <div 
                                key={index} 
                                className={`p-3 rounded-md ${match.is_best_match 
                                  ? 'bg-green-50 border border-green-100' 
                                  : 'bg-gray-50 border border-gray-100'}`}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <p className="text-sm font-medium">
                                      {match.transaction.description || 'Transaction'}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {new Date(match.transaction.date).toLocaleDateString()}
                                    </p>
                                  </div>
                                  <div className="text-right">
                                    <p className="text-sm font-medium">
                                      ${match.transaction.amount.toFixed(2)}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      Match score: {(match.score * 100).toFixed(1)}%
                                    </p>
                                  </div>
                                </div>
                                {match.is_best_match && (
                                  <div className="mt-2 flex items-center text-sm text-green-600">
                                    <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    Best match
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {matchingResults.matches.length === 0 && (
                        <div className="mt-4 bg-yellow-50 border border-yellow-100 rounded-md p-3">
                          <p className="text-sm text-yellow-800">
                            No matching transactions found. This could be because the transaction hasn't been imported yet or the amount/date doesn't match any existing transactions.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setMatchingModalOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default ReceiptDetail;
