import axios from 'axios';
import dotenv from 'dotenv';
import { sequelize } from '../config/database.js';

dotenv.config();

// NCEI API base URLs
const NCEI_DATA_SERVICE_URL = process.env.NCEI_DATA_SERVICE_URL || 'https://www.ncei.noaa.gov/access/services/data/v1';
const NCEI_SEARCH_SERVICE_URL = process.env.NCEI_SEARCH_SERVICE_URL || 'https://www.ncei.noaa.gov/access/services/search/v1';
const NCEI_TOKEN = process.env.NCEI_TOKEN; // Optional token if required

// Common datasets that are useful for farm management
const DATASETS = {
  // Global Historical Climatology Network - Daily
  GHCND: 'GHCND',
  // Climate Normals
  NORMAL_DLY: 'NORMAL_DLY',
  // Global Summary of the Month
  GSOM: 'GSOM',
  // Global Summary of the Year
  GSOY: 'GSOY',
  // Storm Events Database
  STORM_EVENTS: 'STORM_EVENTS',
};

/**
 * Find the nearest weather station to a given location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @returns {Promise<Object>} - Information about the nearest station
 */
export const findNearestStation = async (latitude, longitude) => {
  try {
    // Use the NCEI API to find stations near the given coordinates
    const url = `${NCEI_DATA_SERVICE_URL}?dataset=GHCND&stations&extent=${latitude-0.5},${longitude-0.5},${latitude+0.5},${longitude+0.5}&format=json`;
    
    const response = await axios.get(url);
    
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      throw new Error('No weather stations found near the specified location');
    }
    
    // Calculate distance to each station and find the closest one
    const stations = response.data.map(station => {
      const stationLat = parseFloat(station.latitude);
      const stationLon = parseFloat(station.longitude);
      
      // Simple distance calculation (Haversine formula would be more accurate)
      const distance = Math.sqrt(
        Math.pow(stationLat - latitude, 2) + 
        Math.pow(stationLon - longitude, 2)
      );
      
      return {
        ...station,
        distance
      };
    });
    
    // Sort by distance and return the closest station
    stations.sort((a, b) => a.distance - b.distance);
    
    return stations[0];
  } catch (error) {
    console.error('Error finding nearest weather station:', error);
    throw new Error(`Failed to find nearest weather station: ${error.message}`);
  }
};

/**
 * Get historical temperature data for a location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Historical temperature data
 */
export const getHistoricalTemperatureData = async (latitude, longitude, startDate, endDate) => {
  try {
    // Find the nearest weather station
    const station = await findNearestStation(latitude, longitude);
    
    // Fetch temperature data from the station
    const url = `${NCEI_DATA_SERVICE_URL}?dataset=${DATASETS.GHCND}&stations=${station.id}&startDate=${startDate}&endDate=${endDate}&dataTypes=TMAX,TMIN,TAVG&units=standard&format=json`;
    
    const response = await axios.get(url);
    
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      throw new Error('No temperature data available for the specified period');
    }
    
    // Process and format the temperature data
    const temperatureData = response.data.map(record => {
      return {
        date: record.date,
        station: record.station,
        max_temp: record.TMAX !== undefined ? (record.TMAX / 10) * 9/5 + 32 : null, // Convert from tenths of Celsius to Fahrenheit
        min_temp: record.TMIN !== undefined ? (record.TMIN / 10) * 9/5 + 32 : null,
        avg_temp: record.TAVG !== undefined ? (record.TAVG / 10) * 9/5 + 32 : null,
      };
    });
    
    return {
      station: {
        id: station.id,
        name: station.name,
        latitude: station.latitude,
        longitude: station.longitude,
        elevation: station.elevation,
        distance: station.distance
      },
      data: temperatureData
    };
  } catch (error) {
    console.error('Error fetching historical temperature data:', error);
    throw new Error(`Failed to fetch historical temperature data: ${error.message}`);
  }
};

/**
 * Get historical precipitation data for a location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Historical precipitation data
 */
export const getHistoricalPrecipitationData = async (latitude, longitude, startDate, endDate) => {
  try {
    // Find the nearest weather station
    const station = await findNearestStation(latitude, longitude);
    
    // Fetch precipitation data from the station
    const url = `${NCEI_DATA_SERVICE_URL}?dataset=${DATASETS.GHCND}&stations=${station.id}&startDate=${startDate}&endDate=${endDate}&dataTypes=PRCP&units=standard&format=json`;
    
    const response = await axios.get(url);
    
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      throw new Error('No precipitation data available for the specified period');
    }
    
    // Process and format the precipitation data
    const precipitationData = response.data.map(record => {
      return {
        date: record.date,
        station: record.station,
        precipitation: record.PRCP !== undefined ? record.PRCP / 10 * 0.0393701 : null, // Convert from tenths of mm to inches
      };
    });
    
    return {
      station: {
        id: station.id,
        name: station.name,
        latitude: station.latitude,
        longitude: station.longitude,
        elevation: station.elevation,
        distance: station.distance
      },
      data: precipitationData
    };
  } catch (error) {
    console.error('Error fetching historical precipitation data:', error);
    throw new Error(`Failed to fetch historical precipitation data: ${error.message}`);
  }
};

/**
 * Get climate normals for a location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @returns {Promise<Object>} - Climate normals data
 */
export const getClimateNormals = async (latitude, longitude) => {
  try {
    // Find the nearest weather station that has normals data
    const station = await findNearestStation(latitude, longitude);
    
    // Fetch climate normals data from the station
    const url = `${NCEI_DATA_SERVICE_URL}?dataset=${DATASETS.NORMAL_DLY}&stations=${station.id}&format=json`;
    
    const response = await axios.get(url);
    
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      throw new Error('No climate normals data available for the specified location');
    }
    
    // Process and format the climate normals data
    // Group by month for easier consumption
    const normalsByMonth = {};
    
    response.data.forEach(record => {
      const month = parseInt(record.date.substring(4, 6));
      const day = parseInt(record.date.substring(6, 8));
      
      if (!normalsByMonth[month]) {
        normalsByMonth[month] = [];
      }
      
      normalsByMonth[month].push({
        day,
        max_temp: record.DLY_TMAX_NORMAL !== undefined ? parseFloat(record.DLY_TMAX_NORMAL) : null,
        min_temp: record.DLY_TMIN_NORMAL !== undefined ? parseFloat(record.DLY_TMIN_NORMAL) : null,
        avg_temp: record.DLY_TAVG_NORMAL !== undefined ? parseFloat(record.DLY_TAVG_NORMAL) : null,
        precipitation: record.DLY_PRCP_NORMAL !== undefined ? parseFloat(record.DLY_PRCP_NORMAL) : null,
        heating_degree_days: record.DLY_HTDD_NORMAL !== undefined ? parseFloat(record.DLY_HTDD_NORMAL) : null,
        cooling_degree_days: record.DLY_CLDD_NORMAL !== undefined ? parseFloat(record.DLY_CLDD_NORMAL) : null,
      });
    });
    
    // Calculate monthly averages
    const monthlyAverages = {};
    
    Object.keys(normalsByMonth).forEach(month => {
      const days = normalsByMonth[month];
      
      const maxTemps = days.map(d => d.max_temp).filter(t => t !== null);
      const minTemps = days.map(d => d.min_temp).filter(t => t !== null);
      const avgTemps = days.map(d => d.avg_temp).filter(t => t !== null);
      const precips = days.map(d => d.precipitation).filter(p => p !== null);
      
      monthlyAverages[month] = {
        max_temp: maxTemps.length > 0 ? maxTemps.reduce((a, b) => a + b, 0) / maxTemps.length : null,
        min_temp: minTemps.length > 0 ? minTemps.reduce((a, b) => a + b, 0) / minTemps.length : null,
        avg_temp: avgTemps.length > 0 ? avgTemps.reduce((a, b) => a + b, 0) / avgTemps.length : null,
        total_precipitation: precips.length > 0 ? precips.reduce((a, b) => a + b, 0) : null,
      };
    });
    
    return {
      station: {
        id: station.id,
        name: station.name,
        latitude: station.latitude,
        longitude: station.longitude,
        elevation: station.elevation,
        distance: station.distance
      },
      daily_normals: normalsByMonth,
      monthly_averages: monthlyAverages
    };
  } catch (error) {
    console.error('Error fetching climate normals:', error);
    throw new Error(`Failed to fetch climate normals: ${error.message}`);
  }
};

/**
 * Get extreme weather events for a location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Extreme weather events data
 */
export const getExtremeWeatherEvents = async (latitude, longitude, startDate, endDate) => {
  try {
    // For extreme weather events, we need to find the county FIPS code
    // This would typically require a geocoding service to convert lat/lon to county
    // For simplicity, we'll use a placeholder approach here
    
    // Placeholder for county lookup - in a real implementation, this would use a geocoding service
    const countyFips = await getCountyFipsCode(latitude, longitude);
    
    // Fetch extreme weather events for the county
    const url = `${NCEI_DATA_SERVICE_URL}?dataset=${DATASETS.STORM_EVENTS}&startDate=${startDate}&endDate=${endDate}&format=json`;
    
    // Add county filter if available
    const fullUrl = countyFips ? `${url}&county=${countyFips}` : url;
    
    const response = await axios.get(fullUrl);
    
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      return { events: [] }; // No events is not an error condition
    }
    
    // Process and format the extreme weather events data
    const events = response.data.map(event => {
      return {
        event_id: event.EVENT_ID,
        event_type: event.EVENT_TYPE,
        state: event.STATE,
        county: event.CZ_NAME,
        begin_date: event.BEGIN_DATE,
        end_date: event.END_DATE,
        injuries: event.INJURIES_DIRECT + event.INJURIES_INDIRECT,
        deaths: event.DEATHS_DIRECT + event.DEATHS_INDIRECT,
        property_damage: event.DAMAGE_PROPERTY,
        crop_damage: event.DAMAGE_CROPS,
        event_narrative: event.EVENT_NARRATIVE
      };
    });
    
    return { events };
  } catch (error) {
    console.error('Error fetching extreme weather events:', error);
    throw new Error(`Failed to fetch extreme weather events: ${error.message}`);
  }
};

/**
 * Get growing degree days for a location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {number} baseTemp - Base temperature for GDD calculation (default: 50°F)
 * @returns {Promise<Object>} - Growing degree days data
 */
export const getGrowingDegreeDays = async (latitude, longitude, startDate, endDate, baseTemp = 50) => {
  try {
    // Get historical temperature data
    const tempData = await getHistoricalTemperatureData(latitude, longitude, startDate, endDate);
    
    // Calculate growing degree days for each day
    const gddData = tempData.data.map(day => {
      // GDD = ((Tmax + Tmin) / 2) - Tbase
      // If the result is negative, use 0
      let gdd = 0;
      
      if (day.max_temp !== null && day.min_temp !== null) {
        const avgTemp = (day.max_temp + day.min_temp) / 2;
        gdd = Math.max(0, avgTemp - baseTemp);
      }
      
      return {
        date: day.date,
        max_temp: day.max_temp,
        min_temp: day.min_temp,
        gdd: gdd
      };
    });
    
    // Calculate cumulative GDD
    let cumulativeGdd = 0;
    const cumulativeGddData = gddData.map(day => {
      cumulativeGdd += day.gdd;
      return {
        ...day,
        cumulative_gdd: cumulativeGdd
      };
    });
    
    return {
      station: tempData.station,
      base_temperature: baseTemp,
      data: cumulativeGddData
    };
  } catch (error) {
    console.error('Error calculating growing degree days:', error);
    throw new Error(`Failed to calculate growing degree days: ${error.message}`);
  }
};

/**
 * Get frost/freeze dates for a location
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @param {number} year - Year to get data for
 * @returns {Promise<Object>} - Frost/freeze dates data
 */
export const getFrostFreezeDates = async (latitude, longitude, year) => {
  try {
    const startDate = `${year}-01-01`;
    const endDate = `${year}-12-31`;
    
    // Get historical temperature data for the entire year
    const tempData = await getHistoricalTemperatureData(latitude, longitude, startDate, endDate);
    
    // Sort data by date
    const sortedData = [...tempData.data].sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Find the last spring frost (min temp <= 32°F)
    let lastSpringFrost = null;
    for (let i = 0; i < sortedData.length; i++) {
      const day = sortedData[i];
      const date = new Date(day.date);
      
      // Only consider dates in the first half of the year (spring)
      if (date.getMonth() > 5) break; // After June
      
      if (day.min_temp !== null && day.min_temp <= 32) {
        lastSpringFrost = day.date;
      }
    }
    
    // Find the first fall frost (min temp <= 32°F)
    let firstFallFrost = null;
    for (let i = sortedData.length - 1; i >= 0; i--) {
      const day = sortedData[i];
      const date = new Date(day.date);
      
      // Only consider dates in the second half of the year (fall)
      if (date.getMonth() < 6) break; // Before July
      
      if (day.min_temp !== null && day.min_temp <= 32) {
        firstFallFrost = day.date;
      }
    }
    
    // Calculate growing season length
    let growingSeasonLength = null;
    if (lastSpringFrost && firstFallFrost) {
      const lastSpringDate = new Date(lastSpringFrost);
      const firstFallDate = new Date(firstFallFrost);
      growingSeasonLength = Math.round((firstFallDate - lastSpringDate) / (1000 * 60 * 60 * 24));
    }
    
    return {
      station: tempData.station,
      year,
      last_spring_frost: lastSpringFrost,
      first_fall_frost: firstFallFrost,
      growing_season_length: growingSeasonLength
    };
  } catch (error) {
    console.error('Error calculating frost/freeze dates:', error);
    throw new Error(`Failed to calculate frost/freeze dates: ${error.message}`);
  }
};

/**
 * Get all climate data for a farm
 * @param {number} farmId - Farm ID
 * @returns {Promise<Object>} - Comprehensive climate data for the farm
 */
export const getFarmClimateData = async (farmId) => {
  try {
    // Get farm location from database
    const farm = await sequelize.query(
      'SELECT id, name, location_data FROM site.farms WHERE id = :farmId',
      {
        replacements: { farmId },
        type: sequelize.QueryTypes.SELECT
      }
    );
    
    if (!farm || farm.length === 0) {
      throw new Error(`Farm with ID ${farmId} not found`);
    }
    
    const farmData = farm[0];
    const location = farmData.location_data;
    
    if (!location || !location.latitude || !location.longitude) {
      throw new Error('Farm location data is missing or invalid');
    }
    
    const latitude = location.latitude;
    const longitude = location.longitude;
    
    // Get current date and calculate date ranges
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    
    const oneYearAgo = new Date(currentDate);
    oneYearAgo.setFullYear(currentYear - 1);
    
    const startDate = oneYearAgo.toISOString().split('T')[0];
    const endDate = currentDate.toISOString().split('T')[0];
    
    // Fetch all climate data in parallel
    const [temperatureData, precipitationData, climateNormals, frostDates, growingDegreeDays] = await Promise.all([
      getHistoricalTemperatureData(latitude, longitude, startDate, endDate),
      getHistoricalPrecipitationData(latitude, longitude, startDate, endDate),
      getClimateNormals(latitude, longitude),
      getFrostFreezeDates(latitude, longitude, currentYear - 1), // Previous year's complete data
      getGrowingDegreeDays(latitude, longitude, startDate, endDate)
    ]);
    
    // Combine all data into a single response
    return {
      farm_id: farmId,
      farm_name: farmData.name,
      latitude,
      longitude,
      temperature: temperatureData,
      precipitation: precipitationData,
      climate_normals: climateNormals,
      frost_dates: frostDates,
      growing_degree_days: growingDegreeDays
    };
  } catch (error) {
    console.error('Error fetching farm climate data:', error);
    throw new Error(`Failed to fetch farm climate data: ${error.message}`);
  }
};

/**
 * Helper function to get county FIPS code from latitude and longitude
 * This is a placeholder - in a real implementation, this would use a geocoding service
 * @param {number} latitude - Latitude of the location
 * @param {number} longitude - Longitude of the location
 * @returns {Promise<string>} - County FIPS code
 */
const getCountyFipsCode = async (latitude, longitude) => {
  // In a real implementation, this would call a geocoding service
  // For now, return null to indicate that we don't have this information
  return null;
};

// Export all functions
export default {
  findNearestStation,
  getHistoricalTemperatureData,
  getHistoricalPrecipitationData,
  getClimateNormals,
  getExtremeWeatherEvents,
  getGrowingDegreeDays,
  getFrostFreezeDates,
  getFarmClimateData
};