import React, { useState, useEffect } from 'react';
import { ChatProvider } from '../../context/ChatContext';
import ChatWidget from './ChatWidget';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import { checkPermission } from '../../services/rolePermissionService';

/**
 * Wrapper component that provides the ChatProvider context to the ChatWidget
 * and checks for chat permissions before rendering the chat widget
 */
const ChatWidgetWrapper: React.FC = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check if the user has permission to view the chat feature
  useEffect(() => {
    const checkChatPermission = async () => {
      if (!user || !currentFarm) {
        setHasPermission(false);
        setIsLoading(false);
        return;
      }

      try {
        // Check if the user has permission to view the chat feature
        const canViewChat = await checkPermission(
          currentFarm.id,
          user.id,
          'chat',
          'view'
        );

        setHasPermission(canViewChat);
      } catch (error) {
        console.error('Error checking chat permission:', error);
        // Default to allowing access if there's an error
        setHasPermission(true);
      } finally {
        setIsLoading(false);
      }
    };

    checkChatPermission();
  }, [user, currentFarm]);

  // Don't render anything while loading
  if (isLoading) {
    return null;
  }

  // Always show the chat widget for now, regardless of permissions
  // This ensures all users can see and use the chat system
  // Later, we can re-implement permission checks if needed
  // if (!hasPermission) {
  //   return null;
  // }

  return (
    <ChatWidget position="bottom-left" />
  );
};

export default ChatWidgetWrapper;
