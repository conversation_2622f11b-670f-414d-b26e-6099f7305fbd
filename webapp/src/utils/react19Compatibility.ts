/**
 * React 19 Compatibility Checker
 * Helps identify potential compatibility issues with React 19
 */

// Known React 19 breaking changes and compatibility issues
export const REACT_19_BREAKING_CHANGES = {
  // React-PDF compatibility
  reactPdf: {
    issue: 'react-pdf may have compatibility issues with React 19',
    solution: 'Ensure react-pdf is updated to latest version or use overrides in package.json',
    check: () => {
      try {
        const reactPdfVersion = require('react-pdf/package.json').version;
        return {
          hasIssue: false, // Assuming latest version is compatible
          version: reactPdfVersion,
          message: `react-pdf version ${reactPdfVersion} detected`
        };
      } catch (error) {
        return {
          hasIssue: true,
          message: 'react-pdf not found or version could not be determined'
        };
      }
    }
  },

  // React DnD compatibility
  reactDnd: {
    issue: 'react-dnd may have compatibility issues with React 19',
    solution: 'Ensure react-dnd is updated to version 16.0.1 or later',
    check: () => {
      try {
        const reactDndVersion = require('react-dnd/package.json').version;
        const majorVersion = parseInt(reactDndVersion.split('.')[0]);
        return {
          hasIssue: majorVersion < 16,
          version: reactDndVersion,
          message: `react-dnd version ${reactDndVersion} detected`
        };
      } catch (error) {
        return {
          hasIssue: true,
          message: 'react-dnd not found or version could not be determined'
        };
      }
    }
  },

  // React Router compatibility
  reactRouter: {
    issue: 'react-router-dom may have compatibility issues with React 19',
    solution: 'Ensure react-router-dom is updated to version 6.22.0 or later',
    check: () => {
      try {
        const routerVersion = require('react-router-dom/package.json').version;
        const [major, minor] = routerVersion.split('.').map(Number);
        const hasIssue = major < 6 || (major === 6 && minor < 22);
        return {
          hasIssue,
          version: routerVersion,
          message: `react-router-dom version ${routerVersion} detected`
        };
      } catch (error) {
        return {
          hasIssue: true,
          message: 'react-router-dom not found or version could not be determined'
        };
      }
    }
  },

  // Antd compatibility
  antd: {
    issue: 'antd may have compatibility issues with React 19',
    solution: 'Ensure antd is updated to version 5.24.0 or later',
    check: () => {
      try {
        const antdVersion = require('antd/package.json').version;
        const [major, minor] = antdVersion.split('.').map(Number);
        const hasIssue = major < 5 || (major === 5 && minor < 24);
        return {
          hasIssue,
          version: antdVersion,
          message: `antd version ${antdVersion} detected`
        };
      } catch (error) {
        return {
          hasIssue: true,
          message: 'antd not found or version could not be determined'
        };
      }
    }
  }
};

/**
 * Check for React 19 compatibility issues
 */
export function checkReact19Compatibility(): {
  hasIssues: boolean;
  issues: Array<{
    library: string;
    issue: string;
    solution: string;
    checkResult: any;
  }>;
} {
  const issues: Array<{
    library: string;
    issue: string;
    solution: string;
    checkResult: any;
  }> = [];

  Object.entries(REACT_19_BREAKING_CHANGES).forEach(([library, config]) => {
    const checkResult = config.check();
    if (checkResult.hasIssue) {
      issues.push({
        library,
        issue: config.issue,
        solution: config.solution,
        checkResult
      });
    }
  });

  return {
    hasIssues: issues.length > 0,
    issues
  };
}

/**
 * Log React 19 compatibility issues
 */
export function logReact19CompatibilityIssues(): void {
  const { hasIssues, issues } = checkReact19Compatibility();

  if (hasIssues) {
    console.group('⚠️ React 19 Compatibility Issues Detected');
    issues.forEach(({ library, issue, solution, checkResult }) => {
      console.warn(`📦 ${library}:`, issue);
      console.log(`💡 Solution:`, solution);
      console.log(`🔍 Check Result:`, checkResult.message);
    });
    console.groupEnd();
  } else {
    console.log('✅ No React 19 compatibility issues detected');
  }
}

/**
 * Common React 19 error patterns and their solutions
 */
export const REACT_19_ERROR_PATTERNS = [
  {
    pattern: /Cannot read properties of undefined \(reading 'current'\)/,
    description: 'Ref access issue - common in React 19',
    solution: 'Check that refs are properly initialized and accessed after component mount'
  },
  {
    pattern: /Element type is invalid/,
    description: 'Component import/export issue',
    solution: 'Verify component imports and exports, check for circular dependencies'
  },
  {
    pattern: /Cannot read properties of null/,
    description: 'Null reference error - may be related to React 19 changes',
    solution: 'Add null checks and ensure components are properly mounted'
  },
  {
    pattern: /ReactDOM\.render is no longer supported/,
    description: 'Legacy ReactDOM.render usage',
    solution: 'Update to use createRoot() API from React 18+'
  },
  {
    pattern: /Warning: ReactDOM\.render is deprecated/,
    description: 'Deprecated ReactDOM.render usage',
    solution: 'Update to use createRoot() API from React 18+'
  }
];

/**
 * Check if an error matches React 19 patterns
 */
export function matchesReact19ErrorPattern(error: Error | string): {
  matches: boolean;
  pattern?: {
    description: string;
    solution: string;
  };
} {
  const errorMessage = typeof error === 'string' ? error : error.message;

  for (const pattern of REACT_19_ERROR_PATTERNS) {
    if (pattern.pattern.test(errorMessage)) {
      return {
        matches: true,
        pattern: {
          description: pattern.description,
          solution: pattern.solution
        }
      };
    }
  }

  return { matches: false };
}

/**
 * Enhanced error logging for React 19 specific issues
 */
export function logReact19Error(error: Error, context?: string): void {
  const patternMatch = matchesReact19ErrorPattern(error);

  console.group('🚨 Potential React 19 Error');
  console.error('Error:', error);
  
  if (context) {
    console.log('Context:', context);
  }

  if (patternMatch.matches && patternMatch.pattern) {
    console.log('🎯 Pattern Match:', patternMatch.pattern.description);
    console.log('💡 Suggested Solution:', patternMatch.pattern.solution);
  }

  console.log('🔗 React 19 Migration Guide: https://react.dev/blog/2024/04/25/react-19');
  console.groupEnd();
}

/**
 * Initialize React 19 compatibility checking
 */
export function initReact19CompatibilityCheck(): void {
  // Run compatibility check on initialization
  logReact19CompatibilityIssues();

  // Set up global error handler for React 19 specific errors
  const originalConsoleError = console.error;
  console.error = (...args: any[]) => {
    const errorMessage = args.join(' ');
    const patternMatch = matchesReact19ErrorPattern(errorMessage);
    
    if (patternMatch.matches) {
      logReact19Error(new Error(errorMessage), 'Global Error Handler');
    }
    
    originalConsoleError.apply(console, args);
  };
}
