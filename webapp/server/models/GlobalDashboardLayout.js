import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const GlobalDashboardLayout = defineModel('GlobalDashboardLayout', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  layout_config: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {}
  },
  dashboard_type: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'farm',
    comment: 'Type of dashboard layout (farm, business)'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this layout is currently active'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'global_dashboard_layouts',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default GlobalDashboardLayout;