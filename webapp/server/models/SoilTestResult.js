import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SoilSample from './SoilSample.js';
import dotenv from 'dotenv';

dotenv.config();

const SoilTestResult = defineModel('SoilTestResult', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  soil_sample_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SoilSample,
      key: 'id'
    }
  },
  ph: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: true
  },
  organic_matter: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  nitrogen: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  phosphorus: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  potassium: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  calcium: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  magnesium: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  sulfur: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  zinc: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  manganese: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  copper: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  iron: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  boron: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  cec: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  base_saturation: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  other_results: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'soil_test_results',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
SoilTestResult.belongsTo(SoilSample, { foreignKey: 'soil_sample_id' });
SoilSample.hasMany(SoilTestResult, { foreignKey: 'soil_sample_id' });

export default SoilTestResult;