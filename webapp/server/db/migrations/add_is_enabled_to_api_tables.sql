-- Migration to add is_enabled field to API tables
-- This migration adds an is_enabled field to the api_providers and api_endpoints tables
-- to allow enabling and disabling API providers and endpoints

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Add is_enabled column to api_providers table
ALTER TABLE site.api_providers
ADD COLUMN is_enabled BOOLEAN NOT NULL DEFAULT TRUE;

-- Add comment to the column
COMMENT ON COLUMN site.api_providers.is_enabled IS 'Whether the API provider is enabled or disabled';

-- Step 2: Add is_enabled column to api_endpoints table
ALTER TABLE site.api_endpoints
ADD COLUMN is_enabled BOOLEAN NOT NULL DEFAULT TRUE;

-- Add comment to the column
COMMENT ON COLUMN site.api_endpoints.is_enabled IS 'Whether the API endpoint is enabled or disabled';