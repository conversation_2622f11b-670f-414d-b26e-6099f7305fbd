import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  createLaborCostAnalysis, 
  getLaborCostAnalyses, 
  deleteLaborCostAnalysis 
} from '../../services/laborService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';

const LaborCostAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [analyses, setAnalyses] = useState<any[]>([]);

  // Get farmId from URL query parameters or currentFarm
  const getFarmId = () => {
    const searchParams = new URLSearchParams(location.search);
    const farmIdParam = searchParams.get('farmId');
    return farmIdParam ? Number(farmIdParam) : currentFarm?.id ? Number(currentFarm.id) : undefined;
  };
  const [formData, setFormData] = useState({
    analysisDate: format(new Date(), 'yyyy-MM-dd'),
    period: 'weekly',
    taskId: '',
    cropId: '',
    fieldId: '',
    laborHours: '',
    laborCost: '',
    productivity: '',
    notes: ''
  });

  useEffect(() => {
    const farmId = getFarmId();
    if (farmId) {
      fetchAnalyses();
    }
  }, [currentFarm, location.search]);

  const fetchAnalyses = async () => {
    try {
      const farmId = getFarmId();
      if (!farmId) {
        toast.error('Farm ID is required');
        return;
      }

      setIsLoading(true);
      const data = await getLaborCostAnalyses(farmId);
      setAnalyses(data);
    } catch (error) {
      console.error('Error fetching labor cost analyses:', error);
      toast.error('Failed to fetch labor cost analyses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    try {
      setIsLoading(true);
      await createLaborCostAnalysis({
        farmId: Number(currentFarm.id),
        ...formData,
        laborHours: parseFloat(formData.laborHours) || 0,
        laborCost: parseFloat(formData.laborCost) || 0,
        productivity: parseFloat(formData.productivity) || 0,
        taskId: formData.taskId ? parseInt(formData.taskId) : undefined,
        cropId: formData.cropId ? parseInt(formData.cropId) : undefined,
        fieldId: formData.fieldId ? parseInt(formData.fieldId) : undefined
      });

      toast.success('Labor cost analysis added successfully');
      setFormData({
        analysisDate: format(new Date(), 'yyyy-MM-dd'),
        period: 'weekly',
        taskId: '',
        cropId: '',
        fieldId: '',
        laborHours: '',
        laborCost: '',
        productivity: '',
        notes: ''
      });
      fetchAnalyses();
    } catch (error) {
      console.error('Error creating labor cost analysis:', error);
      toast.error('Failed to add labor cost analysis');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this labor cost analysis?')) {
      try {
        setIsLoading(true);
        await deleteLaborCostAnalysis(id);
        toast.success('Labor cost analysis deleted successfully');
        fetchAnalyses();
      } catch (error) {
        console.error('Error deleting labor cost analysis:', error);
        toast.error('Failed to delete labor cost analysis');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getPeriodLabel = (period: string) => {
    const periods: Record<string, string> = {
      'daily': 'Daily',
      'weekly': 'Weekly',
      'monthly': 'Monthly',
      'quarterly': 'Quarterly',
      'yearly': 'Yearly',
      'custom': 'Custom'
    };
    return periods[period] || period;
  };

  const calculateEfficiency = (laborHours: number, laborCost: number, productivity: number) => {
    if (!laborHours || !laborCost || !productivity) return 'N/A';
    const efficiency = (productivity / (laborHours * laborCost)) * 100;
    return efficiency.toFixed(2);
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Labor Cost Analysis</h1>
        <button
          onClick={() => navigate('/labor')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Add Labor Cost Analysis</h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="analysisDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Analysis Date
                </label>
                <input
                  type="date"
                  id="analysisDate"
                  name="analysisDate"
                  value={formData.analysisDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-1">
                  Period
                </label>
                <select
                  id="period"
                  name="period"
                  value={formData.period}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="yearly">Yearly</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label htmlFor="taskId" className="block text-sm font-medium text-gray-700 mb-1">
                  Task ID (Optional)
                </label>
                <input
                  type="number"
                  id="taskId"
                  name="taskId"
                  value={formData.taskId}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label htmlFor="cropId" className="block text-sm font-medium text-gray-700 mb-1">
                  Crop ID (Optional)
                </label>
                <input
                  type="number"
                  id="cropId"
                  name="cropId"
                  value={formData.cropId}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label htmlFor="fieldId" className="block text-sm font-medium text-gray-700 mb-1">
                  Field ID (Optional)
                </label>
                <input
                  type="number"
                  id="fieldId"
                  name="fieldId"
                  value={formData.fieldId}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label htmlFor="laborHours" className="block text-sm font-medium text-gray-700 mb-1">
                  Labor Hours
                </label>
                <input
                  type="number"
                  id="laborHours"
                  name="laborHours"
                  value={formData.laborHours}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="laborCost" className="block text-sm font-medium text-gray-700 mb-1">
                  Labor Cost ($)
                </label>
                <input
                  type="number"
                  id="laborCost"
                  name="laborCost"
                  value={formData.laborCost}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="productivity" className="block text-sm font-medium text-gray-700 mb-1">
                  Productivity (Units)
                </label>
                <input
                  type="number"
                  id="productivity"
                  name="productivity"
                  value={formData.productivity}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
            >
              {isLoading ? 'Adding...' : 'Add Labor Cost Analysis'}
            </button>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Labor Cost Analyses</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && analyses.length === 0 && (
            <p className="text-gray-500">No labor cost analyses found.</p>
          )}

          {!isLoading && analyses.length > 0 && (
            <div className="space-y-4">
              {analyses.map((analysis) => (
                <div key={analysis.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {getPeriodLabel(analysis.period)} Analysis - {new Date(analysis.analysis_date).toLocaleDateString()}
                      </h3>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div>
                          <p className="text-sm text-gray-500">Labor Hours:</p>
                          <p className="text-sm font-medium">{analysis.labor_hours}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Labor Cost:</p>
                          <p className="text-sm font-medium">${analysis.labor_cost}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Productivity:</p>
                          <p className="text-sm font-medium">{analysis.productivity} units</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Efficiency:</p>
                          <p className="text-sm font-medium">
                            {calculateEfficiency(analysis.labor_hours, analysis.labor_cost, analysis.productivity)}
                            {calculateEfficiency(analysis.labor_hours, analysis.labor_cost, analysis.productivity) !== 'N/A' && '%'}
                          </p>
                        </div>
                      </div>
                      {(analysis.task_id || analysis.crop_id || analysis.field_id) && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">Related to:</p>
                          <div className="flex space-x-2">
                            {analysis.task_id && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                Task #{analysis.task_id}
                              </span>
                            )}
                            {analysis.crop_id && (
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                Crop #{analysis.crop_id}
                              </span>
                            )}
                            {analysis.field_id && (
                              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                Field #{analysis.field_id}
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleDelete(analysis.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                  {analysis.notes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{analysis.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default LaborCostAnalysis;
