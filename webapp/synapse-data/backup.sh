#!/bin/bash
set -e

# Configuration
BACKUP_DIR="/data/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/matrix_backup_${TIMESTAMP}.tar.gz"
POSTGRES_DUMP="${BACKUP_DIR}/postgres_dump_${TIMESTAMP}.sql"
MEDIA_STORE="/data/media_store"
CONFIG_FILE="/data/homeserver.yaml"
LOG_CONFIG="/data/log.config"
RETENTION_DAYS=7

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

echo "Starting Matrix Synapse backup at $(date)"

# Backup PostgreSQL database
echo "Backing up PostgreSQL database..."
PGPASSWORD="${DB_PASSWORD}" pg_dump \
  -h "${DB_HOST}" \
  -p "${DB_PORT}" \
  -U "${DB_USER}" \
  -d "${DB_NAME}" \
  -n "matrix" \
  -f "${POSTGRES_DUMP}"

if [ $? -ne 0 ]; then
  echo "Error: PostgreSQL backup failed"
  exit 1
fi

echo "PostgreSQL backup completed successfully"

# Create a tar archive of the media store and configuration
echo "Creating tar archive of media store and configuration..."
tar -czf "${BACKUP_FILE}" \
  -C "$(dirname "${POSTGRES_DUMP}")" "$(basename "${POSTGRES_DUMP}")" \
  -C "$(dirname "${MEDIA_STORE}")" "$(basename "${MEDIA_STORE}")" \
  -C "$(dirname "${CONFIG_FILE}")" "$(basename "${CONFIG_FILE}")" \
  -C "$(dirname "${LOG_CONFIG}")" "$(basename "${LOG_CONFIG}")"

if [ $? -ne 0 ]; then
  echo "Error: Tar archive creation failed"
  exit 1
fi

echo "Tar archive created successfully: ${BACKUP_FILE}"

# Remove the temporary PostgreSQL dump
rm "${POSTGRES_DUMP}"

# Clean up old backups
echo "Cleaning up backups older than ${RETENTION_DAYS} days..."
find "${BACKUP_DIR}" -name "matrix_backup_*.tar.gz" -type f -mtime +${RETENTION_DAYS} -delete

echo "Backup completed at $(date)"

# Optional: Copy backup to external storage
if [ -n "${BACKUP_DESTINATION}" ]; then
  echo "Copying backup to external storage: ${BACKUP_DESTINATION}"
  
  # Example: Copy to S3 bucket
  if [[ "${BACKUP_DESTINATION}" == s3://* ]]; then
    if command -v aws &> /dev/null; then
      aws s3 cp "${BACKUP_FILE}" "${BACKUP_DESTINATION}/"
      if [ $? -ne 0 ]; then
        echo "Error: Failed to copy backup to S3"
        exit 1
      fi
      echo "Backup copied to S3 successfully"
    else
      echo "Error: aws CLI not found, cannot copy to S3"
      exit 1
    fi
  
  # Example: Copy to remote server via SCP
  elif [[ "${BACKUP_DESTINATION}" == *:* ]]; then
    if command -v scp &> /dev/null; then
      scp "${BACKUP_FILE}" "${BACKUP_DESTINATION}/"
      if [ $? -ne 0 ]; then
        echo "Error: Failed to copy backup via SCP"
        exit 1
      fi
      echo "Backup copied via SCP successfully"
    else
      echo "Error: scp not found, cannot copy to remote server"
      exit 1
    fi
  
  # Example: Copy to local directory
  else
    mkdir -p "${BACKUP_DESTINATION}"
    cp "${BACKUP_FILE}" "${BACKUP_DESTINATION}/"
    if [ $? -ne 0 ]; then
      echo "Error: Failed to copy backup to local directory"
      exit 1
    fi
    echo "Backup copied to local directory successfully"
  fi
fi

echo "Backup process completed successfully"