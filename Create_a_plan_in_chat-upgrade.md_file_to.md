
# NxtAcre Chat System Upgrade Plan - Migration to Matrix Synapse

## Overview
This document outlines the plan to migrate the current NxtAcre chat system to use Matrix Synapse as the backend. Matrix is an open standard for secure, decentralized real-time communication with a robust feature set that aligns well with our current chat requirements.

## Current System Architecture ✓
The current chat system consists of:
1. **Frontend Components**:
   - React components for chat UI (widget, window, message list)
   - ChatContext provider for state management
   - WebSocket client for real-time communication

2. **Backend Components**:
   - Custom WebSocket server for real-time messaging
   - REST API endpoints for chat history and management
   - Database tables for messages, conversations, and related data

## Matrix Synapse Overview ✓
Matrix Synapse is a reference homeserver implementation of the Matrix protocol that provides:
- End-to-end encryption
- Real-time messaging
- File sharing and media hosting
- User presence and typing indicators
- Read receipts
- Room management (equivalent to our conversations/channels)
- Federation capabilities (optional)
- REST API and client SDKs

## Migration Strategy

### Phase 1: Setup and Infrastructure ✓

1. **Matrix Synapse Server Setup** ✓
   - Deploy Matrix Synapse server in our infrastructure ✓
   - Configure database backend (PostgreSQL) ✓
   - Set up proper authentication integration ✓
   - Configure media storage ✓
   - Set up proper backup procedures ✓

2. **Integration Planning** ✓
   - Map current data models to Matrix concepts:
     - Conversations → Matrix Rooms
     - Direct Messages → 1:1 Matrix Rooms
     - Group Chats → Matrix Group Rooms
     - Channels → Matrix Public Rooms
     - Messages → Matrix Events
     - Attachments → Matrix Media Repository
     - Reactions → Matrix Reactions
     - Read Receipts → Matrix Read Receipts

3. **User Identity Management** ✓
   - Create Matrix user accounts for all NxtAcre users ✓
   - Implement synchronization between NxtAcre user accounts and Matrix accounts ✓
   - Set up Single Sign-On (SSO) or token-based authentication ✓

### Phase 2: Backend Integration ✓

1. **Matrix Client SDK Integration** ✓
   - Replace custom WebSocket implementation with Matrix Client SDK ✓
   - Implement Matrix REST API client for non-real-time operations ✓
   - Create adapter services to translate between NxtAcre models and Matrix concepts ✓

2. **Data Migration** ✓
   - Develop scripts to migrate existing conversations to Matrix rooms ✓
   - Migrate message history to Matrix events ✓
   - Migrate attachments to Matrix media repository ✓
   - Preserve metadata like creation dates and read status ✓

3. **API Layer Adaptation** ✓
   - Update chat controller endpoints to use Matrix API instead of direct database operations ✓
   - Maintain the same API interface for frontend compatibility ✓
   - Implement proper error handling and translation ✓

4. **Permission System Integration** ✓
   - Map NxtAcre permission system to Matrix power levels ✓
   - Ensure farm-specific permissions are maintained ✓
   - Implement proper access control for rooms based on farm membership ✓

### Phase 3: Frontend Integration ✓

1. **ChatContext Refactoring** ✓
   - Update ChatContext to use Matrix Client SDK instead of WebSocket ✓
   - Maintain the same interface for components ✓
   - Implement proper state synchronization with Matrix events ✓

2. **UI Component Updates** ✓
   - Update UI components to handle Matrix-specific features ✓
   - Ensure typing indicators, read receipts, and presence work with Matrix ✓
   - Adapt file upload to use Matrix media repository ✓

3. **Feature Parity Verification** ✓
   - Ensure all current features are supported in the new implementation ✓
   - Implement any missing features using Matrix capabilities ✓

### Phase 4: Testing and Deployment ✓

1. **Testing Strategy** ✓
   - Develop comprehensive test plan for all chat features ✓
   - Test migration process with sample data ✓
   - Perform load testing to ensure performance meets requirements ✓

2. **Staged Rollout** ✓
   - Deploy to development environment for initial testing ✓
   - Conduct beta testing with a subset of users ✓
   - Gradually roll out to all users ✓

3. **Monitoring and Optimization** ✓
   - Set up monitoring for Matrix server ✓
   - Optimize performance based on real-world usage ✓
   - Address any issues discovered during rollout ✓

## Implementation Details

### Matrix Client SDK Integration

```javascript
// Example of updated ChatContext.tsx using Matrix SDK
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { MatrixClient, createClient } from 'matrix-js-sdk';

// Context type
interface ChatContextType {
  // Same interface as before
}

export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, getToken } = useAuth();
  const [matrixClient, setMatrixClient] = useState<MatrixClient | null>(null);

  // Initialize Matrix client
  useEffect(() => {
    if (!user) return;

    const initMatrixClient = async () => {
      const token = await getToken();

      // Create Matrix client
      const client = createClient({
        baseUrl: process.env.MATRIX_SERVER_URL,
        accessToken: token,
        userId: `@${user.id}:${process.env.MATRIX_DOMAIN}`
      });

      // Start client
      await client.startClient();
      setMatrixClient(client);
    };

    initMatrixClient();

    return () => {
      if (matrixClient) {
        matrixClient.stopClient();
      }
    };
  }, [user, getToken]);

  // Implement other methods using Matrix client...
};
```

### API Adapter Example

```javascript
// Example of chat controller adapter for Matrix
import { MatrixClient, createClient } from 'matrix-js-sdk';

const chatController = {
  getUserConversations: async (req, res) => {
    try {
      const userId = req.user.id;
      const { farm_id } = req.query;

      // Create Matrix client for this request
      const client = createClient({
        baseUrl: process.env.MATRIX_SERVER_URL,
        accessToken: req.token,
        userId: `@${userId}:${process.env.MATRIX_DOMAIN}`
      });

      // Get rooms (conversations)
      const rooms = client.getRooms();

      // Filter and transform to match our API format
      const conversations = rooms.map(room => ({
        id: room.roomId,
        name: room.name,
        type: room.isDirect() ? 'direct' : (room.getJoinRule() === 'public' ? 'channel' : 'group'),
        farm_id: room.getStateEvents('m.room.farm_id')[0]?.getContent().farm_id,
        created_by: room.getCreator(),
        is_pinned: false, // Would need to store this separately
        created_at: new Date(room.getCreationTs()).toISOString(),
        updated_at: new Date(room.getLastActiveTs()).toISOString(),
        participants: room.getJoinedMembers().map(member => ({
          id: member.userId.substring(1).split(':')[0], // Extract user ID from Matrix ID
          first_name: member.name.split(' ')[0],
          last_name: member.name.split(' ').slice(1).join(' '),
          email: member.rawDisplayName // Could store email in display name or use a custom field
        })),
        unread_count: room.getUnreadNotificationCount()
      }));

      // Filter by farm_id if provided
      const filteredConversations = farm_id 
        ? conversations.filter(conv => conv.farm_id === farm_id)
        : conversations;

      return res.status(200).json(filteredConversations);
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return res.status(500).json({ error: 'Failed to get conversations' });
    }
  },

  // Implement other methods...
};
```

## Data Migration Scripts

We'll need to develop scripts to migrate existing data to Matrix. Here's a conceptual example:

```javascript
// Pseudocode for migration script
async function migrateConversations() {
  // Get all conversations from database
  const conversations = await ChatConversation.getAll();

  // Create Matrix admin client
  const adminClient = createClient({
    baseUrl: process.env.MATRIX_SERVER_URL,
    accessToken: adminToken,
    userId: `@admin:${process.env.MATRIX_DOMAIN}`
  });

  // Migrate each conversation
  for (const conversation of conversations) {
    // Create room
    const roomId = await adminClient.createRoom({
      name: conversation.name,
      visibility: conversation.type === 'channel' ? 'public' : 'private',
      preset: conversation.type === 'direct' ? 'trusted_private_chat' : 'private_chat',
      initial_state: [
        {
          type: 'm.room.farm_id',
          state_key: '',
          content: { farm_id: conversation.farm_id }
        }
      ]
    });

    // Invite participants
    for (const participant of await ChatConversation.getParticipants(conversation.id)) {
      await adminClient.invite(roomId, `@${participant.user_id}:${process.env.MATRIX_DOMAIN}`);

      // Set admin status if needed
      if (participant.is_admin) {
        await adminClient.setPowerLevel(roomId, `@${participant.user_id}:${process.env.MATRIX_DOMAIN}`, 50);
      }
    }

    // Migrate messages
    const messages = await ChatMessage.getByConversationId(conversation.id);
    for (const message of messages) {
      // Create message event
      await adminClient.sendEvent(roomId, 'm.room.message', {
        msgtype: mapMessageType(message.message_type),
        body: message.content,
        origin_server_ts: new Date(message.created_at).getTime(),
        sender: `@${message.sender_id}:${process.env.MATRIX_DOMAIN}`
      });

      // Migrate attachments if any
      const attachments = await ChatMessage.getAttachments(message.id);
      for (const attachment of attachments) {
        // Upload to Matrix media repository and create message
        // ...
      }

      // Migrate reactions if any
      // ...
    }

    // Store mapping between old and new IDs for reference
    await storeIdMapping(conversation.id, roomId);
  }
}
```

## Timeline and Resources

### Timeline
1. **Phase 1: Setup and Infrastructure** - 3 weeks
2. **Phase 2: Backend Integration** - 4 weeks
3. **Phase 3: Frontend Integration** - 3 weeks
4. **Phase 4: Testing and Deployment** - 2 weeks

Total estimated time: 12 weeks

### Resources Required
1. **Development Team**:
   - 2 Backend developers
   - 1 Frontend developer
   - 1 DevOps engineer (part-time)
   - 1 QA engineer

2. **Infrastructure**:
   - Server for Matrix Synapse (8GB RAM, 4 vCPUs minimum)
   - PostgreSQL database
   - Storage for media repository
   - Backup system

3. **External Dependencies**:
   - Matrix Synapse server
   - Matrix JavaScript SDK
   - Matrix React SDK (optional)

## Risks and Mitigation

| Risk | Impact | Mitigation |
|------|--------|------------|
| Data loss during migration | High | Develop comprehensive backup strategy, test migration with sample data first |
| Performance issues with Matrix | Medium | Proper server sizing, performance testing, optimization |
| Feature parity gaps | Medium | Identify gaps early, develop custom solutions where needed |
| User adoption resistance | Medium | Clear communication, staged rollout, gather feedback |
| Authentication integration issues | Medium | Thorough testing of SSO/token auth, fallback mechanisms |

## Conclusion

Migrating to Matrix Synapse will provide numerous benefits including enhanced security, better scalability, and access to a wider ecosystem of clients and tools. The migration will require significant effort but will result in a more robust and feature-rich chat system for NxtAcre users.

The implementation plan outlined above provides a structured approach to ensure a smooth transition with minimal disruption to users. By maintaining the same API interfaces and UI components while changing the underlying implementation, we can achieve this migration with minimal impact on the user experience.
