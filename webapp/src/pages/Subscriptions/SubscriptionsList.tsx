import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { 
  initStripeSession, 
  PaymentProvider, 
  SubscriptionOperation 
} from '../../services/paymentService';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_farms: number;
  max_users: number;
  is_active: boolean;
}

interface Farm {
  id: string;
  name: string;
  subscription_plan_id: string;
  subscription_status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  SubscriptionPlan?: SubscriptionPlan;
}

interface UserDetails {
  userType: string;
  isBusinessOwner: boolean;
  isFarmOwner: boolean;
}

const SubscriptionsList = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [farm, setFarm] = useState<Farm | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [selectedOperation, setSelectedOperation] = useState<SubscriptionOperation>('subscribe');
  const [processingPayment, setProcessingPayment] = useState(false);
  const [promoCode, setPromoCode] = useState<string>('');
  const [validatingPromoCode, setValidatingPromoCode] = useState(false);
  const [promoCodeValid, setPromoCodeValid] = useState<boolean | null>(null);
  const [promoCodeMessage, setPromoCodeMessage] = useState<string>('');

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch user details
        const userResponse = await axios.get(`${API_URL}/users/${user.id}/details`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Set user details with default values if not provided
        const details = userResponse.data.user || {};
        setUserDetails({
          userType: details.user_type || 'farmer',
          isBusinessOwner: details.is_business_owner || false,
          isFarmOwner: details.user_type === 'farmer' || false
        });

        // Fetch subscription plans
        const plansResponse = await axios.get(`${API_URL}/subscriptions/plans`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setPlans(plansResponse.data.plans || []);

        // Fetch farm information if user has a farm
        if (user.farm_id) {
          const farmResponse = await axios.get(`${API_URL}/farms/${user.farm_id}`, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          });

          setFarm(farmResponse.data.farm || null);
        }
      } catch (err: any) {
        console.error('Error fetching subscription data:', err);
        setError(err.response?.data?.error || 'Failed to load subscription information');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, navigate]);

  const handleSubscribe = (planId: string) => {
    // Determine if this is a new subscription or an upgrade/downgrade
    const operation: SubscriptionOperation = farm?.subscription_plan_id 
      ? (farm.subscription_plan_id !== planId ? 'upgrade' : 'subscribe')
      : 'subscribe';

    setSelectedPlanId(planId);
    setSelectedOperation(operation);
    setShowPaymentModal(true);
  };

  const validatePromoCode = async () => {
    if (!promoCode.trim() || !selectedPlanId || !user?.farm_id) return;

    try {
      setValidatingPromoCode(true);
      setPromoCodeValid(null);
      setPromoCodeMessage('');

      const response = await axios.post(
        `${API_URL}/subscriptions/promo-codes/validate`,
        {
          code: promoCode,
          subscriptionPlanId: selectedPlanId,
          userId: user.id,
          farmId: user.farm_id,
          billingCycle
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (response.data && response.data.message) {
        setPromoCodeValid(true);
        setPromoCodeMessage(response.data.message);
      }
    } catch (err: any) {
      console.error('Error validating promo code:', err);
      setPromoCodeValid(false);
      setPromoCodeMessage(err.response?.data?.error || 'Invalid promo code');
    } finally {
      setValidatingPromoCode(false);
    }
  };

  const handlePaymentMethodSelect = async (provider: PaymentProvider) => {
    if (!selectedPlanId || !user?.farm_id) return;

    try {
      setProcessingPayment(true);
      setError(null);

      let sessionData;
      const promoCodeToApply = promoCodeValid ? promoCode : null;

      // Initialize payment session with Stripe
      sessionData = await initStripeSession(
        selectedPlanId,
        user.farm_id,
        selectedOperation,
        billingCycle,
        promoCodeToApply
      );

      // Redirect to Stripe Checkout
      window.location.href = sessionData.url;

      // The user will be redirected to the payment provider's checkout page,
      // and then back to our success or cancel page
    } catch (err: any) {
      console.error('Error processing payment:', err);
      setError(err.response?.data?.error || 'Failed to process payment');
      setProcessingPayment(false);
      setShowPaymentModal(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Subscription Plans</h1>
        {farm && farm.subscription_plan_id && (
          <Link
            to={`/subscriptions/${farm.subscription_plan_id}`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Manage Subscription
          </Link>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {farm && farm.subscription_plan_id && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Current Subscription</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  You are currently subscribed to the <strong>{farm.SubscriptionPlan?.name}</strong> plan.
                  Your subscription is <span className="capitalize">{farm.subscription_status}</span> and will
                  {farm.subscription_end_date ? ` renew on ${new Date(farm.subscription_end_date).toLocaleDateString()}.` : ' continue until canceled.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-center mb-8">
        <div className="relative bg-white rounded-lg p-1 flex">
          <button
            type="button"
            className={`${
              billingCycle === 'monthly'
                ? 'bg-primary-100 text-primary-800'
                : 'bg-white text-gray-500'
            } relative py-2 px-4 border-transparent rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-500 focus:z-10`}
            onClick={() => setBillingCycle('monthly')}
          >
            Monthly billing
          </button>
          <button
            type="button"
            className={`${
              billingCycle === 'yearly'
                ? 'bg-primary-100 text-primary-800'
                : 'bg-white text-gray-500'
            } relative py-2 px-4 border-transparent rounded-md ml-0.5 text-sm font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-500 focus:z-10`}
            onClick={() => setBillingCycle('yearly')}
          >
            Yearly billing
            <span className="ml-1 text-xs text-green-500 font-semibold">Save 16%</span>
          </button>
        </div>
      </div>

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading subscription plans...</p>
        </div>
      ) : userDetails && userDetails.isBusinessOwner && !userDetails.isFarmOwner ? (
        <div className="bg-white shadow rounded-lg p-8">
          <div className="text-center mb-6">
            <svg className="h-16 w-16 text-blue-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Business Account Upgrade</h3>
            <p className="text-gray-500 mb-2">
              As a {userDetails.userType === 'supplier' ? 'supplier' : 'veterinarian'}, you can upgrade your account to access the full system.
            </p>
            <p className="text-gray-500 mb-6">
              Select a plan below to upgrade your business account and gain access to additional features.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {plans.filter(plan => plan.name.includes('Business') || plan.name.includes('Professional')).map((plan) => (
              <div key={plan.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300 border border-gray-200">
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatPrice(billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly)}
                    </span>
                    <span className="text-gray-500 ml-1">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>
                  </div>
                  <p className="text-sm text-gray-500 mb-4">{plan.description}</p>
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
                    <ul className="space-y-2">
                      {plan.features && Object.entries(plan.features).map(([key, value]) => (
                        <li key={key} className="flex items-start">
                          <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          <span className="text-sm text-gray-600">{key}: {String(value)}</span>
                        </li>
                      ))}
                      <li className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-gray-600">Business listing management</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-gray-600">Full system access</span>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-6">
                    <button
                      onClick={() => handleSubscribe(plan.id)}
                      disabled={farm?.subscription_plan_id === plan.id}
                      className={`w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white 
                        ${farm?.subscription_plan_id === plan.id 
                          ? 'bg-gray-400 cursor-not-allowed' 
                          : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'}`}
                    >
                      {farm?.subscription_plan_id === plan.id ? 'Current Plan' : 'Upgrade Account'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : plans.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No subscription plans available</h3>
          <p className="text-gray-500 mb-6">Please contact support for assistance.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {plans.map((plan) => (
            <div key={plan.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-gray-900">
                    {formatPrice(billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly)}
                  </span>
                  <span className="text-gray-500 ml-1">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>
                </div>
                <p className="text-sm text-gray-500 mb-4">{plan.description}</p>
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
                  <ul className="space-y-2">
                    {plan.features && Object.entries(plan.features).map(([key, value]) => (
                      <li key={key} className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-gray-600">{key}: {String(value)}</span>
                      </li>
                    ))}
                    <li className="flex items-start">
                      <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-gray-600">Valid for 1 farm</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-gray-600">Up to {plan.max_users} users</span>
                    </li>
                  </ul>
                </div>
                <div className="mt-6">
                  <button
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={farm?.subscription_plan_id === plan.id}
                    className={`w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white 
                      ${farm?.subscription_plan_id === plan.id 
                        ? 'bg-gray-400 cursor-not-allowed' 
                        : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'}`}
                  >
                    {farm?.subscription_plan_id === plan.id ? 'Current Plan' : 'Subscribe'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Payment Method Selection Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Select Payment Method</h3>
              <button 
                onClick={() => setShowPaymentModal(false)}
                disabled={processingPayment}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="text-sm text-gray-500 mb-4">
                Choose your preferred payment method to {selectedOperation === 'subscribe' ? 'subscribe to' : selectedOperation === 'upgrade' ? 'upgrade to' : 'downgrade to'} the selected plan.
              </p>

              {/* Promo Code Section */}
              <div className="mb-6">
                <label htmlFor="promo-code" className="block text-sm font-medium text-gray-700 mb-1">
                  Have a promo code?
                </label>
                <div className="flex">
                  <input
                    type="text"
                    id="promo-code"
                    name="promo-code"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                    placeholder="Enter promo code"
                    className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    disabled={validatingPromoCode || promoCodeValid === true}
                  />
                  <button
                    type="button"
                    onClick={validatePromoCode}
                    disabled={!promoCode.trim() || validatingPromoCode || promoCodeValid === true}
                    className={`ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white 
                      ${!promoCode.trim() || validatingPromoCode || promoCodeValid === true
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                      }`}
                  >
                    {validatingPromoCode ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Validating...
                      </>
                    ) : promoCodeValid === true ? (
                      'Applied'
                    ) : (
                      'Apply'
                    )}
                  </button>
                </div>
                {promoCodeMessage && (
                  <p className={`mt-1 text-sm ${promoCodeValid ? 'text-green-600' : 'text-red-600'}`}>
                    {promoCodeMessage}
                  </p>
                )}
              </div>

              <div className="space-y-4">
                <button
                  onClick={() => handlePaymentMethodSelect('stripe')}
                  disabled={processingPayment}
                  className="w-full flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <svg className="h-8 w-8 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M14.24 0H14.12L7.55.02c-.18.01-.31.01-.4.02-.08 0-.16.01-.23.02-.08 0-.15.01-.22.02-.07.01-.14.02-.2.03-.07.01-.13.02-.19.04-.06.01-.12.03-.18.04-.06.02-.11.03-.16.05-.05.02-.1.04-.15.06-.05.02-.1.04-.14.06-.05.03-.09.05-.13.08-.04.02-.08.05-.12.08-.04.03-.08.06-.11.09-.04.03-.07.06-.1.1-.03.03-.06.07-.09.1-.03.04-.05.07-.08.11-.02.04-.05.08-.07.12-.02.04-.04.08-.06.12-.02.04-.03.09-.05.13-.01.04-.03.09-.04.14-.01.05-.02.09-.03.14-.01.05-.02.1-.02.15-.01.05-.01.1-.01.16 0 .05-.01.11-.01.16v16.12c0 .06 0 .11.01.17 0 .05 0 .1.01.15 0 .**********.***********.**********.***********.***********.***********.04.04.08.06.12.02.04.04.08.07.12.02.04.05.07.08.11.02.03.05.07.08.1.03.04.06.07.1.1.03.03.07.06.11.09.04.03.07.05.12.08.04.02.08.05.13.07.04.02.09.05.14.07.05.02.1.04.15.06.05.02.1.03.16.05.06.01.12.03.18.04.06.01.12.03.19.03.07.01.14.02.21.03.07 0 .15.01.23.01.09.01.22.01.4.02l6.97.02v-7.12c0-.11 0-.21.01-.31.01-.11.02-.21.04-.31.02-.1.04-.2.07-.29.03-.1.06-.19.1-.28.04-.09.08-.18.13-.26.05-.08.1-.16.16-.24.06-.08.12-.15.19-.22.07-.07.14-.14.22-.2.08-.06.16-.12.24-.17.08-.05.17-.1.26-.14.09-.04.18-.08.28-.11.1-.03.2-.06.3-.08.1-.02.2-.04.31-.05.11-.01.21-.02.32-.02h1.11c.11 0 .21.01.32.02.1.01.21.03.31.05.1.02.2.05.3.08.1.03.19.07.28.11.09.04.18.09.26.14.08.05.16.11.24.17.08.06.15.13.22.2.07.07.13.14.19.22.06.08.11.16.16.24.05.08.09.17.13.26.04.09.07.18.1.28.03.09.05.19.07.29.02.1.03.2.04.31.01.1.01.2.01.31v7.12h1.17c.09 0 .19 0 .28-.01.09-.01.18-.02.26-.03.09-.01.17-.03.25-.05.08-.02.16-.04.24-.07.08-.03.15-.06.22-.09.07-.03.14-.07.2-.11.07-.04.13-.08.19-.13.06-.04.12-.09.17-.14.05-.05.11-.1.16-.16.05-.05.09-.11.14-.17.04-.06.08-.12.12-.19.04-.06.07-.13.1-.2.03-.07.06-.14.08-.22.03-.07.05-.15.06-.23.02-.08.03-.16.04-.25.01-.08.02-.17.02-.26.01-.09.01-.18.01-.28V7.59c0-.09 0-.19-.01-.28-.01-.09-.01-.18-.02-.26-.01-.09-.02-.17-.04-.25-.02-.08-.04-.16-.06-.23-.03-.08-.05-.15-.08-.22-.03-.07-.06-.14-.1-.2-.04-.07-.08-.13-.12-.19-.04-.06-.09-.12-.14-.17-.05-.06-.1-.11-.16-.16-.05-.05-.11-.1-.17-.14-.06-.05-.12-.09-.19-.13-.06-.04-.13-.08-.2-.11-.07-.03-.14-.06-.22-.09-.08-.03-.16-.05-.24-.07-.08-.02-.16-.04-.25-.05-.09-.01-.17-.02-.26-.03-.09-.01-.19-.01-.28-.01h-1.17v.02H14.24z"/>
                    </svg>
                    <span className="ml-3 font-medium">Pay with Credit Card</span>
                  </div>
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

              </div>

              {processingPayment && (
                <div className="mt-4 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                  <span className="ml-2 text-sm text-gray-500">Processing payment...</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default SubscriptionsList;
