import express from 'express';
import {
  getFarmWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  executeWorkflow,
  processTriggers
} from '../controllers/workflowController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all workflow automations for a farm
router.get('/farm/:farmId', authenticate, getFarmWorkflows);

// Get a specific workflow automation
router.get('/:workflowId', authenticate, getWorkflow);

// Create a new workflow automation
router.post('/', authenticate, createWorkflow);

// Update a workflow automation
router.put('/:workflowId', authenticate, updateWorkflow);

// Delete a workflow automation
router.delete('/:workflowId', authenticate, deleteWorkflow);

// Execute a workflow manually
router.post('/:workflowId/execute', authenticate, executeWorkflow);

// Process triggers for automated execution (could be called by a cron job)
router.post('/process-triggers', processTriggers);

export default router;