import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import PageHeader from '../../components/ui/PageHeader';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Alert } from '../../components/ui/Alert';
import { Spinner } from '../../components/ui/Spinner';
import { Input } from '../../components/ui/Input';
import { FaLock, FaPlus, FaKey, FaSearch } from 'react-icons/fa';
import MasterPasswordModal from './components/MasterPasswordModal';
import RecoveryKeyModal from './components/RecoveryKeyModal';
import Layout from '../../components/Layout';

const PasswordManagerPage: React.FC = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [passwordGroups, setPasswordGroups] = useState<any[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<any[]>([]);
  const [showMasterPasswordModal, setShowMasterPasswordModal] = useState(false);
  const [showRecoveryKeyModal, setShowRecoveryKeyModal] = useState(false);
  const [hasRecoveryKey, setHasRecoveryKey] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Check if user has a recovery key
    const checkRecoveryKey = async () => {
      try {
        const response = await fetch('/api/password-manager/recovery-keys', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setHasRecoveryKey(data.data.hasRecoveryKey);
        }
      } catch (error) {
        console.error('Error checking recovery key:', error);
      }
    };

    checkRecoveryKey();
  }, []);

  // Filter password groups based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredGroups(passwordGroups);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = passwordGroups.filter(group => 
        group.name.toLowerCase().includes(query) || 
        (group.description && group.description.toLowerCase().includes(query))
      );
      setFilteredGroups(filtered);
    }
  }, [searchQuery, passwordGroups]);

  const fetchPasswordGroups = async (masterPassword: string) => {
    if (!currentFarm) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/password-manager/farms/${currentFarm.id}/password-groups`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch password groups');
      }

      const data = await response.json();
      setPasswordGroups(data.data);
    } catch (error) {
      console.error('Error fetching password groups:', error);
      setError('Failed to load password groups. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = () => {
    navigate('/password-manager/groups/new');
  };

  const handleGroupClick = (groupId: string) => {
    navigate(`/password-manager/groups/${groupId}`);
  };

  const handleMasterPasswordSubmit = (masterPassword: string) => {
    setShowMasterPasswordModal(false);
    fetchPasswordGroups(masterPassword);

    // Store master password in session storage (not ideal for production)
    // In a real implementation, we would use a more secure approach
    sessionStorage.setItem('masterPassword', masterPassword);
  };

  const handleGenerateRecoveryKey = () => {
    setShowRecoveryKeyModal(true);
  };

  const handleRecoveryKeyGenerated = () => {
    setShowRecoveryKeyModal(false);
    setHasRecoveryKey(true);
  };

  useEffect(() => {
    // Check if master password is already in session storage
    const masterPassword = sessionStorage.getItem('masterPassword');
    if (masterPassword) {
      fetchPasswordGroups(masterPassword);
    } else {
      setShowMasterPasswordModal(true);
    }
  }, [currentFarm]);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
      <PageHeader
        title="Password Manager"
        description="Securely store and manage your passwords"
        icon={<FaLock className="text-primary" />}
      />

      {!hasRecoveryKey && (
        <Alert
          type="warning"
          title="No Recovery Key"
          message="You haven't generated a recovery key yet. A recovery key allows you to access your passwords if you forget your master password."
          action={
            <Button
              variant="warning"
              onClick={handleGenerateRecoveryKey}
              className="ml-4"
            >
              <FaKey className="mr-2" /> Generate Recovery Key
            </Button>
          }
          className="mb-6"
        />
      )}

      {error && (
        <Alert
          type="error"
          title="Error"
          message={error}
          className="mb-6"
        />
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <div className="w-full md:w-auto">
          <h2 className="text-xl font-semibold mb-2">Password Groups</h2>
          <div className="relative w-full md:w-64">
            <Input
              type="text"
              placeholder="Search groups..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>
        <Button
          variant="primary"
          onClick={handleCreateGroup}
        >
          <FaPlus className="mr-2" /> Create Group
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" />
        </div>
      ) : passwordGroups.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-500 mb-4">
            <FaLock className="mx-auto text-5xl mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Password Groups</h3>
            <p>Create your first password group to start storing passwords securely.</p>
          </div>
          <Button
            variant="primary"
            onClick={handleCreateGroup}
            className="mt-4"
          >
            <FaPlus className="mr-2" /> Create Group
          </Button>
        </Card>
      ) : filteredGroups.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-500 mb-4">
            <FaSearch className="mx-auto text-5xl mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Matching Groups</h3>
            <p>No password groups match your search criteria.</p>
          </div>
          <Button
            variant="secondary"
            onClick={() => setSearchQuery('')}
            className="mt-4"
          >
            Clear Search
          </Button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGroups.map((group) => (
            <Card
              key={group.id}
              className="p-6 cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleGroupClick(group.id)}
            >
              <h3 className="text-lg font-semibold mb-2">{group.name}</h3>
              <p className="text-gray-600 text-sm mb-4">{group.description || 'No description'}</p>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  Created: {new Date(group.created_at).toLocaleDateString()}
                </span>
                <span className="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                  {group.passwords?.length || 0} passwords
                </span>
              </div>
            </Card>
          ))}
        </div>
      )}

      <MasterPasswordModal
        isOpen={showMasterPasswordModal}
        onClose={() => setShowMasterPasswordModal(false)}
        onSubmit={handleMasterPasswordSubmit}
      />

      <RecoveryKeyModal
        isOpen={showRecoveryKeyModal}
        onClose={() => setShowRecoveryKeyModal(false)}
        onGenerated={handleRecoveryKeyGenerated}
      />
      </div>
    </Layout>
  );
};

export default PasswordManagerPage;
