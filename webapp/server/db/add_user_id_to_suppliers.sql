-- Add user_id column to suppliers table
-- This migration adds a user_id column to the suppliers table to associate suppliers with users
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the current schema or use 'site' as default
    SELECT current_schema() INTO schema_name;
    
    -- Add user_id column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES %I.users(id);
    ', schema_name, schema_name);
    
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.user_id IS ''User account associated with this supplier'';
    ', schema_name);
    
    RAISE NOTICE 'Successfully added user_id column to suppliers table in schema %', schema_name;
END $$;