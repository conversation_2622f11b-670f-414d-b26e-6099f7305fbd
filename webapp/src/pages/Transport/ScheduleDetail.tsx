import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getDriverScheduleById, deleteDriverSchedule, formatDate as serviceFormatDate, DriverSchedule } from '../../services/driverScheduleService';

interface Stop {
  id: string;
  type: string;
  locationName: string;
  address: string;
  scheduledTime: string;
  notes: string;
}

// Extend the DriverSchedule interface from the service with additional fields needed for the schedule detail
interface Schedule extends DriverSchedule {
  driverName: string;
  startDate: string;
  endDate: string;
  title: string;
  description: string;
  routeDetails: string;
  stops: Stop[];
}

const ScheduleDetail = () => {
  const { scheduleId } = useParams<{ scheduleId: string }>();
  const navigate = useNavigate();

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch schedule data
  useEffect(() => {
    const fetchSchedule = async () => {
      if (!scheduleId) return;

      setLoading(true);
      setError(null);

      try {
        const scheduleData = await getDriverScheduleById(scheduleId);
        setSchedule(scheduleData as Schedule);
      } catch (err: any) {
        console.error('Error fetching schedule:', err);

        // Check if the error has structured error information
        if (err.structuredError) {
          setError(err.structuredError.message);
        } else {
          setError('Failed to load schedule details. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSchedule();
  }, [scheduleId]);

  // Handle schedule deletion
  const handleDeleteSchedule = async () => {
    if (!window.confirm('Are you sure you want to delete this schedule?')) {
      return;
    }

    try {
      await deleteDriverSchedule(scheduleId!);
      navigate('/transport/schedules');
    } catch (err: any) {
      console.error('Error deleting schedule:', err);

      // Check if the error has structured error information
      if (err.structuredError) {
        setError(err.structuredError.message);
      } else {
        setError('Failed to delete schedule. Please try again later.');
      }
    }
  };

  // Using formatDate from driverScheduleService.ts with a custom wrapper to show only the date part
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time
  const formatTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format datetime using serviceFormatDate from driverScheduleService.ts
  const formatDateTime = (dateString: string | null) => {
    return serviceFormatDate(dateString);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format status text
  const formatStatus = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  // Get stop type badge class
  const getStopTypeBadgeClass = (type: string) => {
    switch (type) {
      case 'pickup':
        return 'bg-green-100 text-green-800';
      case 'delivery':
        return 'bg-blue-100 text-blue-800';
      case 'rest':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format stop type text
  const formatStopType = (type: string) => {
    switch (type) {
      case 'pickup':
        return 'Pickup';
      case 'delivery':
        return 'Delivery';
      case 'rest':
        return 'Rest Stop';
      default:
        return type;
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Schedule Details</h1>
        <div className="flex space-x-2">
          <Link
            to="/transport/schedules"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Schedules
          </Link>
          {schedule && (
            <>
              <Link
                to={`/transport/schedules/${scheduleId}/edit`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Edit
              </Link>
              <button
                onClick={handleDeleteSchedule}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading schedule details...</p>
        </div>
      ) : schedule ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                {schedule.title}
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                {schedule.driverName && `Driver: ${schedule.driverName}`}
              </p>
            </div>
            <div>
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(schedule.status)}`}>
                {formatStatus(schedule.status)}
              </span>
            </div>
          </div>
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Schedule Period</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {formatDate(schedule.startDate)}
                  {schedule.endDate && ` to ${formatDate(schedule.endDate)}`}
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Description</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {schedule.description || 'No description provided'}
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Route Details</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {schedule.routeDetails || 'No route details provided'}
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Stops</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {schedule.stops && schedule.stops.length > 0 ? (
                    <div className="space-y-4">
                      {schedule.stops.map((stop, index) => (
                        <div key={index} className="border border-gray-200 rounded-md p-4">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="text-sm font-medium text-gray-900">Stop #{index + 1}: {stop.locationName}</h4>
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStopTypeBadgeClass(stop.type)}`}>
                              {formatStopType(stop.type)}
                            </span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <div>
                              <p className="text-xs text-gray-500">Scheduled Time</p>
                              <p className="text-sm">{formatDateTime(stop.scheduledTime)}</p>
                            </div>
                            {stop.address && (
                              <div>
                                <p className="text-xs text-gray-500">Address</p>
                                <p className="text-sm">{stop.address}</p>
                              </div>
                            )}
                            {stop.notes && (
                              <div className="md:col-span-2 mt-2">
                                <p className="text-xs text-gray-500">Notes</p>
                                <p className="text-sm">{stop.notes}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <span>No stops listed</span>
                  )}
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Created</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {formatDate(schedule.createdAt)}
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {formatDate(schedule.updatedAt)}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">Schedule not found.</p>
          <Link
            to="/transport/schedules"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Schedules
          </Link>
        </div>
      )}
    </Layout>
  );
};

export default ScheduleDetail;
