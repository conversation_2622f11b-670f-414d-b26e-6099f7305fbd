-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create migration_systems table
CREATE TABLE IF NOT EXISTS migration_systems (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  logo_url VARCHAR(255),
  website VARCHAR(255),
  supported_formats VARCHAR(255)[] DEFAULT ARRAY['csv', 'excel', 'json']::VA<PERSON>HA<PERSON>(255)[],
  supported_entities VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create migration_jobs table
CREATE TABLE IF NOT EXISTS migration_jobs (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  source_system UUID NOT NULL REFERENCES migration_systems(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  entities VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[],
  file_path VARCHAR(255),
  error_message TEXT,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create migration_results table
CREATE TABLE IF NOT EXISTS migration_results (
  id UUID PRIMARY KEY,
  job_id UUID NOT NULL REFERENCES migration_jobs(id) ON DELETE CASCADE,
  total_records INTEGER DEFAULT 0,
  imported_records INTEGER DEFAULT 0,
  failed_records INTEGER DEFAULT 0,
  warnings TEXT[] DEFAULT ARRAY[]::TEXT[],
  errors TEXT[] DEFAULT ARRAY[]::TEXT[],
  entity_counts JSONB DEFAULT '{}'::JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_migration_jobs_user_id ON migration_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_migration_jobs_farm_id ON migration_jobs(farm_id);
CREATE INDEX IF NOT EXISTS idx_migration_jobs_source_system ON migration_jobs(source_system);
CREATE INDEX IF NOT EXISTS idx_migration_jobs_status ON migration_jobs(status);
CREATE INDEX IF NOT EXISTS idx_migration_results_job_id ON migration_results(job_id);

-- Add comments to tables and columns for documentation
COMMENT ON TABLE migration_systems IS 'Stores information about supported migration systems';
COMMENT ON TABLE migration_jobs IS 'Stores information about migration jobs';
COMMENT ON TABLE migration_results IS 'Stores results of migration jobs';