-- Migration: Add stripe_customer_id column to users table
-- Depends on: create_users_table.sql

SET search_path TO site;

-- Add stripe_customer_id column if it doesn't exist
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'users'
        AND column_name = 'stripe_customer_id'
    ) INTO column_exists;

    IF NOT column_exists THEN
        ALTER TABLE users ADD COLUMN stripe_customer_id VARCHAR(255);
        COMMENT ON COLUMN users.stripe_customer_id IS 'Stripe customer ID for this user';
    END IF;
END $$;

-- Create index on stripe_customer_id
DO $$
DECLARE
    index_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'site'
        AND tablename = 'users'
        AND indexname = 'idx_users_stripe_customer_id'
    ) INTO index_exists;

    IF NOT index_exists THEN
        CREATE INDEX idx_users_stripe_customer_id ON users(stripe_customer_id);
    END IF;
END $$;