import dotenv from 'dotenv';
import { sequelize } from '../config/database.js';
import IoTDevice from '../models/IoTDevice.js';
import { encryptCameraCredentials, decryptCameraCredentials } from '../utils/encryption.js';

dotenv.config();

// Test updating an Amcrest camera configuration
const testAmcrestCameraUpdate = async () => {
  try {
    console.log('Testing Amcrest camera configuration update...');
    
    // Create a test camera configuration
    const initialConfig = {
      cameraType: 'amcrest',
      serialNumber: 'TEST123456789',
      username: 'admin',
      password: 'initial-password',
      p2pEnabled: true,
      streamUrl: 'p2p://TEST123456789'
    };
    
    // Encrypt the initial configuration
    const encryptedInitialConfig = encryptCameraCredentials(initialConfig);
    
    // Create a test device or use an existing one
    let device = await IoTDevice.findOne({
      where: {
        device_type: 'camera',
        name: 'Test Amcrest Camera'
      }
    });
    
    if (!device) {
      console.log('Creating a new test camera device...');
      device = await IoTDevice.create({
        farm_id: process.env.TEST_FARM_ID || '00000000-0000-0000-0000-000000000000', // Replace with a valid farm ID
        name: 'Test Amcrest Camera',
        device_type: 'camera',
        manufacturer: 'Amcrest',
        model: 'Test Model',
        serial_number: 'TEST123456789',
        status: 'active',
        configuration: encryptedInitialConfig
      });
    } else {
      console.log('Using existing test camera device...');
      await device.update({
        configuration: encryptedInitialConfig
      });
    }
    
    // Log the initial configuration
    console.log('\nInitial configuration:');
    const decryptedInitialConfig = decryptCameraCredentials(device.configuration);
    console.log(JSON.stringify(decryptedInitialConfig, null, 2));
    
    // Create an updated configuration with some changes
    const updatedConfig = {
      cameraType: 'amcrest',
      serialNumber: 'TEST123456789',
      username: 'newadmin', // Changed username
      // Password intentionally omitted to test preservation
      p2pEnabled: false // Changed p2pEnabled
      // streamUrl intentionally omitted to test preservation
    };
    
    console.log('\nUpdated configuration to apply:');
    console.log(JSON.stringify(updatedConfig, null, 2));
    
    // Simulate the update process from the controller
    let processedConfig = updatedConfig;
    if (device.configuration && device.configuration.cameraType === 'amcrest') {
      processedConfig = {
        ...device.configuration,
        ...updatedConfig,
        password: updatedConfig.password || device.configuration.password
      };
    }
    
    // Encrypt the processed configuration
    const encryptedProcessedConfig = encryptCameraCredentials(processedConfig);
    
    // Update the device
    await device.update({
      configuration: encryptedProcessedConfig
    });
    
    // Fetch the updated device
    const updatedDevice = await IoTDevice.findByPk(device.id);
    
    // Log the final configuration
    console.log('\nFinal configuration after update:');
    const decryptedFinalConfig = decryptCameraCredentials(updatedDevice.configuration);
    console.log(JSON.stringify(decryptedFinalConfig, null, 2));
    
    // Verify that all fields are preserved correctly
    console.log('\nVerification:');
    console.log(`Username changed: ${decryptedInitialConfig.username !== decryptedFinalConfig.username}`);
    console.log(`Password preserved: ${decryptedInitialConfig.password === decryptedFinalConfig.password}`);
    console.log(`p2pEnabled changed: ${decryptedInitialConfig.p2pEnabled !== decryptedFinalConfig.p2pEnabled}`);
    console.log(`streamUrl preserved: ${decryptedInitialConfig.streamUrl === decryptedFinalConfig.streamUrl}`);
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
};

// Run the test
console.log('Starting Amcrest camera update test...');
console.log('===================================');

// Check if encryption key is set
if (!process.env.ENCRYPTION_KEY) {
  console.error('ERROR: ENCRYPTION_KEY environment variable is not set.');
  console.error('Please set it in the .env file and try again.');
  process.exit(1);
}

// Run the test
testAmcrestCameraUpdate()
  .then(() => {
    console.log('Test script completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });