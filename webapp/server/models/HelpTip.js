import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const HelpTip = defineModel('HelpTip', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  page_path: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'The page path where this tip should be displayed'
  },
  element_selector: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'CSS selector for the element this tip is associated with'
  },
  position: {
    type: DataTypes.ENUM('top', 'right', 'bottom', 'left'),
    defaultValue: 'right',
    comment: 'Position of the tip relative to the element'
  },
  order: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Order in which tips should be displayed on a page'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'help_tips',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default HelpTip;