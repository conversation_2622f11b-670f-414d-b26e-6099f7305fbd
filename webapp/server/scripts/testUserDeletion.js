import { sequelize } from '../config/database.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import dotenv from 'dotenv';

dotenv.config();

// Helper function to find or create a role by name and farm ID
const findOrCreateRoleByName = async (roleName, farmId, transaction) => {
  try {
    // First, try to find an existing role for this farm with this name
    let role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: farmId
      },
      transaction
    });

    // If role exists, return it
    if (role) {
      return role;
    }

    // If no farm-specific role exists, try to find a global role with this name
    role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: null
      },
      transaction
    });

    // If global role exists, return it
    if (role) {
      return role;
    }

    // If no role exists at all, create a new one for this farm
    console.log(`Creating new role '${roleName}' for farm ${farmId}`);
    role = await Role.create({
      name: roleName,
      farm_id: farmId,
      description: `${roleName} role for farm ${farmId}`,
      is_system_role: true
    }, { transaction });

    return role;
  } catch (error) {
    console.error(`Error finding or creating role '${roleName}' for farm ${farmId}:`, error);
    throw error;
  }
};

const testUserDeletion = async () => {
  const transaction = await sequelize.transaction();

  try {
    console.log('Starting user deletion test...');

    // Create a test farm owner
    console.log('Creating test farm owner...');
    const farmOwner = await User.create({
      email: '<EMAIL>',
      password_hash: 'password123',
      first_name: 'Test',
      last_name: 'Owner',
      phone_number: '1234567890',
      user_type: 'farmer',
      is_business_owner: true,
      is_approved: true
    }, { transaction });

    // Create a test farm
    console.log('Creating test farm...');
    const farm = await Farm.create({
      name: 'Test Farm',
      subscription_status: 'active'
    }, { transaction });

    // Create a test farm employee
    console.log('Creating test farm employee...');
    const farmEmployee = await User.create({
      email: '<EMAIL>',
      password_hash: 'password123',
      first_name: 'Test',
      last_name: 'Employee',
      phone_number: '0987654321',
      user_type: 'farmer',
      is_business_owner: false,
      is_approved: true
    }, { transaction });

    // Associate farm owner with farm
    console.log('Associating farm owner with farm...');
    // Find the farm_owner role
    const ownerRole = await findOrCreateRoleByName('farm_owner', farm.id, transaction);

    await UserFarm.create({
      user_id: farmOwner.id,
      farm_id: farm.id,
      role: 'farm_owner',
      role_id: ownerRole.id,
      is_approved: true
    }, { transaction });

    // Associate farm employee with farm
    console.log('Associating farm employee with farm...');
    // Find the farm_employee role
    const employeeRole = await findOrCreateRoleByName('farm_employee', farm.id, transaction);

    await UserFarm.create({
      user_id: farmEmployee.id,
      farm_id: farm.id,
      role: 'farm_employee',
      role_id: employeeRole.id,
      is_approved: true
    }, { transaction });

    // Verify associations
    console.log('Verifying associations...');
    const farmUsers = await UserFarm.findAll({
      where: { farm_id: farm.id },
      transaction
    });
    console.log(`Farm has ${farmUsers.length} users associated with it.`);

    // Simulate deleting the farm owner
    console.log('Simulating deletion of farm owner...');

    // Find all farms owned by the user
    const ownedFarms = await UserFarm.findAll({
      where: { 
        user_id: farmOwner.id,
        role: 'farm_owner'
      },
      transaction
    });
    console.log(`Farm owner owns ${ownedFarms.length} farms.`);

    // Process each farm owned by the user
    for (const userFarm of ownedFarms) {
      const farmId = userFarm.farm_id;

      // Find all users associated with this farm
      const farmUsers = await UserFarm.findAll({
        where: { farm_id: farmId },
        transaction
      });
      console.log(`Farm has ${farmUsers.length} users associated with it.`);

      // Remove farm associations for all users except the owner
      for (const farmUser of farmUsers) {
        if (farmUser.user_id !== farmOwner.id) {
          console.log(`Removing association for user ${farmUser.user_id}...`);
          // Delete the association
          await farmUser.destroy({ transaction });

          // Check if the user has any other farms
          const otherFarms = await UserFarm.count({
            where: { user_id: farmUser.user_id },
            transaction
          });
          console.log(`User ${farmUser.user_id} has ${otherFarms} other farms.`);

          // If user has no other farms, delete the user
          if (otherFarms === 0) {
            console.log(`Deleting user ${farmUser.user_id}...`);
            const userToDelete = await User.findByPk(farmUser.user_id, { transaction });
            if (userToDelete) {
              await userToDelete.destroy({ transaction });
            }
          }
        }
      }

      // Delete the farm
      console.log(`Deleting farm ${farmId}...`);
      const farmToDelete = await Farm.findByPk(farmId, { transaction });
      if (farmToDelete) {
        await farmToDelete.destroy({ transaction });
      }
    }

    // Finally, delete the farm owner
    console.log(`Deleting farm owner ${farmOwner.id}...`);
    await farmOwner.destroy({ transaction });

    // Verify that everything was deleted
    console.log('Verifying deletions...');

    // Check if farm still exists
    const farmExists = await Farm.findByPk(farm.id, { transaction });
    console.log(`Farm exists: ${!!farmExists}`);

    // Check if farm owner still exists
    const ownerExists = await User.findByPk(farmOwner.id, { transaction });
    console.log(`Farm owner exists: ${!!ownerExists}`);

    // Check if farm employee still exists
    const employeeExists = await User.findByPk(farmEmployee.id, { transaction });
    console.log(`Farm employee exists: ${!!employeeExists}`);

    // Check if any UserFarm associations still exist
    const userFarmsExist = await UserFarm.findAll({
      where: { farm_id: farm.id },
      transaction
    });
    console.log(`UserFarm associations exist: ${userFarmsExist.length > 0}`);

    console.log('Test completed successfully!');

    // Rollback the transaction to clean up the test data
    await transaction.rollback();
    console.log('Transaction rolled back. Test data cleaned up.');

  } catch (error) {
    await transaction.rollback();
    console.error('Error during test:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
};

// Run the test
testUserDeletion();
