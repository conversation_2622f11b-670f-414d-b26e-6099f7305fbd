import { useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import Layout from '../components/Layout';
import TwoFactorSetupModal from '../components/TwoFactorSetupModal';
import SessionManagement from './Settings/SessionManagement';
import axios from 'axios';
import { API_URL } from '../config';

const Profile = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState<'profile' | 'sessions'>('profile');

  // User profile state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [profileUpdated, setProfileUpdated] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);

  // Phone verification state
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const [verificationSuccess, setVerificationSuccess] = useState(false);

  // 2FA state
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [secret, setSecret] = useState<string | null>(null);
  const [token, setToken] = useState('');
  const [step, setStep] = useState<'loading' | 'setup' | 'verify' | 'enabled' | 'disabled' | 'method-selection'>('disabled');
  const [twoFactorError, setTwoFactorError] = useState<string | null>(null);
  const [showSetupModal, setShowSetupModal] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<'app' | 'sms'>('app');

  const { user, setupTwoFactor, setupSMS2FA, confirmTwoFactor, confirmTwoFactorWithConsecutiveCodes, disableTwoFactor, updateUser, loading, error, clearError, sendPhoneVerificationCode, verifyPhoneNumber } = useContext(AuthContext);
  const navigate = useNavigate();

  // Initialize user profile data
  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    // Set initial values from user context
    setFirstName(user.firstName || '');
    setLastName(user.lastName || '');
    setEmail(user.email || '');
    setPhoneNumber(user.phoneNumber || '');

    // Check if 2FA is already enabled
    if (user.twoFactorEnabled) {
      setStep('enabled');
    } else {
      // Check if we have stored 2FA setup data from a previous session
      const storedQrCode = localStorage.getItem('2fa_qrcode');
      const storedSecret = localStorage.getItem('2fa_secret');

      if (storedQrCode && storedSecret) {
        // Restore the 2FA setup state
        setQrCode(storedQrCode);
        setSecret(storedSecret);
        setStep('setup');
        setShowSetupModal(true);
      } else {
        setStep('disabled');
      }
    }
  }, [user, navigate]);

  // Handle profile update
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileError(null);
    setProfileUpdated(false);

    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const response = await axios.put(`${API_URL}/auth/profile/${user.id}`, {
        firstName,
        lastName,
        phoneNumber
      });

      // Update user in context
      if (response.data.success) {
        const updatedUser = response.data.user;
        updateUser(updatedUser);
      }

      setProfileUpdated(true);
      setTimeout(() => {
        setProfileUpdated(false);
      }, 3000);
    } catch (err: any) {
      console.error('Profile update error:', err);
      setProfileError(err.response?.data?.error || 'Failed to update profile');
    }
  };

  // Handle sending phone verification code
  const handleSendVerificationCode = async () => {
    setVerificationError(null);
    setIsVerificationSent(false);

    try {
      await sendPhoneVerificationCode(phoneNumber);
      setIsVerificationSent(true);
      setVerificationSuccess(false);
    } catch (err: any) {
      console.error('Send verification code error:', err);
      setVerificationError(err.response?.data?.error || 'Failed to send verification code');
    }
  };

  // Handle verifying phone number
  const handleVerifyPhoneNumber = async (e: React.FormEvent) => {
    e.preventDefault();
    setVerificationError(null);

    try {
      await verifyPhoneNumber(verificationCode);
      setVerificationSuccess(true);
      setIsVerificationSent(false);
      setVerificationCode('');

      // Update user in context to reflect verified phone
      if (user) {
        updateUser({ 
          ...user,
          phoneVerified: true 
        });
      }
    } catch (err: any) {
      console.error('Verify phone number error:', err);
      setVerificationError(err.response?.data?.error || 'Failed to verify phone number');
    }
  };

  // Show 2FA method selection
  const handleShowMethodSelection = () => {
    setTwoFactorError(null);
    setStep('method-selection');
  };

  // Initialize app-based 2FA setup
  const handleSetupAppBased2FA = async () => {
    console.log('Setup app-based 2FA');
    setTwoFactorError(null);
    setStep('loading');

    // Store a reference to the button for resetting if needed
    const setupButton = document.getElementById('setup-app-2fa-button');

    try {
      const result = await setupTwoFactor();
      console.log('Setup 2FA response:', result);

      if (!result.qrCode || !result.secret) {
        throw new Error('Failed to get QR code or secret for 2FA setup');
      }

      console.log('QR code received:', result.qrCode ? `${result.qrCode.substring(0, 50)}...` : 'null');
      console.log('Secret received:', result.secret || 'null');

      // Set the QR code and secret first
      setQrCode(result.qrCode);
      setSecret(result.secret);

      // Store the QR code and secret in localStorage so they persist across page refreshes
      localStorage.setItem('2fa_qrcode', result.qrCode);
      localStorage.setItem('2fa_secret', result.secret);

      // Then update the step and show the modal
      setStep('setup');
      setShowSetupModal(true);

    } catch (err: any) {
      console.error('2FA setup error:', err);
      setTwoFactorError(err.response?.data?.error || 'Failed to setup two-factor authentication');
      setStep('method-selection');

      // Reset the button state if there was an error
      if (setupButton) {
        (setupButton as HTMLButtonElement).disabled = false;
        (setupButton as HTMLButtonElement).innerHTML = 'Set Up App-Based 2FA';
      }
    }
  };

  // Initialize SMS-based 2FA setup
  const handleSetupSMS2FA = async () => {
    console.log('Setup SMS-based 2FA');
    setTwoFactorError(null);
    setStep('loading');

    // Store a reference to the button for resetting if needed
    const setupButton = document.getElementById('setup-sms-2fa-button');

    try {
      await setupSMS2FA();

      // Update the step to enabled since SMS 2FA doesn't require QR code scanning
      setStep('verify');

      // Change to enabled after a short delay
      setTimeout(() => {
        setStep('enabled');
      }, 3000);

    } catch (err: any) {
      console.error('SMS 2FA setup error:', err);
      setTwoFactorError(err.response?.data?.error || 'Failed to setup SMS-based two-factor authentication');
      setStep('method-selection');

      // Reset the button state if there was an error
      if (setupButton) {
        (setupButton as HTMLButtonElement).disabled = false;
        (setupButton as HTMLButtonElement).innerHTML = 'Set Up SMS-Based 2FA';
      }
    }
  };

  // Confirm 2FA setup
  const handleConfirm2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    setTwoFactorError(null);

    try {
      await confirmTwoFactor(token);
      setStep('verify');
      // Change to enabled after a short delay
      setTimeout(() => {
        setStep('enabled');
      }, 3000);
    } catch (err: any) {
      console.error('2FA confirmation error:', err);
      setTwoFactorError(err.response?.data?.error || 'Failed to confirm two-factor authentication');
    }
  };

  // Handle verification of two consecutive codes
  const handleVerifyConsecutiveCodes = async (code1: string, code2: string) => {
    try {
      await confirmTwoFactorWithConsecutiveCodes(code1, code2);
      setStep('enabled');
      setShowSetupModal(false);

      // Clear the stored QR code and secret from localStorage
      localStorage.removeItem('2fa_qrcode');
      localStorage.removeItem('2fa_secret');
    } catch (err: any) {
      console.error('2FA consecutive codes verification error:', err);
      throw new Error(err.response?.data?.error || 'Failed to verify consecutive codes');
    }
  };

  // Disable 2FA
  const handleDisable2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    setTwoFactorError(null);

    try {
      await disableTwoFactor(token);
      setToken('');
      setStep('disabled');
    } catch (err: any) {
      console.error('2FA disabling error:', err);
      setTwoFactorError(err.response?.data?.error || 'Failed to disable two-factor authentication');
    }
  };

  // Cancel 2FA setup
  const handleCancel2FASetup = () => {
    // Clear the stored QR code and secret from localStorage
    localStorage.removeItem('2fa_qrcode');
    localStorage.removeItem('2fa_secret');

    // Reset the state
    setQrCode(null);
    setSecret(null);
    setToken('');
    setStep('disabled');
    setShowSetupModal(false);
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Profile Settings</h1>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'profile'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('profile')}
          >
            Personal Information
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'sessions'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('sessions')}
          >
            Device Sessions
          </button>
        </div>

        {activeTab === 'profile' && (
          <>
            {/* Profile Information Section */}
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Personal Information</h2>
              </div>

              <div className="p-6">
                {profileError && (
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span className="block sm:inline">{profileError}</span>
                  </div>
                )}

                {profileUpdated && (
                  <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span className="block sm:inline">Profile updated successfully!</span>
                  </div>
                )}

                <form onSubmit={handleProfileUpdate}>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="first-name" className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                      <input
                        id="first-name"
                        name="firstName"
                        type="text"
                        required
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                      />
                    </div>

                    <div>
                      <label htmlFor="last-name" className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                      <input
                        id="last-name"
                        name="lastName"
                        type="text"
                        required
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        disabled
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 bg-gray-50 text-gray-500 sm:text-sm"
                        value={email}
                      />
                      <p className="mt-1 text-xs text-gray-500">Email address cannot be changed</p>
                    </div>

                    <div>
                      <label htmlFor="phone-number" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number {user?.phoneVerified ? 
                          <span className="text-xs text-green-600 ml-1">(Verified)</span> : 
                          <span className="text-xs text-gray-500 ml-1">(Not Verified)</span>
                        }
                      </label>
                      <div className="flex">
                        <input
                          id="phone-number"
                          name="phoneNumber"
                          type="tel"
                          className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                          placeholder="(*************"
                          value={phoneNumber}
                          onChange={(e) => setPhoneNumber(e.target.value)}
                        />
                        {!user?.phoneVerified && phoneNumber && (
                          <button
                            type="button"
                            onClick={handleSendVerificationCode}
                            className="ml-2 px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            Verify
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <button
                      type="submit"
                      className="w-full sm:w-auto px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Update Profile
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {/* Phone Verification Section - Only show if verification code has been sent */}
            {isVerificationSent && (
              <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Verify Your Phone Number</h2>
                </div>
                <div className="p-6">
                  {verificationError && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                      <span className="block sm:inline">{verificationError}</span>
                    </div>
                  )}

                  <p className="text-gray-600 mb-4">
                    We've sent a verification code to your phone number. Enter the code below to verify your phone.
                  </p>

                  <form onSubmit={handleVerifyPhoneNumber}>
                    <div className="mb-4">
                      <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700 mb-1">Verification Code</label>
                      <input
                        id="verification-code"
                        name="verificationCode"
                        type="text"
                        required
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        placeholder="Enter verification code"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                        autoComplete="off"
                        maxLength={6}
                        pattern="[0-9]*"
                        inputMode="numeric"
                      />
                    </div>

                    <div className="flex space-x-3">
                      <button
                        type="button"
                        onClick={() => setIsVerificationSent(false)}
                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleSendVerificationCode}
                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Resend Code
                      </button>
                      <button
                        type="submit"
                        disabled={loading || verificationCode.length !== 6}
                        className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${(loading || verificationCode.length !== 6) ? 'opacity-70 cursor-not-allowed' : ''}`}
                      >
                        {loading ? 'Verifying...' : 'Verify Phone'}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Success message when phone is verified */}
            {verificationSuccess && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md mb-6" role="alert">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="font-medium">Phone number verified successfully!</p>
                    <p className="text-sm">You can now use SMS-based two-factor authentication.</p>
                  </div>
                </div>
              </div>
            )}

            {/* Two-Factor Authentication Section */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Two-Factor Authentication</h2>
              </div>

              <div className="p-6">
                {twoFactorError && (
                  <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6" role="alert">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">Error setting up two-factor authentication</p>
                        <p className="text-sm text-red-700 mt-1">{twoFactorError}</p>
                        <p className="text-xs text-red-600 mt-2">Please try again or contact support if the problem persists.</p>
                      </div>
                    </div>
                  </div>
                )}

                {step === 'disabled' && (
                  <div>
                    <p className="text-gray-600 mb-4">
                      Two-factor authentication adds an extra layer of security to your account by requiring a code from your phone in addition to your password.
                    </p>
                    <button
                      id="setup-2fa-button"
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Button clicked, showing method selection');
                        handleShowMethodSelection();
                      }}
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out"
                    >
                      Set Up Two-Factor Authentication
                    </button>
                  </div>
                )}

                {step === 'method-selection' && (
                  <div>
                    <p className="text-gray-600 mb-4">
                      Choose your preferred two-factor authentication method:
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      {/* App-based 2FA option */}
                      <div className="border rounded-lg p-4 hover:border-primary-500 transition-colors">
                        <h3 className="text-lg font-medium mb-2">Authenticator App</h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Use an authenticator app like Google Authenticator, Authy, or Microsoft Authenticator to generate verification codes.
                        </p>
                        <button
                          id="setup-app-2fa-button"
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();

                            // Add immediate visual feedback
                            const button = e.currentTarget;
                            button.disabled = true;
                            button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Setting up...';

                            handleSetupAppBased2FA();
                          }}
                          className="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out"
                        >
                          Set Up App-Based 2FA
                        </button>
                      </div>

                      {/* SMS-based 2FA option */}
                      <div className="border rounded-lg p-4 hover:border-primary-500 transition-colors">
                        <h3 className="text-lg font-medium mb-2">SMS Verification</h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Receive verification codes via SMS text message on your phone.
                          {!user?.phoneVerified && <span className="block text-red-600 mt-1">You need to verify your phone number first.</span>}
                        </p>
                        <button
                          id="setup-sms-2fa-button"
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();

                            // Add immediate visual feedback
                            const button = e.currentTarget;
                            button.disabled = true;
                            button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Setting up...';

                            handleSetupSMS2FA();
                          }}
                          className="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out"
                          disabled={!user?.phoneVerified}
                        >
                          Set Up SMS-Based 2FA
                        </button>
                      </div>
                    </div>

                    <button
                      type="button"
                      onClick={() => setStep('disabled')}
                      className="text-gray-600 hover:text-gray-900 text-sm"
                    >
                      ← Back
                    </button>
                  </div>
                )}

                {step === 'loading' && (
                  <div className="flex flex-col items-center justify-center h-64 bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary-600 mb-4"></div>
                    <p className="text-lg font-medium text-primary-700">Loading two-factor authentication setup...</p>
                    <p className="text-sm text-gray-600 mt-2">This may take a few moments. Please don't refresh the page.</p>
                  </div>
                )}

                {step === 'setup' && (
                  <div>
                    <div className="mb-6">
                      <h3 className="text-md font-medium text-gray-900 mb-2">Step 1: Scan QR Code</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Scan this QR code with your authenticator app (like Google Authenticator, Authy, or Microsoft Authenticator).
                      </p>

                      {qrCode && (
                        <div className="flex justify-center mb-4">
                          <img src={qrCode} alt="QR Code for 2FA" className="border border-gray-300 rounded-md" />
                        </div>
                      )}

                      {secret && (
                        <div className="mb-6">
                          <p className="text-sm text-gray-600 mb-2">
                            If you can't scan the QR code, enter this code manually in your app:
                          </p>
                          <div className="bg-gray-100 p-2 rounded-md font-mono text-center break-all">
                            {secret}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="mb-6">
                      <h3 className="text-md font-medium text-gray-900 mb-2">Step 2: Verify Setup</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Enter the 6-digit code from your authenticator app to verify the setup.
                      </p>

                      <form onSubmit={handleConfirm2FA}>
                        <div className="mb-4">
                          <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-1">Authentication Code</label>
                          <input
                            id="token"
                            name="token"
                            type="text"
                            required
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            placeholder="Enter 6-digit code"
                            value={token}
                            onChange={(e) => setToken(e.target.value)}
                            autoComplete="off"
                            maxLength={6}
                            pattern="[0-9]*"
                            inputMode="numeric"
                          />
                        </div>

                        <div className="flex flex-col sm:flex-row sm:justify-between space-y-2 sm:space-y-0 sm:space-x-3">
                          <button
                            type="button"
                            onClick={handleCancel2FASetup}
                            className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            Cancel Setup
                          </button>
                          <button
                            type="submit"
                            disabled={loading || token.length !== 6}
                            className={`w-full sm:w-auto px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${(loading || token.length !== 6) ? 'opacity-70 cursor-not-allowed' : ''}`}
                          >
                            {loading ? 'Verifying...' : 'Verify and Enable 2FA'}
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                )}

                {step === 'verify' && (
                  <div className="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-md">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-800">Success!</p>
                        <p className="text-sm text-green-700">Two-factor authentication has been enabled for your account.</p>
                      </div>
                    </div>
                  </div>
                )}

                {step === 'enabled' && (
                  <div>
                    <div className="flex items-center mb-4">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div className="ml-2">
                        <p className="text-sm font-medium text-gray-900">Two-factor authentication is enabled</p>
                      </div>
                    </div>

                    <p className="text-gray-600 mb-4">
                      To disable two-factor authentication, enter the 6-digit code from your authenticator app and click the button below.
                    </p>

                    <form onSubmit={handleDisable2FA} className="flex items-end space-x-2">
                      <div className="flex-grow">
                        <label htmlFor="disable-token" className="block text-sm font-medium text-gray-700 mb-1">Authentication Code</label>
                        <input
                          id="disable-token"
                          name="token"
                          type="text"
                          required
                          className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                          placeholder="Enter 6-digit code"
                          value={token}
                          onChange={(e) => setToken(e.target.value)}
                          autoComplete="off"
                          maxLength={6}
                          pattern="[0-9]*"
                          inputMode="numeric"
                        />
                      </div>

                      <button
                        type="submit"
                        disabled={loading || token.length !== 6}
                        className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${(loading || token.length !== 6) ? 'opacity-70 cursor-not-allowed' : ''}`}
                      >
                        {loading ? 'Disabling...' : 'Disable 2FA'}
                      </button>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {activeTab === 'sessions' && (
          <SessionManagement />
        )}
      </div>

      {/* Two-Factor Authentication Setup Modal */}
      <TwoFactorSetupModal
        isOpen={showSetupModal}
        onClose={() => {
          setShowSetupModal(false);
          // Don't clear localStorage if the user is just closing the modal temporarily
          // They might refresh the page and want to continue setup
        }}
        onCancel={handleCancel2FASetup}
        qrCode={qrCode}
        secret={secret}
        onVerify={handleVerifyConsecutiveCodes}
      />
    </Layout>
  );
};

export default Profile;
