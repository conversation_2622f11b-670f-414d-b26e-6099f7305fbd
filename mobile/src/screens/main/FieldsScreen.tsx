import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { fieldService, Field } from '@/services/fieldService';

type FieldsScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Fields'>,
  NativeStackScreenProps<MainStackParamList>
>;

// Field type is imported from fieldService

const FieldsScreen: React.FC<FieldsScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [fields, setFields] = useState<Field[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFields, setFilteredFields] = useState<Field[]>([]);

  useEffect(() => {
    const fetchFields = async () => {
      try {
        setLoading(true);

        if (!user?.farmId) {
          console.error('No farm ID available');
          Alert.alert('Error', 'Could not load fields. No farm ID available.');
          setLoading(false);
          return;
        }

        const fieldsData = await fieldService.getFields(user.farmId);
        setFields(fieldsData);
        setFilteredFields(fieldsData);
      } catch (error) {
        console.error('Error fetching fields:', error);
        Alert.alert('Error', 'Could not load fields. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFields();
  }, [user?.farmId]);

  useEffect(() => {
    if (searchQuery) {
      const filtered = fields.filter(field => 
        field.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (field.crop && field.crop.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredFields(filtered);
    } else {
      setFilteredFields(fields);
    }
  }, [searchQuery, fields]);

  const handleAddField = () => {
    navigation.navigate('Map');
    // In a real app, we would navigate to a field creation screen or show a modal
    Alert.alert(
      'Add New Field',
      'To add a new field, go to the Map tab and use the drawing tool to outline your field boundaries.'
    );
  };

  const renderFieldItem = ({ item }: { item: Field }) => (
    <TouchableOpacity
      style={styles.fieldItem}
      onPress={() => navigation.navigate('FieldDetail', { fieldId: item.id })}
    >
      <View style={styles.fieldIcon}>
        <Ionicons 
          name={item.crop ? "leaf-outline" : "scan-outline"} 
          size={24} 
          color="#22c55e" 
        />
      </View>
      <View style={styles.fieldInfo}>
        <Text style={styles.fieldName}>{item.name}</Text>
        <Text style={styles.fieldDetails}>
          {item.acres} acres {item.crop ? `• ${item.crop}` : ''}
        </Text>
        <View style={styles.fieldStatus}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: item.status === 'Active' ? '#22c55e' : '#d1d5db' }
          ]} />
          <Text style={styles.statusText}>{item.status}</Text>
          {item.lastActivity && (
            <Text style={styles.lastActivity}>Last activity: {item.lastActivity}</Text>
          )}
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading fields...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search fields..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        ) : null}
      </View>

      {filteredFields.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="leaf" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No fields found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? "Try a different search term" 
              : "Add your first field to get started"}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredFields}
          renderItem={renderFieldItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity style={styles.addButton} onPress={handleAddField}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for the add button
  },
  fieldItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  fieldIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  fieldInfo: {
    flex: 1,
  },
  fieldName: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
  },
  fieldDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  fieldStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
  },
  lastActivity: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default FieldsScreen;
