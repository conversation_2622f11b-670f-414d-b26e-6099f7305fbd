import Bill from '../models/Bill.js';
import BillCategory from '../models/BillCategory.js';
import RecurringBill from '../models/RecurringBill.js';
import BillPayment from '../models/BillPayment.js';
import BillTransaction from '../models/BillTransaction.js';
import BillAttachment from '../models/BillAttachment.js';
import Farm from '../models/Farm.js';
import Vendor from '../models/Vendor.js';
import Transaction from '../models/Transaction.js';
import User from '../models/User.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Get all bills for a farm
export const getFarmBills = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { status, category, vendor, startDate, endDate, search } = req.query;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = { farm_id: farmId };

    if (status) {
      whereConditions.status = status;
    }

    if (category) {
      whereConditions.category_id = category;
    }

    if (vendor) {
      whereConditions.vendor_id = vendor;
    }

    if (startDate && endDate) {
      whereConditions.due_date = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      whereConditions.due_date = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      whereConditions.due_date = {
        [Op.lte]: new Date(endDate)
      };
    }

    if (search) {
      whereConditions[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { reference_number: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Get all bills for the farm with the specified conditions
    const bills = await Bill.findAll({
      where: whereConditions,
      include: [
        {
          model: BillCategory,
          as: 'category',
          attributes: ['id', 'name', 'color']
        },
        {
          model: Vendor,
          as: 'vendor',
          attributes: ['id', 'name']
        },
        {
          model: RecurringBill,
          as: 'recurringSchedule',
          attributes: ['id', 'frequency', 'next_due_date']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name']
        }
      ],
      order: [['due_date', 'ASC']]
    });

    return res.status(200).json({ bills });
  } catch (error) {
    console.error('Error getting farm bills:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single bill by ID
export const getBillById = async (req, res) => {
  try {
    const { billId } = req.params;

    const bill = await Bill.findByPk(billId, {
      include: [
        {
          model: BillCategory,
          as: 'category',
          attributes: ['id', 'name', 'color']
        },
        {
          model: Vendor,
          as: 'vendor',
          attributes: ['id', 'name', 'email', 'phone', 'address']
        },
        {
          model: RecurringBill,
          as: 'recurringSchedule',
          attributes: ['id', 'frequency', 'start_date', 'end_date', 'next_due_date']
        },
        {
          model: BillPayment,
          as: 'payments',
          attributes: ['id', 'amount', 'payment_date', 'payment_method', 'reference_number', 'notes'],
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'first_name', 'last_name']
            }
          ]
        },
        {
          model: BillTransaction,
          as: 'transactions',
          attributes: ['id', 'amount', 'linked_at', 'notes'],
          include: [
            {
              model: Transaction,
              as: 'transaction',
              attributes: ['id', 'transaction_date', 'description', 'amount', 'transaction_type']
            },
            {
              model: User,
              as: 'linkedBy',
              attributes: ['id', 'first_name', 'last_name']
            }
          ]
        },
        {
          model: BillAttachment,
          as: 'attachments',
          attributes: ['id', 'file_name', 'file_path', 'file_type', 'file_size', 'uploaded_at'],
          include: [
            {
              model: User,
              as: 'uploader',
              attributes: ['id', 'first_name', 'last_name']
            }
          ]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name']
        }
      ]
    });

    if (!bill) {
      return res.status(404).json({ error: 'Bill not found' });
    }

    return res.status(200).json({ bill });
  } catch (error) {
    console.error('Error getting bill:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new bill
export const createBill = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farmId,
      categoryId,
      vendorId,
      title,
      description,
      amount,
      dueDate,
      status,
      paymentMethod,
      referenceNumber,
      notes,
      isRecurring,
      recurringSchedule
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!title) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Title is required' });
    }

    if (!amount) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Amount is required' });
    }

    if (!dueDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Due date is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If category ID is provided, ensure it exists
    if (categoryId) {
      const category = await BillCategory.findOne({
        where: {
          id: categoryId,
          farm_id: farmId
        }
      });

      if (!category) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Category not found' });
      }
    }

    // If vendor ID is provided, ensure it exists
    if (vendorId) {
      const vendor = await Vendor.findOne({
        where: {
          id: vendorId,
          farm_id: farmId
        }
      });

      if (!vendor) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Vendor not found' });
      }
    }

    // Create bill
    const bill = await Bill.create({
      farm_id: farmId,
      category_id: categoryId,
      vendor_id: vendorId,
      title,
      description,
      amount,
      due_date: dueDate,
      status: status || 'unpaid',
      payment_method: paymentMethod,
      reference_number: referenceNumber,
      notes,
      is_recurring: isRecurring || false,
      created_by: req.user?.id
    }, { transaction });

    // If this is a recurring bill, create the recurring schedule
    if (isRecurring && recurringSchedule) {
      const {
        frequency,
        startDate,
        endDate,
        dayOfMonth,
        dayOfWeek,
        weekOfMonth,
        monthOfYear,
        nextDueDate
      } = recurringSchedule;

      if (!frequency) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Frequency is required for recurring bills' });
      }

      if (!startDate) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Start date is required for recurring bills' });
      }

      if (!nextDueDate) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Next due date is required for recurring bills' });
      }

      await RecurringBill.create({
        bill_id: bill.id,
        frequency,
        start_date: startDate,
        end_date: endDate,
        day_of_month: dayOfMonth,
        day_of_week: dayOfWeek,
        week_of_month: weekOfMonth,
        month_of_year: monthOfYear,
        next_due_date: nextDueDate
      }, { transaction });
    }

    await transaction.commit();

    return res.status(201).json({
      message: 'Bill created successfully',
      bill
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating bill:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a bill
export const updateBill = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId } = req.params;
    const {
      categoryId,
      vendorId,
      title,
      description,
      amount,
      dueDate,
      status,
      paymentMethod,
      referenceNumber,
      notes,
      isRecurring,
      recurringSchedule
    } = req.body;

    // Find bill to ensure it exists
    const bill = await Bill.findByPk(billId, {
      include: [
        {
          model: RecurringBill,
          as: 'recurringSchedule'
        }
      ]
    });

    if (!bill) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Bill not found' });
    }

    // If category ID is provided, ensure it exists
    if (categoryId) {
      const category = await BillCategory.findOne({
        where: {
          id: categoryId,
          farm_id: bill.farm_id
        }
      });

      if (!category) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Category not found' });
      }
    }

    // If vendor ID is provided, ensure it exists
    if (vendorId) {
      const vendor = await Vendor.findOne({
        where: {
          id: vendorId,
          farm_id: bill.farm_id
        }
      });

      if (!vendor) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Vendor not found' });
      }
    }

    // Update bill
    await bill.update({
      category_id: categoryId !== undefined ? categoryId : bill.category_id,
      vendor_id: vendorId !== undefined ? vendorId : bill.vendor_id,
      title: title || bill.title,
      description: description !== undefined ? description : bill.description,
      amount: amount !== undefined ? amount : bill.amount,
      due_date: dueDate || bill.due_date,
      status: status || bill.status,
      payment_method: paymentMethod !== undefined ? paymentMethod : bill.payment_method,
      reference_number: referenceNumber !== undefined ? referenceNumber : bill.reference_number,
      notes: notes !== undefined ? notes : bill.notes,
      is_recurring: isRecurring !== undefined ? isRecurring : bill.is_recurring
    }, { transaction });

    // Handle recurring schedule
    if (isRecurring) {
      if (recurringSchedule) {
        const {
          frequency,
          startDate,
          endDate,
          dayOfMonth,
          dayOfWeek,
          weekOfMonth,
          monthOfYear,
          nextDueDate
        } = recurringSchedule;

        if (bill.recurringSchedule) {
          // Update existing recurring schedule
          await bill.recurringSchedule.update({
            frequency: frequency || bill.recurringSchedule.frequency,
            start_date: startDate || bill.recurringSchedule.start_date,
            end_date: endDate !== undefined ? endDate : bill.recurringSchedule.end_date,
            day_of_month: dayOfMonth !== undefined ? dayOfMonth : bill.recurringSchedule.day_of_month,
            day_of_week: dayOfWeek !== undefined ? dayOfWeek : bill.recurringSchedule.day_of_week,
            week_of_month: weekOfMonth !== undefined ? weekOfMonth : bill.recurringSchedule.week_of_month,
            month_of_year: monthOfYear !== undefined ? monthOfYear : bill.recurringSchedule.month_of_year,
            next_due_date: nextDueDate || bill.recurringSchedule.next_due_date
          }, { transaction });
        } else {
          // Create new recurring schedule
          if (!frequency) {
            await transaction.rollback();
            return res.status(400).json({ error: 'Frequency is required for recurring bills' });
          }

          if (!startDate) {
            await transaction.rollback();
            return res.status(400).json({ error: 'Start date is required for recurring bills' });
          }

          if (!nextDueDate) {
            await transaction.rollback();
            return res.status(400).json({ error: 'Next due date is required for recurring bills' });
          }

          await RecurringBill.create({
            bill_id: bill.id,
            frequency,
            start_date: startDate,
            end_date: endDate,
            day_of_month: dayOfMonth,
            day_of_week: dayOfWeek,
            week_of_month: weekOfMonth,
            month_of_year: monthOfYear,
            next_due_date: nextDueDate
          }, { transaction });
        }
      }
    } else if (bill.recurringSchedule) {
      // If bill is no longer recurring, delete the recurring schedule
      await bill.recurringSchedule.destroy({ transaction });
    }

    await transaction.commit();

    // Fetch updated bill with all associations
    const updatedBill = await Bill.findByPk(billId, {
      include: [
        {
          model: BillCategory,
          as: 'category',
          attributes: ['id', 'name', 'color']
        },
        {
          model: Vendor,
          as: 'vendor',
          attributes: ['id', 'name']
        },
        {
          model: RecurringBill,
          as: 'recurringSchedule'
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name']
        }
      ]
    });

    return res.status(200).json({
      message: 'Bill updated successfully',
      bill: updatedBill
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating bill:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a bill
export const deleteBill = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId } = req.params;

    // Find bill to ensure it exists
    const bill = await Bill.findByPk(billId);
    if (!bill) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Bill not found' });
    }

    // Delete bill (this will cascade delete all related records)
    await bill.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Bill deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting bill:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Add a payment to a bill
export const addBillPayment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId } = req.params;
    const {
      amount,
      paymentDate,
      paymentMethod,
      referenceNumber,
      notes
    } = req.body;

    // Validate required fields
    if (!amount) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Payment amount is required' });
    }

    if (!paymentDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Payment date is required' });
    }

    // Find bill to ensure it exists
    const bill = await Bill.findByPk(billId);
    if (!bill) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Bill not found' });
    }

    // Create payment
    const payment = await BillPayment.create({
      bill_id: billId,
      amount,
      payment_date: paymentDate,
      payment_method: paymentMethod,
      reference_number: referenceNumber,
      notes,
      created_by: req.user?.id
    }, { transaction });

    // Update bill status based on payments
    const payments = await BillPayment.findAll({
      where: { bill_id: billId },
      attributes: ['amount']
    });

    const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const billAmount = parseFloat(bill.amount);

    let newStatus;
    if (totalPaid >= billAmount) {
      newStatus = 'paid';
    } else if (totalPaid > 0) {
      newStatus = 'partial';
    } else {
      newStatus = 'unpaid';
    }

    await bill.update({ status: newStatus }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Payment added successfully',
      payment,
      billStatus: newStatus
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error adding payment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Link a transaction to a bill
export const linkTransaction = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId } = req.params;
    const {
      transactionId,
      amount,
      notes
    } = req.body;

    // Validate required fields
    if (!transactionId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Transaction ID is required' });
    }

    if (!amount) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Amount is required' });
    }

    // Find bill to ensure it exists
    const bill = await Bill.findByPk(billId);
    if (!bill) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Bill not found' });
    }

    // Find transaction to ensure it exists
    const transactionRecord = await Transaction.findByPk(transactionId);
    if (!transactionRecord) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Transaction not found' });
    }

    // Check if transaction is already linked to this bill
    const existingLink = await BillTransaction.findOne({
      where: {
        bill_id: billId,
        transaction_id: transactionId
      }
    });

    if (existingLink) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Transaction is already linked to this bill' });
    }

    // Create link
    const link = await BillTransaction.create({
      bill_id: billId,
      transaction_id: transactionId,
      amount,
      notes,
      linked_by: req.user?.id
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Transaction linked successfully',
      link
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error linking transaction:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Unlink a transaction from a bill
export const unlinkTransaction = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId, linkId } = req.params;

    // Find link to ensure it exists
    const link = await BillTransaction.findOne({
      where: {
        id: linkId,
        bill_id: billId
      }
    });

    if (!link) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Transaction link not found' });
    }

    // Delete link
    await link.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Transaction unlinked successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error unlinking transaction:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Upload an attachment to a bill
export const uploadAttachment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId } = req.params;
    
    // Check if file was uploaded
    if (!req.file) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Find bill to ensure it exists
    const bill = await Bill.findByPk(billId);
    if (!bill) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Bill not found' });
    }

    const { originalname, mimetype, size, path: tempPath } = req.file;
    
    // Create directory for bill attachments if it doesn't exist
    const uploadDir = path.join(process.cwd(), 'uploads', 'bills', billId);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const fileExtension = path.extname(originalname);
    const fileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(uploadDir, fileName);

    // Move file from temp location to permanent location
    fs.copyFileSync(tempPath, filePath);
    fs.unlinkSync(tempPath); // Delete temp file

    // Create attachment record
    const attachment = await BillAttachment.create({
      bill_id: billId,
      file_name: originalname,
      file_path: `/uploads/bills/${billId}/${fileName}`,
      file_type: mimetype,
      file_size: size,
      uploaded_by: req.user?.id
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Attachment uploaded successfully',
      attachment
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error uploading attachment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an attachment from a bill
export const deleteAttachment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { billId, attachmentId } = req.params;

    // Find attachment to ensure it exists
    const attachment = await BillAttachment.findOne({
      where: {
        id: attachmentId,
        bill_id: billId
      }
    });

    if (!attachment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Attachment not found' });
    }

    // Delete file from filesystem
    const filePath = path.join(process.cwd(), attachment.file_path);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete attachment record
    await attachment.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting attachment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get bill statistics for a farm
export const getBillStatistics = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate } = req.query;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build date range condition
    let dateCondition = {};
    if (startDate && endDate) {
      dateCondition = {
        due_date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      };
    } else if (startDate) {
      dateCondition = {
        due_date: {
          [Op.gte]: new Date(startDate)
        }
      };
    } else if (endDate) {
      dateCondition = {
        due_date: {
          [Op.lte]: new Date(endDate)
        }
      };
    }

    // Get total bills amount
    const totalBills = await Bill.sum('amount', {
      where: {
        farm_id: farmId,
        ...dateCondition
      }
    });

    // Get total paid amount
    const paidBills = await Bill.sum('amount', {
      where: {
        farm_id: farmId,
        status: 'paid',
        ...dateCondition
      }
    });

    // Get total unpaid amount
    const unpaidBills = await Bill.sum('amount', {
      where: {
        farm_id: farmId,
        status: 'unpaid',
        ...dateCondition
      }
    });

    // Get total partial amount
    const partialBills = await Bill.sum('amount', {
      where: {
        farm_id: farmId,
        status: 'partial',
        ...dateCondition
      }
    });

    // Get overdue bills
    const overdueBills = await Bill.sum('amount', {
      where: {
        farm_id: farmId,
        status: {
          [Op.in]: ['unpaid', 'partial']
        },
        due_date: {
          [Op.lt]: new Date()
        },
        ...dateCondition
      }
    });

    // Get upcoming bills (due in the next 30 days)
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);

    const upcomingBills = await Bill.sum('amount', {
      where: {
        farm_id: farmId,
        status: {
          [Op.in]: ['unpaid', 'partial']
        },
        due_date: {
          [Op.between]: [today, thirtyDaysFromNow]
        },
        ...dateCondition
      }
    });

    // Get bills by category
    const billsByCategory = await Bill.findAll({
      attributes: [
        'category_id',
        [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount']
      ],
      where: {
        farm_id: farmId,
        ...dateCondition
      },
      include: [
        {
          model: BillCategory,
          as: 'category',
          attributes: ['name', 'color']
        }
      ],
      group: ['category_id', 'category.id', 'category.name', 'category.color']
    });

    return res.status(200).json({
      statistics: {
        totalBills: totalBills || 0,
        paidBills: paidBills || 0,
        unpaidBills: unpaidBills || 0,
        partialBills: partialBills || 0,
        overdueBills: overdueBills || 0,
        upcomingBills: upcomingBills || 0,
        billsByCategory
      }
    });
  } catch (error) {
    console.error('Error getting bill statistics:', error);
    return res.status(500).json({ error: error.message });
  }
};