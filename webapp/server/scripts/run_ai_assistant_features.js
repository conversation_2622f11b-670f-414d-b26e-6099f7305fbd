import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runAiAssistantFeatures() {
  try {
    console.log('Running AI Assistant Features migration...');

    // Check if ai_assistant_queries table exists to ensure base AI tables are already created
    let aiTablesExist = false;
    const schema = process.env.DB_SCHEMA || 'site';

    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.ai_assistant_queries LIMIT 1`);
      console.log('AI Assistant base tables exist, proceeding with AI Assistant features tables creation.');
      aiTablesExist = true;
    } catch (error) {
      console.log('AI Assistant base tables do not exist, need to run run_ai_assistant_tables.js first.');
    }

    // If AI Assistant base tables don't exist, run run_ai_assistant_tables.js first
    if (!aiTablesExist) {
      console.error('Please run run_ai_assistant_tables.js first to create the base AI Assistant tables.');
      process.exit(1);
    }

    // Now run add_ai_assistant_features.sql
    console.log('Creating AI Assistant features tables...');
    const aiAssistantFeaturesPath = path.join(__dirname, '../db/migrations/add_ai_assistant_features.sql');
    const aiAssistantFeaturesSQL = fs.readFileSync(aiAssistantFeaturesPath, 'utf8');

    // Split the SQL into individual statements
    const aiAssistantFeatureStatements = aiAssistantFeaturesSQL
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');

    console.log(`Found ${aiAssistantFeatureStatements.length} SQL statements to execute in add_ai_assistant_features.sql.`);

    // Set the search_path to use the specified schema before executing AI assistant features tables
    await sequelize.query(`SET search_path TO ${schema};`);
    console.log(`Set search_path to schema: ${schema} for AI assistant features tables`);

    // Execute each statement
    for (let i = 0; i < aiAssistantFeatureStatements.length; i++) {
      const statement = aiAssistantFeatureStatements[i];
      try {
        await sequelize.query(statement);
        console.log(`Executed statement ${i + 1}/${aiAssistantFeatureStatements.length} from add_ai_assistant_features.sql`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}/${aiAssistantFeatureStatements.length} from add_ai_assistant_features.sql:`, error.message);
        console.error('Statement:', statement);
        throw error;
      }
    }

    console.log('AI Assistant features tables created successfully!');
  } catch (error) {
    console.error('Error running AI Assistant features migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runAiAssistantFeatures();