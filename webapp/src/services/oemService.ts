import axios from 'axios';
import { API_URL } from '../config';

export interface OemSystem {
  id: string;
  name: string;
  manufacturer: string;
  api_key: string | null;
  api_secret: string | null;
  api_url: string | null;
  status: 'active' | 'inactive' | 'error';
  last_sync: string | null;
  created_at: string;
  updated_at: string;
}

export interface OemEquipmentData {
  id: string;
  equipment_id: string;
  oem_system_id: string;
  oem_equipment_id: string;
  model_details: any | null;
  warranty_info: any | null;
  service_history: any | null;
  diagnostic_data: any | null;
  firmware_version: string | null;
  last_sync: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Get all OEM systems
 */
export const getOemSystems = async (): Promise<OemSystem[]> => {
  try {
    const response = await axios.get(`${API_URL}/oem/systems`);
    return response.data.systems;
  } catch (error) {
    console.error('Error fetching OEM systems:', error);
    throw error;
  }
};

/**
 * Get OEM system by ID
 */
export const getOemSystem = async (systemId: string): Promise<OemSystem> => {
  try {
    const response = await axios.get(`${API_URL}/oem/systems/${systemId}`);
    return response.data.system;
  } catch (error) {
    console.error('Error fetching OEM system:', error);
    throw error;
  }
};

/**
 * Create new OEM system
 */
export const createOemSystem = async (data: {
  name: string;
  manufacturer: string;
  api_key?: string;
  api_secret?: string;
  api_url?: string;
}): Promise<OemSystem> => {
  try {
    const response = await axios.post(`${API_URL}/oem/systems`, data);
    return response.data.system;
  } catch (error) {
    console.error('Error creating OEM system:', error);
    throw error;
  }
};

/**
 * Update OEM system
 */
export const updateOemSystem = async (
  systemId: string,
  data: {
    name?: string;
    manufacturer?: string;
    api_key?: string;
    api_secret?: string;
    api_url?: string;
    status?: 'active' | 'inactive';
  }
): Promise<OemSystem> => {
  try {
    const response = await axios.put(`${API_URL}/oem/systems/${systemId}`, data);
    return response.data.system;
  } catch (error) {
    console.error('Error updating OEM system:', error);
    throw error;
  }
};

/**
 * Delete OEM system
 */
export const deleteOemSystem = async (systemId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/oem/systems/${systemId}`);
  } catch (error) {
    console.error('Error deleting OEM system:', error);
    throw error;
  }
};

/**
 * Get OEM data for a specific equipment item
 */
export const getEquipmentOemData = async (equipmentId: string): Promise<OemEquipmentData[]> => {
  try {
    const response = await axios.get(`${API_URL}/oem/equipment/${equipmentId}`);
    return response.data.oemData;
  } catch (error) {
    console.error('Error fetching equipment OEM data:', error);
    throw error;
  }
};

/**
 * Link equipment to OEM system
 */
export const linkEquipmentToOem = async (data: {
  equipment_id: string;
  oem_system_id: string;
  oem_equipment_id: string;
  model_details?: any;
}): Promise<OemEquipmentData> => {
  try {
    const response = await axios.post(`${API_URL}/oem/equipment/link`, data);
    return response.data.oemEquipment;
  } catch (error) {
    console.error('Error linking equipment to OEM system:', error);
    throw error;
  }
};

/**
 * Sync equipment data with OEM system
 */
export const syncEquipmentWithOem = async (
  equipmentId: string,
  oemSystemId: string
): Promise<OemEquipmentData> => {
  try {
    const response = await axios.post(`${API_URL}/oem/equipment/${equipmentId}/sync`, {
      oem_system_id: oemSystemId
    });
    return response.data.oemData;
  } catch (error) {
    console.error('Error syncing equipment with OEM system:', error);
    throw error;
  }
};

/**
 * Get available OEM systems for a manufacturer
 */
export const getAvailableOemSystems = async (manufacturer: string): Promise<OemSystem[]> => {
  try {
    const response = await axios.get(`${API_URL}/oem/available?manufacturer=${manufacturer}`);
    return response.data.systems;
  } catch (error) {
    console.error('Error fetching available OEM systems:', error);
    throw error;
  }
};
