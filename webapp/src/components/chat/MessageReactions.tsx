import React, { useState } from 'react';
import { Reaction } from '../../context/ChatContext';
import EmojiPicker from './EmojiPicker';

interface MessageReactionsProps {
  messageId: string;
  conversationId: string;
  reactions: Reaction[];
  onAddReaction: (conversationId: string, messageId: string, reaction: string) => Promise<void>;
}

/**
 * Component for displaying and adding reactions to messages
 */
const MessageReactions: React.FC<MessageReactionsProps> = ({
  messageId,
  conversationId,
  reactions = [],
  onAddReaction
}) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // Group reactions by emoji
  const groupedReactions = reactions.reduce((acc, reaction) => {
    if (!acc[reaction.reaction]) {
      acc[reaction.reaction] = [];
    }
    acc[reaction.reaction].push(reaction);
    return acc;
  }, {} as Record<string, Reaction[]>);

  // Handle adding a reaction
  const handleAddReaction = async (emoji: string) => {
    try {
      await onAddReaction(conversationId, messageId, emoji);
    } catch (error) {
      console.error('Error adding reaction:', error);
    }
  };

  return (
    <div className="flex flex-wrap items-center mt-1 relative">
      {/* Display grouped reactions */}
      {Object.entries(groupedReactions).map(([emoji, reactions]) => (
        <div 
          key={emoji}
          className="bg-gray-100 rounded-full px-2 py-0.5 text-xs mr-1 mb-1 flex items-center cursor-pointer hover:bg-gray-200"
          onClick={() => handleAddReaction(emoji)}
          title={reactions.map(r => `${r.first_name || ''} ${r.last_name || ''}`).join(', ')}
        >
          <span className="mr-1">{emoji}</span>
          <span>{reactions.length}</span>
        </div>
      ))}

      {/* Add reaction button */}
      <button
        type="button"
        className="text-xs text-gray-500 hover:text-gray-700 mr-1 mb-1"
        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
        aria-label="Add reaction"
      >
        +
      </button>

      {/* Emoji picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={(emoji) => {
            handleAddReaction(emoji);
            setShowEmojiPicker(false);
          }}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  );
};

export default MessageReactions;