import dotenv from 'dotenv';
import { generateInvoiceAuthCode, verifyInvoiceAuthCode } from '../utils/authUtils.js';
import { getFrontendUrl } from '../utils/emailUtils.js';

dotenv.config();

// Test invoice email URL generation
const testInvoiceEmailUrl = async () => {
  console.log('Testing invoice email URL generation...');
  
  // Mock invoice data
  const mockInvoice = {
    id: 'test-invoice-123',
    invoice_number: 'INV-001'
  };
  
  const mockCustomer = {
    id: 'test-customer-456'
  };
  
  const mockFarm = {
    id: 'test-farm-789',
    name: 'Test Farm'
  };
  
  try {
    // Test auth code generation
    console.log('1. Testing auth code generation...');
    const authCode = generateInvoiceAuthCode(mockInvoice.id, mockCustomer.id, mockFarm.id);
    console.log(`Generated auth code: ${authCode.substring(0, 20)}...`);
    
    // Test auth code verification
    console.log('2. Testing auth code verification...');
    const decoded = verifyInvoiceAuthCode(authCode);
    console.log('Decoded auth code:', {
      invoiceId: decoded.invoiceId,
      customerId: decoded.customerId,
      farmId: decoded.farmId,
      type: decoded.type
    });
    
    // Verify the decoded data matches
    if (decoded.invoiceId === mockInvoice.id && 
        decoded.customerId === mockCustomer.id && 
        decoded.farmId === mockFarm.id) {
      console.log('✅ Auth code verification successful');
    } else {
      console.log('❌ Auth code verification failed');
      return false;
    }
    
    // Test frontend URL generation
    console.log('3. Testing frontend URL generation...');
    const frontendUrl = await getFrontendUrl(null, mockFarm.id);
    console.log(`Frontend URL: ${frontendUrl}`);
    
    // Test complete invoice URL generation
    console.log('4. Testing complete invoice URL generation...');
    const invoiceUrl = `${frontendUrl}/invoices/${mockInvoice.id}/view?auth=${authCode}`;
    console.log(`Complete invoice URL: ${invoiceUrl}`);
    
    // Verify URL structure
    if (invoiceUrl.includes('/invoices/') && 
        invoiceUrl.includes('/view?auth=') && 
        invoiceUrl.includes(mockInvoice.id)) {
      console.log('✅ Invoice URL structure is correct');
    } else {
      console.log('❌ Invoice URL structure is incorrect');
      return false;
    }
    
    console.log('✅ All invoice email URL tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
};

// Test public invoice route accessibility
const testPublicInvoiceRoute = () => {
  console.log('Testing public invoice route configuration...');
  
  // Test route pattern matching
  const testRoutes = [
    '/invoices/test-123/view',
    '/invoices/abc-def-456/view',
    '/invoices/123-456-789-abc/view'
  ];
  
  const publicPathPattern = /^\/invoices\/[^\/]+\/view$/;
  
  let allPassed = true;
  
  testRoutes.forEach(route => {
    const matches = publicPathPattern.test(route);
    console.log(`Route ${route}: ${matches ? '✅ matches' : '❌ does not match'}`);
    if (!matches) allPassed = false;
  });
  
  // Test routes that should NOT match
  const invalidRoutes = [
    '/invoices/test-123/edit',
    '/invoices/test-123',
    '/invoices/test-123/view/extra',
    '/api/invoices/test-123/view'
  ];
  
  invalidRoutes.forEach(route => {
    const matches = publicPathPattern.test(route);
    console.log(`Invalid route ${route}: ${!matches ? '✅ correctly rejected' : '❌ incorrectly matched'}`);
    if (matches) allPassed = false;
  });
  
  if (allPassed) {
    console.log('✅ All public route pattern tests passed!');
  } else {
    console.log('❌ Some public route pattern tests failed!');
  }
  
  return allPassed;
};

// Run all tests
const runTests = async () => {
  console.log('Starting invoice email and routing tests...\n');
  
  try {
    const urlTestPassed = await testInvoiceEmailUrl();
    console.log('');
    const routeTestPassed = testPublicInvoiceRoute();
    
    if (urlTestPassed && routeTestPassed) {
      console.log('\n🎉 All tests passed! Invoice email links should now work correctly.');
    } else {
      console.log('\n❌ Some tests failed. Please check the implementation.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Test execution failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (process.argv[1].endsWith('invoiceEmailTest.js')) {
  runTests();
}

export { testInvoiceEmailUrl, testPublicInvoiceRoute, runTests };
