import { useState, useEffect } from 'react';
import { Doughn<PERSON> } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Too<PERSON><PERSON>,
  Legend,
  ChartOptions
} from 'chart.js';
import axios from 'axios';
import { API_URL } from '../config';

// Register Chart.js components
ChartJS.register(
  Arc<PERSON><PERSON>,
  Tooltip,
  Legend
);

interface TaskStats {
  completed: number;
  inProgress: number;
  notStarted: number;
  overdue: number;
}

interface TasksByCategory {
  [category: string]: TaskStats;
}

const TaskCompletionWidget = () => {
  const [taskStats, setTaskStats] = useState<TaskStats>({
    completed: 0,
    inProgress: 0,
    notStarted: 0,
    overdue: 0
  });
  const [tasksByCategory, setTasksByCategory] = useState<TasksByCategory>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'overall' | 'byCategory'>('overall');
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  // Fetch task statistics
  useEffect(() => {
    const fetchTaskStats = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/api/tasks/stats`, {
          withCredentials: true
        });
        
        if (response.data) {
          if (response.data.overall) {
            setTaskStats(response.data.overall);
          }
          
          if (response.data.byCategory) {
            setTasksByCategory(response.data.byCategory);
            // Set the first category as selected by default
            const categories = Object.keys(response.data.byCategory);
            if (categories.length > 0 && !selectedCategory) {
              setSelectedCategory(categories[0]);
            }
          }
        }
      } catch (err) {
        console.error('Error fetching task statistics:', err);
        setError('Failed to load task statistics. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTaskStats();
  }, []);

  // Handle view mode change
  const handleViewModeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setViewMode(e.target.value as 'overall' | 'byCategory');
  };

  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  // Get current stats based on view mode
  const currentStats = viewMode === 'overall' 
    ? taskStats 
    : (tasksByCategory[selectedCategory] || taskStats);

  // Prepare chart data
  const chartData = {
    labels: ['Completed', 'In Progress', 'Not Started', 'Overdue'],
    datasets: [
      {
        data: [
          currentStats.completed,
          currentStats.inProgress,
          currentStats.notStarted,
          currentStats.overdue
        ],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',  // Green for completed
          'rgba(54, 162, 235, 0.6)',  // Blue for in progress
          'rgba(201, 203, 207, 0.6)', // Gray for not started
          'rgba(255, 99, 132, 0.6)'   // Red for overdue
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(201, 203, 207, 1)',
          'rgba(255, 99, 132, 1)'
        ],
        borderWidth: 1
      }
    ]
  };

  // Chart options
  const chartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
      },
      title: {
        display: true,
        text: viewMode === 'overall' 
          ? 'Overall Task Completion' 
          : `Task Completion: ${selectedCategory}`,
        font: {
          size: 16
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw as number;
            const total = (context.dataset.data as number[]).reduce((a, b) => (a as number) + (b as number), 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Calculate total tasks
  const totalTasks = currentStats.completed + currentStats.inProgress + 
                     currentStats.notStarted + currentStats.overdue;
  
  // Calculate completion percentage
  const completionPercentage = totalTasks > 0 
    ? Math.round((currentStats.completed / totalTasks) * 100) 
    : 0;

  return (
    <div className="h-full flex flex-col">
      <div className="mb-4 flex flex-wrap justify-between items-center gap-2">
        <div className="text-sm font-medium text-gray-500 flex items-center">
          <span>View:</span>
          <select
            value={viewMode}
            onChange={handleViewModeChange}
            className="ml-2 p-1 border border-gray-300 rounded"
          >
            <option value="overall">Overall</option>
            <option value="byCategory">By Category</option>
          </select>
          
          {viewMode === 'byCategory' && (
            <select
              value={selectedCategory}
              onChange={handleCategoryChange}
              className="ml-2 p-1 border border-gray-300 rounded"
            >
              {Object.keys(tasksByCategory).map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          )}
        </div>
        
        <div className="flex space-x-4">
          <div className="text-sm">
            <span className="font-medium text-gray-500">Total Tasks:</span>
            <span className="ml-1">{totalTasks}</span>
          </div>
          <div className="text-sm">
            <span className="font-medium text-gray-500">Completion:</span>
            <span className="ml-1 text-green-600">{completionPercentage}%</span>
          </div>
        </div>
      </div>

      <div className="flex-grow relative">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">Loading task data...</p>
          </div>
        ) : error ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : totalTasks === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">No task data available.</p>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div style={{ height: '90%', width: '90%' }}>
              <Doughnut data={chartData} options={chartOptions} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskCompletionWidget;