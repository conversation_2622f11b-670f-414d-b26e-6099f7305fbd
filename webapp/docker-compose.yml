version: '3.8'

services:
  # Existing webapp service
  webapp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_MAIN_DOMAIN=${VITE_MAIN_DOMAIN}
        - VITE_API_URL=${VITE_API_URL}
        - API_URL=${API_URL}
        - SPACES_ENDPOINT=${SPACES_ENDPOINT}
        - SPACES_REGION=${SPACES_REGION}
        - SPACES_NAME=${SPACES_NAME}
        - SPACES_KEY=${SPACES_KEY}
        - SPACES_SECRET=${SPACES_SECRET}
        - SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
        - SENTRY_ORG=${SENTRY_ORG}
        - SENTRY_PROJECT=${SENTRY_PROJECT}
        - SENTRY_DSN=${SENTRY_DSN}
        - VITE_SENTRY_DSN=${VITE_SENTRY_DSN}
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_NAME=${DB_NAME:-farmbooks}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - DB_SCHEMA=${DB_SCHEMA:-site}
      - JWT_SECRET=${JWT_SECRET}
      - MATRIX_SERVER_URL=http://synapse:8008
      - MATRIX_DOMAIN=${MATRIX_DOMAIN:-nxtacre.local}
    depends_on:
      - postgres
      - synapse
    networks:
      - nxtacre-network

  # Matrix Synapse server
  synapse:
    image: matrixdotorg/synapse:latest
    volumes:
      - ./synapse-data:/data
    environment:
      - MATRIX_DOMAIN=${MATRIX_DOMAIN:-nxtacre.local}
      - SYNAPSE_REPORT_STATS=no
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - DB_NAME=${DB_NAME:-farmbooks}
      - JWT_SECRET=${JWT_SECRET}
      - REGISTRATION_SHARED_SECRET=${REGISTRATION_SHARED_SECRET:-$(openssl rand -hex 32)}
      - TURN_SHARED_SECRET=${TURN_SHARED_SECRET:-$(openssl rand -hex 32)}
      - MATRIX_ADMIN_USERNAME=${MATRIX_ADMIN_USERNAME:-admin}
      - MATRIX_ADMIN_PASSWORD=${MATRIX_ADMIN_PASSWORD:-$(openssl rand -hex 16)}
    ports:
      - "8008:8008"  # Matrix client-server API
    depends_on:
      - postgres
    networks:
      - nxtacre-network
    command: ["/bin/sh", "-c", "chmod +x /data/generate-config.sh && /data/generate-config.sh && python -m synapse.app.homeserver --config-path=/data/homeserver.yaml"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Matrix admin setup
  matrix-admin-setup:
    image: curlimages/curl:latest
    volumes:
      - ./synapse-data:/data
    environment:
      - MATRIX_DOMAIN=${MATRIX_DOMAIN:-nxtacre.local}
      - MATRIX_ADMIN_USERNAME=${MATRIX_ADMIN_USERNAME:-admin}
      - MATRIX_ADMIN_PASSWORD=${MATRIX_ADMIN_PASSWORD:-$(openssl rand -hex 16)}
      - REGISTRATION_SHARED_SECRET=${REGISTRATION_SHARED_SECRET:-$(openssl rand -hex 32)}
    depends_on:
      synapse:
        condition: service_healthy
    networks:
      - nxtacre-network
    command: ["/bin/sh", "-c", "chmod +x /data/create-admin-user.sh && /data/create-admin-user.sh"]

  # Matrix backup service
  matrix-backup:
    image: postgres:14
    volumes:
      - ./synapse-data:/data
      - ./backups:/backups
    environment:
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - DB_NAME=${DB_NAME:-farmbooks}
      - BACKUP_DESTINATION=${BACKUP_DESTINATION:-/backups}
      - PGPASSWORD=${DB_PASSWORD:-postgres}
    depends_on:
      synapse:
        condition: service_healthy
    networks:
      - nxtacre-network
    entrypoint: ["/bin/sh", "-c"]
    command: ["chmod +x /data/backup.sh && echo '0 2 * * * /data/backup.sh >> /data/backups/backup.log 2>&1' | crontab - && crond -f -d 8"]

  # PostgreSQL database (if not using an external one)
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=${DB_NAME:-farmbooks}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - nxtacre-network

networks:
  nxtacre-network:
    driver: bridge

volumes:
  postgres-data:
  synapse-data:
