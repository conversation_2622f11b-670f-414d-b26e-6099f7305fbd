import express from 'express';
import {
  getEquipmentTelematics,
  getLatestTelematics,
  createTelematics,
  getAggregatedTelematics,
  receiveExternalTelematics
} from '../controllers/telematicsController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get telematics data for a specific equipment item
router.get('/equipment/:equipmentId', authenticate, getEquipmentTelematics);

// Get the latest telematics data for an equipment item
router.get('/equipment/:equipmentId/latest', authenticate, getLatestTelematics);

// Create new telematics data record
router.post('/', authenticate, createTelematics);

// Get aggregated telematics data for reporting
router.get('/equipment/:equipmentId/aggregated', authenticate, getAggregatedTelematics);

// Receive telematics data from external systems
// This endpoint is typically not authenticated with the same mechanism
// as it's used by external systems
router.post('/external', receiveExternalTelematics);

export default router;
