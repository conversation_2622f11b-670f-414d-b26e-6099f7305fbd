-- Migration: Add location_data column to farms table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add location_data column to farms table if it doesn't exist
DO $$
DECLARE
  schema_name TEXT;
  column_exists BOOLEAN;
BEGIN
  -- Get the schema name
  SELECT current_schema() INTO schema_name;
  
  -- Check if the column already exists
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'site'
    AND table_name = 'farms'
    AND column_name = 'location_data'
  ) INTO column_exists;
  
  -- Add the column if it doesn't exist
  IF NOT column_exists THEN
    EXECUTE format('ALTER TABLE %I.farms ADD COLUMN location_data JSONB', schema_name);
    EXECUTE format('COMMENT ON COLUMN %I.farms.location_data IS ''GeoJSON for farm location''', schema_name);
    
    RAISE NOTICE 'The location_data column was successfully added to the farms table.';
  ELSE
    RAISE NOTICE 'The location_data column already exists in the farms table.';
  END IF;
  
  -- Verify the column was added
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'site'
    AND table_name = 'farms'
    AND column_name = 'location_data'
  ) INTO column_exists;
  
  IF column_exists THEN
    RAISE NOTICE 'The location_data column was successfully verified in the farms table.';
  ELSE
    RAISE EXCEPTION 'Failed to add the location_data column to the farms table.';
  END IF;
END $$;