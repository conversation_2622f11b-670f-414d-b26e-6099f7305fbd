{"name": "nxtacre-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-picker/picker": "2.4.10", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@react-navigation/stack": "^6.3.17", "axios": "^1.4.0", "expo": "~49.0.8", "expo-av": "~13.4.1", "expo-background-fetch": "~11.3.0", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-file-system": "~15.4.3", "expo-location": "~16.1.0", "expo-network": "~5.4.0", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-sharing": "~11.5.0", "expo-speech": "~11.3.0", "expo-sqlite": "~11.3.2", "expo-status-bar": "~1.6.0", "expo-task-manager": "~11.3.0", "react": "18.2.0", "react-native": "0.72.10", "react-native-ble-plx": "^2.0.3", "react-native-gesture-handler": "~2.12.0", "react-native-maps": "1.7.1", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "babel-plugin-module-resolver": "^5.0.0", "typescript": "^5.1.3"}, "private": true}