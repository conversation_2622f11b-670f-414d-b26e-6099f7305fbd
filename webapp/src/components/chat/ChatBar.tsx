import React, { useState, useEffect } from 'react';
import CloseIcon from './icons/CloseIcon';
import UnpinIcon from './icons/UnpinIcon';
import MessageArea from './MessageArea';
import { useChat } from '../../context/ChatContext';

interface ChatBarProps {
  onClose: () => void;
  onUnpin: () => void;
}

/**
 * Chat bar component that displays at the bottom of the screen when pinned
 */
const ChatBar: React.FC<ChatBarProps> = ({ onClose, onUnpin }) => {
  const { conversations, loadConversations } = useChat();
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [openConversations, setOpenConversations] = useState<string[]>([]);
  
  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);
  
  // Open a conversation
  const openConversation = (id: string) => {
    setActiveConversationId(id);
    if (!openConversations.includes(id)) {
      setOpenConversations([...openConversations, id]);
    }
  };
  
  // Close a specific conversation tab
  const closeConversation = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const newOpenConversations = openConversations.filter(convId => convId !== id);
    setOpenConversations(newOpenConversations);
    
    if (activeConversationId === id) {
      setActiveConversationId(newOpenConversations.length > 0 ? newOpenConversations[0] : null);
    }
  };
  
  return (
    <div className="bg-white border-t border-gray-200 shadow-lg flex flex-col h-96">
      {/* Header with tabs */}
      <div className="bg-gray-100 border-b border-gray-200 flex items-center">
        <div className="flex-1 flex overflow-x-auto">
          {openConversations.length === 0 ? (
            <div className="px-4 py-2 text-gray-500">No open conversations</div>
          ) : (
            openConversations.map(id => {
              const conversation = conversations.find(c => c.id === id);
              return (
                <div 
                  key={id}
                  onClick={() => openConversation(id)}
                  className={`px-4 py-2 flex items-center cursor-pointer ${
                    activeConversationId === id ? 'bg-white border-t-2 border-blue-500' : 'hover:bg-gray-200'
                  }`}
                >
                  <span className="mr-2 truncate max-w-xs">
                    {conversation?.name || 'Direct Message'}
                  </span>
                  <button
                    onClick={(e) => closeConversation(id, e)}
                    className="text-gray-500 hover:text-gray-700"
                    aria-label="Close conversation"
                  >
                    <CloseIcon size={14} />
                  </button>
                </div>
              );
            })
          )}
        </div>
        <div className="flex items-center px-2 space-x-2">
          <button
            onClick={onUnpin}
            className="text-gray-500 hover:text-gray-700 p-1"
            aria-label="Unpin chat"
          >
            <UnpinIcon size={18} />
          </button>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
            aria-label="Close chat"
          >
            <CloseIcon size={18} />
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeConversationId ? (
          <MessageArea 
            conversationId={activeConversationId}
            onBack={() => setActiveConversationId(null)}
            isPinned={true}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Select a conversation or start a new one
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatBar;