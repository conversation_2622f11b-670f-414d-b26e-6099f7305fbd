/**
 * Routes for cron job endpoints
 * These endpoints are protected by a secret key and are used for scheduled tasks
 */

import express from 'express';
import { 
  verifyCronSecret, 
  processAllAlerts, 
  runSystemMaintenance, 
  healthCheck,
  fetchAllFarmsWeather,
  fetchAndStoreGrants
} from '../controllers/cronController.js';

const router = express.Router();

// Apply the secret key verification middleware to all cron routes
router.use(verifyCronSecret);

// Route to process alert rules for all farms
router.post('/process-alerts', processAllAlerts);

// Route to run system maintenance tasks
router.post('/system-maintenance', runSystemMaintenance);

// Route to fetch and store weather data for all farms
router.post('/fetch-weather', fetchAllFarmsWeather);

// Route to fetch and store grants data from various sources
router.post('/fetch-grants', fetchAndStoreGrants);

// Health check endpoint for monitoring
router.get('/health', healthCheck);

export default router;
