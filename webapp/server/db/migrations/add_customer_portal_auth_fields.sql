-- Migration: Add authentication fields to the customers table for customer portal
-- Depends on: add_transport_and_receipt_tables.sql

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema name from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add password_hash column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'password_hash'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN password_hash VARCHAR(255)', schema_name);
    END IF;

    -- Add reset_password_token column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'reset_password_token'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN reset_password_token VARCHAR(255)', schema_name);
    END IF;

    -- Add reset_password_expires column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'reset_password_expires'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN reset_password_expires TIMESTAMP', schema_name);
    END IF;

    -- Add email_verified column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'email_verified'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN email_verified BOOLEAN NOT NULL DEFAULT FALSE', schema_name);
    END IF;

    -- Add email_verification_token column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'email_verification_token'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN email_verification_token VARCHAR(255)', schema_name);
    END IF;

    -- Add email_verification_expires column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'email_verification_expires'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN email_verification_expires TIMESTAMP', schema_name);
    END IF;

    -- Add portal_access column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'portal_access'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN portal_access BOOLEAN NOT NULL DEFAULT FALSE', schema_name);
    END IF;

    -- Record the migration in the database_migrations table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = schema_name AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status)
        VALUES (
            gen_random_uuid(),
            'Add customer portal authentication fields',
            'webapp/server/db/migrations/add_customer_portal_auth_fields.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied'
        );
    END IF;
END $$;