import Employee from '../models/Employee.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import { Op } from 'sequelize';

// Get all employees with optional filtering
export const getEmployees = async (req, res) => {
  try {
    const { farm_id, role, status } = req.query;

    // Build filter conditions
    const whereConditions = {};

    if (farm_id) {
      whereConditions.farm_id = farm_id;
    }

    if (role && role !== 'all') {
      whereConditions.role = role;
    }

    if (status && status !== 'all') {
      whereConditions.status = status;
    }

    // Find employees with filters
    const employees = await Employee.findAll({
      where: whereConditions,
      include: [
        {
          model: Farm,
          attributes: ['name'],
        }
      ],
      order: [['last_name', 'ASC'], ['first_name', 'ASC']]
    });

    // Format response data for regular employees
    const formattedEmployees = employees.map(employee => ({
      id: employee.id,
      farm_id: employee.farm_id,
      farm_name: employee.Farm ? employee.Farm.name : null,
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone,
      role: employee.role,
      status: employee.status,
      hire_date: employee.hire_date,
      hourly_rate: employee.hourly_rate,
      created_at: employee.created_at,
      updated_at: employee.updated_at,
      is_system_user: false
    }));

    // Find farm owners, admins, and accountants if farm_id is provided
    let additionalUsers = [];
    if (farm_id) {
      const userFarms = await UserFarm.findAll({
        where: {
          farm_id: farm_id,
          is_approved: true
        },
        include: [
          {
            model: User,
            attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number', 'created_at', 'updated_at']
          },
          {
            model: Farm,
            attributes: ['name']
          },
          {
            model: Role,
            attributes: ['id', 'name', 'description']
          }
        ]
      });

      // Format response data for farm owners, admins, and accountants
      additionalUsers = userFarms.map(userFarm => ({
        id: userFarm.user_id,
        farm_id: userFarm.farm_id,
        farm_name: userFarm.Farm ? userFarm.Farm.name : null,
        first_name: userFarm.User.first_name,
        last_name: userFarm.User.last_name,
        email: userFarm.User.email,
        phone: userFarm.User.phone_number,
        role: userFarm.role,
        role_id: userFarm.role_id,
        role_name: userFarm.Role ? userFarm.Role.name : null,
        role_description: userFarm.Role ? userFarm.Role.description : null,
        is_farm_owner: userFarm.Role ? userFarm.Role.name === 'Farm Owner' : false,
        status: 'active',
        hire_date: null,
        hourly_rate: null,
        created_at: userFarm.User.created_at,
        updated_at: userFarm.User.updated_at,
        is_system_user: true
      }));
    }

    // Combine regular employees with farm owners, admins, and accountants
    const allEmployees = [...formattedEmployees, ...additionalUsers];

    // Sort by last name, then first name
    allEmployees.sort((a, b) => {
      if (a.last_name !== b.last_name) {
        return a.last_name.localeCompare(b.last_name);
      }
      return a.first_name.localeCompare(b.first_name);
    });

    res.status(200).json(allEmployees);
  } catch (error) {
    console.error('Error fetching employees:', error);
    res.status(500).json({ message: 'Failed to fetch employees', error: error.message });
  }
};

// Get employee by ID
export const getEmployeeById = async (req, res) => {
  try {
    const { employeeId } = req.params;

    const employee = await Employee.findByPk(employeeId, {
      include: [
        {
          model: Farm,
          attributes: ['name'],
        }
      ]
    });

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    // Format response data
    const formattedEmployee = {
      id: employee.id,
      farm_id: employee.farm_id,
      farm_name: employee.Farm ? employee.Farm.name : null,
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone,
      role: employee.role,
      status: employee.status,
      hire_date: employee.hire_date,
      hourly_rate: employee.hourly_rate,
      address: employee.address,
      city: employee.city,
      state: employee.state,
      zip_code: employee.zip_code,
      emergency_contact_name: employee.emergency_contact_name,
      emergency_contact_phone: employee.emergency_contact_phone,
      notes: employee.notes,
      created_at: employee.created_at,
      updated_at: employee.updated_at
    };

    res.status(200).json(formattedEmployee);
  } catch (error) {
    console.error('Error fetching employee:', error);
    res.status(500).json({ message: 'Failed to fetch employee', error: error.message });
  }
};

// Create a new employee
export const createEmployee = async (req, res) => {
  try {
    const employeeData = req.body;

    // Validate required fields
    if (!employeeData.farm_id || !employeeData.first_name || !employeeData.last_name) {
      return res.status(400).json({ message: 'Farm ID, first name, and last name are required' });
    }

    // Create the employee
    const newEmployee = await Employee.create(employeeData);

    res.status(201).json(newEmployee);
  } catch (error) {
    console.error('Error creating employee:', error);
    res.status(500).json({ message: 'Failed to create employee', error: error.message });
  }
};

// Update an employee
export const updateEmployee = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const employeeData = req.body;

    // Find the employee
    const employee = await Employee.findByPk(employeeId);

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    // Update the employee
    await employee.update(employeeData);

    res.status(200).json(employee);
  } catch (error) {
    console.error('Error updating employee:', error);
    res.status(500).json({ message: 'Failed to update employee', error: error.message });
  }
};

// Delete an employee
export const deleteEmployee = async (req, res) => {
  try {
    const { employeeId } = req.params;

    // Find the employee
    const employee = await Employee.findByPk(employeeId);

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    // Delete the employee
    await employee.destroy();

    res.status(200).json({ message: 'Employee deleted successfully' });
  } catch (error) {
    console.error('Error deleting employee:', error);
    res.status(500).json({ message: 'Failed to delete employee', error: error.message });
  }
};

// Get employees by farm ID
export const getEmployeesByFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Get regular employees
    const employees = await Employee.findAll({
      where: { farm_id: farmId },
      order: [['last_name', 'ASC'], ['first_name', 'ASC']]
    });

    // Format response data for regular employees
    const formattedEmployees = employees.map(employee => ({
      id: employee.id,
      farm_id: employee.farm_id,
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone,
      role: employee.role,
      status: employee.status,
      hire_date: employee.hire_date,
      hourly_rate: employee.hourly_rate,
      created_at: employee.created_at,
      updated_at: employee.updated_at,
      is_system_user: false
    }));

    // Find farm owners, admins, and accountants
    const userFarms = await UserFarm.findAll({
      where: {
        farm_id: farmId,
        is_approved: true
      },
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number', 'created_at', 'updated_at']
        },
        {
          model: Role,
          attributes: ['id', 'name', 'description']
        }
      ]
    });

    // Format response data for farm owners, admins, and accountants
    const additionalUsers = userFarms.map(userFarm => ({
      id: userFarm.user_id,
      farm_id: userFarm.farm_id,
      first_name: userFarm.User.first_name,
      last_name: userFarm.User.last_name,
      email: userFarm.User.email,
      phone: userFarm.User.phone_number,
      role: userFarm.role,
      role_id: userFarm.role_id,
      role_name: userFarm.Role ? userFarm.Role.name : null,
      role_description: userFarm.Role ? userFarm.Role.description : null,
      is_farm_owner: userFarm.Role ? userFarm.Role.name === 'Farm Owner' : false,
      status: 'active',
      hire_date: null,
      hourly_rate: null,
      created_at: userFarm.User.created_at,
      updated_at: userFarm.User.updated_at,
      is_system_user: true
    }));

    // Combine regular employees with farm owners, admins, and accountants
    const allEmployees = [...formattedEmployees, ...additionalUsers];

    // Sort by last name, then first name
    allEmployees.sort((a, b) => {
      if (a.last_name !== b.last_name) {
        return a.last_name.localeCompare(b.last_name);
      }
      return a.first_name.localeCompare(b.first_name);
    });

    res.status(200).json(allEmployees);
  } catch (error) {
    console.error('Error fetching farm employees:', error);
    res.status(500).json({ message: 'Failed to fetch farm employees', error: error.message });
  }
};

// Search employees
export const searchEmployees = async (req, res) => {
  try {
    const { query, farm_id } = req.query;

    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    // Build search conditions for employees
    const whereConditions = {
      [Op.or]: [
        { first_name: { [Op.iLike]: `%${query}%` } },
        { last_name: { [Op.iLike]: `%${query}%` } },
        { email: { [Op.iLike]: `%${query}%` } }
      ]
    };

    if (farm_id) {
      whereConditions.farm_id = farm_id;
    }

    // Search regular employees
    const employees = await Employee.findAll({
      where: whereConditions,
      include: [
        {
          model: Farm,
          attributes: ['name'],
        }
      ],
      order: [['last_name', 'ASC'], ['first_name', 'ASC']]
    });

    // Format response data for regular employees
    const formattedEmployees = employees.map(employee => ({
      id: employee.id,
      farm_id: employee.farm_id,
      farm_name: employee.Farm ? employee.Farm.name : null,
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone,
      role: employee.role,
      status: employee.status,
      hire_date: employee.hire_date,
      hourly_rate: employee.hourly_rate,
      created_at: employee.created_at,
      updated_at: employee.updated_at,
      is_system_user: false
    }));

    // Search for farm owners, admins, and accountants
    let additionalUsers = [];
    if (farm_id) {
      // Build search conditions for UserFarm
      const userFarmWhere = {
        farm_id: farm_id,
        role: {
          [Op.in]: ['farm_owner', 'farm_admin', 'accountant']
        },
        is_approved: true
      };

      // Build search conditions for User
      const userWhere = {
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${query}%` } },
          { last_name: { [Op.iLike]: `%${query}%` } },
          { email: { [Op.iLike]: `%${query}%` } }
        ]
      };

      const userFarms = await UserFarm.findAll({
        where: userFarmWhere,
        include: [
          {
            model: User,
            attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number', 'created_at', 'updated_at'],
            where: userWhere
          },
          {
            model: Farm,
            attributes: ['name']
          },
          {
            model: Role,
            attributes: ['id', 'name', 'description']
          }
        ]
      });

      // Format response data for farm owners, admins, and accountants
      additionalUsers = userFarms.map(userFarm => ({
        id: userFarm.user_id,
        farm_id: userFarm.farm_id,
        farm_name: userFarm.Farm ? userFarm.Farm.name : null,
        first_name: userFarm.User.first_name,
        last_name: userFarm.User.last_name,
        email: userFarm.User.email,
        phone: userFarm.User.phone_number,
        role: userFarm.role,
        role_id: userFarm.role_id,
        role_name: userFarm.Role ? userFarm.Role.name : null,
        role_description: userFarm.Role ? userFarm.Role.description : null,
        is_farm_owner: userFarm.Role ? userFarm.Role.name === 'Farm Owner' : false,
        status: 'active',
        hire_date: null,
        hourly_rate: null,
        created_at: userFarm.User.created_at,
        updated_at: userFarm.User.updated_at,
        is_system_user: true
      }));
    }

    // Combine regular employees with farm owners, admins, and accountants
    const allEmployees = [...formattedEmployees, ...additionalUsers];

    // Sort by last name, then first name
    allEmployees.sort((a, b) => {
      if (a.last_name !== b.last_name) {
        return a.last_name.localeCompare(b.last_name);
      }
      return a.first_name.localeCompare(b.first_name);
    });

    res.status(200).json(allEmployees);
  } catch (error) {
    console.error('Error searching employees:', error);
    res.status(500).json({ message: 'Failed to search employees', error: error.message });
  }
};
