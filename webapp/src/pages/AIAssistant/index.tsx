import React, { useState, useEffect, useRef } from 'react';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { useLocation } from 'react-router-dom';
import { 
  getAIResponse, 
  getQueryHistory, 
  getSuggestions,
  getDecisionSupport,
  updateDecisionSupport,
  getPredictiveMaintenance,
  updatePredictiveMaintenance,
  getHarvestRecommendations,
  updateHarvestRecommendation,
  getFieldImprovementRecommendations,
  updateFieldImprovementRecommendation,
  getFinancialRecommendations,
  updateFinancialRecommendation,
  getYieldProfitRecommendations,
  updateYieldProfitRecommendation,
  DecisionSupport,
  PredictiveMaintenance,
  HarvestRecommendation,
  FieldImprovementRecommendation,
  FinancialRecommendation,
  YieldProfitRecommendation
} from '../../services/aiAssistantService';
import { Title, Meta } from 'react-head';
import Layout from '../../components/Layout';

const AIAssistant: React.FC = () => {
  const { currentFarm } = useFarm();
  const location = useLocation();
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversation, setConversation] = useState<{ query: string; response: string }[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [suggestionsLoading, setSuggestionsLoading] = useState(false);
  const [suggestionsError, setSuggestionsError] = useState<string | null>(null);
  const [queryHistory, setQueryHistory] = useState<{ query: string; response: string; created_at: string }[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State for Decision Support System
  const [decisionSupport, setDecisionSupport] = useState<DecisionSupport[]>([]);
  const [loadingDecisionSupport, setLoadingDecisionSupport] = useState(false);

  // State for Predictive Maintenance
  const [predictiveMaintenance, setPredictiveMaintenance] = useState<PredictiveMaintenance[]>([]);
  const [loadingPredictiveMaintenance, setLoadingPredictiveMaintenance] = useState(false);

  // State for Harvest Recommendations
  const [harvestRecommendations, setHarvestRecommendations] = useState<HarvestRecommendation[]>([]);
  const [loadingHarvestRecommendations, setLoadingHarvestRecommendations] = useState(false);

  // State for Field Improvement Recommendations
  const [fieldImprovementRecommendations, setFieldImprovementRecommendations] = useState<FieldImprovementRecommendation[]>([]);
  const [loadingFieldImprovementRecommendations, setLoadingFieldImprovementRecommendations] = useState(false);

  // State for Financial Recommendations
  const [financialRecommendations, setFinancialRecommendations] = useState<FinancialRecommendation[]>([]);
  const [loadingFinancialRecommendations, setLoadingFinancialRecommendations] = useState(false);

  // State for Yield/Profit Recommendations
  const [yieldProfitRecommendations, setYieldProfitRecommendations] = useState<YieldProfitRecommendation[]>([]);
  const [loadingYieldProfitRecommendations, setLoadingYieldProfitRecommendations] = useState(false);

  // Tab state
  const [activeTab, setActiveTab] = useState<
    'chat' | 
    'decision-support' | 
    'predictive-maintenance' | 
    'harvest-recommendations' | 
    'field-improvement' | 
    'financial-optimization' | 
    'yield-profit'
  >('chat');

  // Set active tab based on URL query parameter
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');

    if (tabParam) {
      // Check if the tab parameter is valid
      const validTabs = [
        'chat',
        'decision-support',
        'predictive-maintenance',
        'harvest-recommendations',
        'field-improvement',
        'financial-optimization',
        'yield-profit'
      ];

      if (validTabs.includes(tabParam)) {
        setActiveTab(tabParam as any);
      }
    }
  }, [location.search]);

  // Fetch data when component mounts or farm changes
  useEffect(() => {
    if (currentFarm?.id) {
      fetchSuggestions();
      fetchQueryHistory();
      fetchDecisionSupport();
      fetchPredictiveMaintenance();
      fetchHarvestRecommendations();
      fetchFieldImprovementRecommendations();
      fetchFinancialRecommendations();
      fetchYieldProfitRecommendations();
    }
  }, [currentFarm]);

  // Scroll to bottom of conversation when it updates
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation]);

  const fetchSuggestions = async () => {
    try {
      if (!currentFarm?.id) return;
      setSuggestionsLoading(true);
      setSuggestionsError(null);
      const suggestions = await getSuggestions(currentFarm.id);
      setSuggestions(suggestions);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      setSuggestionsError('Failed to fetch AI suggestions. Please try again.');
      toast.error('Failed to fetch AI suggestions');
    } finally {
      setSuggestionsLoading(false);
    }
  };

  const fetchQueryHistory = async () => {
    try {
      if (!currentFarm?.id) return;
      const history = await getQueryHistory(currentFarm.id);
      setQueryHistory(history);
    } catch (error) {
      console.error('Error fetching query history:', error);
      toast.error('Failed to fetch query history');
    }
  };

  const fetchDecisionSupport = async () => {
    try {
      if (!currentFarm?.id) return;
      setLoadingDecisionSupport(true);
      const recommendations = await getDecisionSupport(currentFarm.id);
      setDecisionSupport(recommendations);
    } catch (error) {
      console.error('Error fetching decision support recommendations:', error);
      toast.error('Failed to fetch decision support recommendations');
    } finally {
      setLoadingDecisionSupport(false);
    }
  };

  const fetchPredictiveMaintenance = async () => {
    try {
      if (!currentFarm?.id) return;
      setLoadingPredictiveMaintenance(true);
      const predictions = await getPredictiveMaintenance(currentFarm.id);
      setPredictiveMaintenance(predictions);
    } catch (error) {
      console.error('Error fetching predictive maintenance predictions:', error);
      toast.error('Failed to fetch predictive maintenance predictions');
    } finally {
      setLoadingPredictiveMaintenance(false);
    }
  };

  const fetchHarvestRecommendations = async () => {
    try {
      if (!currentFarm?.id) return;
      setLoadingHarvestRecommendations(true);
      const recommendations = await getHarvestRecommendations(currentFarm.id);
      setHarvestRecommendations(recommendations);
    } catch (error) {
      console.error('Error fetching harvest recommendations:', error);
      toast.error('Failed to fetch harvest recommendations');
    } finally {
      setLoadingHarvestRecommendations(false);
    }
  };

  const fetchFieldImprovementRecommendations = async () => {
    try {
      if (!currentFarm?.id) return;
      setLoadingFieldImprovementRecommendations(true);
      const recommendations = await getFieldImprovementRecommendations(currentFarm.id);
      setFieldImprovementRecommendations(recommendations);
    } catch (error) {
      console.error('Error fetching field improvement recommendations:', error);
      toast.error('Failed to fetch field improvement recommendations');
    } finally {
      setLoadingFieldImprovementRecommendations(false);
    }
  };

  const fetchFinancialRecommendations = async () => {
    try {
      if (!currentFarm?.id) return;
      setLoadingFinancialRecommendations(true);
      const recommendations = await getFinancialRecommendations(currentFarm.id);
      setFinancialRecommendations(recommendations);
    } catch (error) {
      console.error('Error fetching financial recommendations:', error);
      toast.error('Failed to fetch financial recommendations');
    } finally {
      setLoadingFinancialRecommendations(false);
    }
  };

  const fetchYieldProfitRecommendations = async () => {
    try {
      if (!currentFarm?.id) return;
      setLoadingYieldProfitRecommendations(true);
      const recommendations = await getYieldProfitRecommendations(currentFarm.id);
      setYieldProfitRecommendations(recommendations);
    } catch (error) {
      console.error('Error fetching yield/profit recommendations:', error);
      toast.error('Failed to fetch yield/profit recommendations');
    } finally {
      setLoadingYieldProfitRecommendations(false);
    }
  };

  const handleImplementRecommendation = async (id: number, isImplemented: boolean) => {
    try {
      await updateDecisionSupport(id, isImplemented);
      fetchDecisionSupport(); // Refresh the list
      toast.success(isImplemented ? 'Recommendation marked as implemented' : 'Recommendation marked as not implemented');
    } catch (error) {
      console.error('Error updating recommendation:', error);
      toast.error('Failed to update recommendation');
    }
  };

  const handleAddressPrediction = async (id: number, isAddressed: boolean) => {
    try {
      await updatePredictiveMaintenance(id, isAddressed);
      fetchPredictiveMaintenance(); // Refresh the list
      toast.success(isAddressed ? 'Maintenance issue marked as addressed' : 'Maintenance issue marked as not addressed');
    } catch (error) {
      console.error('Error updating prediction:', error);
      toast.error('Failed to update maintenance issue');
    }
  };

  const handleImplementHarvestRecommendation = async (id: number, isImplemented: boolean) => {
    try {
      await updateHarvestRecommendation(id, isImplemented);
      fetchHarvestRecommendations(); // Refresh the list
      toast.success(isImplemented ? 'Harvest recommendation marked as implemented' : 'Harvest recommendation marked as not implemented');
    } catch (error) {
      console.error('Error updating harvest recommendation:', error);
      toast.error('Failed to update harvest recommendation');
    }
  };

  const handleImplementFieldImprovementRecommendation = async (id: number, isImplemented: boolean) => {
    try {
      await updateFieldImprovementRecommendation(id, isImplemented);
      fetchFieldImprovementRecommendations(); // Refresh the list
      toast.success(isImplemented ? 'Field improvement recommendation marked as implemented' : 'Field improvement recommendation marked as not implemented');
    } catch (error) {
      console.error('Error updating field improvement recommendation:', error);
      toast.error('Failed to update field improvement recommendation');
    }
  };

  const handleImplementFinancialRecommendation = async (id: number, isImplemented: boolean) => {
    try {
      await updateFinancialRecommendation(id, isImplemented);
      fetchFinancialRecommendations(); // Refresh the list
      toast.success(isImplemented ? 'Financial recommendation marked as implemented' : 'Financial recommendation marked as not implemented');
    } catch (error) {
      console.error('Error updating financial recommendation:', error);
      toast.error('Failed to update financial recommendation');
    }
  };

  const handleImplementYieldProfitRecommendation = async (id: number, isImplemented: boolean) => {
    try {
      await updateYieldProfitRecommendation(id, isImplemented);
      fetchYieldProfitRecommendations(); // Refresh the list
      toast.success(isImplemented ? 'Yield/profit recommendation marked as implemented' : 'Yield/profit recommendation marked as not implemented');
    } catch (error) {
      console.error('Error updating yield/profit recommendation:', error);
      toast.error('Failed to update yield/profit recommendation');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!query.trim()) return;
    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    setIsLoading(true);

    try {
      const response = await getAIResponse(query, currentFarm.id);
      setConversation([...conversation, { query, response }]);
      setQuery('');

      // Refresh query history
      fetchQueryHistory();
    } catch (error) {
      console.error('Error getting AI response:', error);
      toast.error('Failed to get AI response');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle keyboard events for accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // If Enter is pressed without Shift, submit the form
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (query.trim()) {
        handleSubmit(e as unknown as React.FormEvent);
      }
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
  };

  const handleHistoryItemClick = (historyItem: { query: string; response: string }) => {
    setConversation([...conversation, historyItem]);
    setShowHistory(false);
  };

  // Head tags are now added directly in the JSX

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <Title>AI Farming Assistant | NxtAcre</Title>
        <Meta name="description" content="Get intelligent farming assistance, personalized suggestions, decision support, and predictive maintenance for your farm." />
        <h1 className="text-2xl font-bold mb-6">AI Farming Assistant</h1>

      {/* Tab Navigation */}
      <div className="mb-6 border-b border-gray-200 overflow-x-auto">
        <ul className="flex -mb-px whitespace-nowrap">
          <li className="mr-1 sm:mr-2">
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'chat'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('chat')}
              aria-pressed={activeTab === 'chat'}
              aria-label="Chat tab"
            >
              Chat
            </button>
          </li>
          <li className="mr-1 sm:mr-2">
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'decision-support'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('decision-support')}
              aria-pressed={activeTab === 'decision-support'}
              aria-label="Decision Support tab"
            >
              <span className="hidden sm:inline">Decision Support</span>
              <span className="sm:hidden">Decisions</span>
            </button>
          </li>
          <li className="mr-1 sm:mr-2">
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'predictive-maintenance'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('predictive-maintenance')}
              aria-pressed={activeTab === 'predictive-maintenance'}
              aria-label="Predictive Maintenance tab"
            >
              <span className="hidden sm:inline">Predictive Maintenance</span>
              <span className="sm:hidden">Maintenance</span>
            </button>
          </li>
          <li className="mr-1 sm:mr-2">
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'harvest-recommendations'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('harvest-recommendations')}
              aria-pressed={activeTab === 'harvest-recommendations'}
              aria-label="Harvest Recommendations tab"
            >
              <span className="hidden sm:inline">Harvest Timing</span>
              <span className="sm:hidden">Harvest</span>
            </button>
          </li>
          <li className="mr-1 sm:mr-2">
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'field-improvement'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('field-improvement')}
              aria-pressed={activeTab === 'field-improvement'}
              aria-label="Field Improvement tab"
            >
              <span className="hidden sm:inline">Field Improvement</span>
              <span className="sm:hidden">Fields</span>
            </button>
          </li>
          <li className="mr-1 sm:mr-2">
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'financial-optimization'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('financial-optimization')}
              aria-pressed={activeTab === 'financial-optimization'}
              aria-label="Financial Optimization tab"
            >
              <span className="hidden sm:inline">Financial Optimization</span>
              <span className="sm:hidden">Finance</span>
            </button>
          </li>
          <li>
            <button
              className={`inline-block py-2 px-2 sm:px-4 text-xs sm:text-sm font-medium ${
                activeTab === 'yield-profit'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('yield-profit')}
              aria-pressed={activeTab === 'yield-profit'}
              aria-label="Yield/Profit tab"
            >
              <span className="hidden sm:inline">Yield & Profit</span>
              <span className="sm:hidden">Yield</span>
            </button>
          </li>
        </ul>
      </div>

      {/* Chat Tab Content */}
      {activeTab === 'chat' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Main conversation area */}
          <div className="md:col-span-2 bg-white rounded-lg shadow-md p-4">
            <div className="h-64 sm:h-96 overflow-y-auto mb-4 p-4 bg-gray-50 rounded-lg">
              {conversation.length === 0 ? (
                <div className="text-center text-gray-500 mt-32">
                  <p className="text-lg font-semibold">Ask me anything about farming!</p>
                  <p className="text-sm">I can help with crop rotation, pest management, fertilization, weather impacts, and more.</p>
                </div>
              ) : (
                conversation.map((item, index) => (
                  <div key={index} className="mb-4">
                    <div className="flex items-start mb-2">
                      <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 font-semibold mr-2">
                        You
                      </div>
                      <div className="bg-primary-50 p-3 rounded-lg max-w-3xl">
                        <p>{item.query}</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center text-green-700 font-semibold mr-2">
                        AI
                      </div>
                      <div className="bg-green-50 p-3 rounded-lg max-w-3xl">
                        <p>{item.response}</p>
                      </div>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>

            <form onSubmit={handleSubmit} className="flex items-center">
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask about farming practices, crop management, etc."
                className="flex-grow p-3 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                disabled={isLoading}
                aria-label="Ask a question about farming"
              />
              <button
                type="submit"
                className="bg-primary-600 text-white p-3 rounded-r-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
                disabled={isLoading || !query.trim()}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing
                  </span>
                ) : (
                  'Ask'
                )}
              </button>
            </form>
          </div>

          {/* Sidebar with suggestions and history */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <h2 className="text-lg font-semibold">Suggestions</h2>
                <button 
                  onClick={fetchSuggestions}
                  className="text-primary-600 hover:text-primary-800 text-sm"
                >
                  Refresh
                </button>
              </div>
              <div className="space-y-2">
                {suggestionsLoading ? (
                  <div className="flex justify-center items-center h-24">
                    <svg className="animate-spin h-6 w-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                ) : suggestionsError ? (
                  <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                    {suggestionsError}
                    <button 
                      onClick={fetchSuggestions}
                      className="mt-2 text-primary-600 hover:text-primary-800 underline text-xs"
                    >
                      Try again
                    </button>
                  </div>
                ) : suggestions.length > 0 ? (
                  suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded-md text-sm transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))
                ) : (
                  <div className="p-3 bg-gray-50 text-gray-500 rounded-md text-sm">
                    No suggestions available. Try refreshing.
                  </div>
                )}
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <h2 className="text-lg font-semibold">Recent Questions</h2>
                <button 
                  onClick={() => setShowHistory(!showHistory)}
                  className="text-primary-600 hover:text-primary-800 text-sm"
                >
                  {showHistory ? 'Hide' : 'Show'}
                </button>
              </div>
              {showHistory && (
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {queryHistory.length > 0 ? (
                    queryHistory.map((item, index) => (
                      <button
                        key={index}
                        onClick={() => handleHistoryItemClick(item)}
                        className="w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded-md text-sm transition-colors"
                      >
                        <p className="font-medium truncate">{item.query}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(item.created_at).toLocaleDateString()} {new Date(item.created_at).toLocaleTimeString()}
                        </p>
                      </button>
                    ))
                  ) : (
                    <p className="text-gray-500 text-sm">No recent questions</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Decision Support Tab Content */}
      {activeTab === 'decision-support' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">AI-Driven Decision Support</h2>
            <button 
              onClick={fetchDecisionSupport}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={loadingDecisionSupport}
            >
              {loadingDecisionSupport ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          <p className="mb-6 text-gray-600">
            Our AI analyzes your farm data to provide recommendations for optimal farming operations. 
            These recommendations are based on weather patterns, soil conditions, crop history, and market trends.
          </p>

          {loadingDecisionSupport ? (
            <div className="flex justify-center items-center h-64">
              <svg className="animate-spin h-10 w-10 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : decisionSupport.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No recommendations available at this time.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {decisionSupport.map((recommendation) => (
                <div 
                  key={recommendation.id} 
                  className={`border rounded-lg p-4 ${recommendation.is_implemented ? 'bg-green-50 border-green-200' : 'bg-white'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg">{recommendation.operation_type}</h3>
                      <p className="mt-2">{recommendation.recommendation}</p>
                      <div className="mt-3 text-sm text-gray-600">
                        <p><span className="font-medium">Factors considered:</span> {recommendation.factors_considered}</p>
                        <p className="mt-1"><span className="font-medium">Confidence score:</span> {recommendation.confidence_score}%</p>
                        <p className="mt-1"><span className="font-medium">Generated:</span> {new Date(recommendation.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleImplementRecommendation(recommendation.id, !recommendation.is_implemented)}
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          recommendation.is_implemented
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        }`}
                      >
                        {recommendation.is_implemented ? 'Implemented ✓' : 'Mark as Implemented'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Predictive Maintenance Tab Content */}
      {activeTab === 'predictive-maintenance' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Predictive Equipment Maintenance</h2>
            <button 
              onClick={fetchPredictiveMaintenance}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={loadingPredictiveMaintenance}
            >
              {loadingPredictiveMaintenance ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          <p className="mb-6 text-gray-600">
            Our AI analyzes equipment usage patterns and sensor data to predict maintenance needs before failures occur.
            This helps prevent costly breakdowns during critical farming operations.
          </p>

          {loadingPredictiveMaintenance ? (
            <div className="flex justify-center items-center h-64">
              <svg className="animate-spin h-10 w-10 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : predictiveMaintenance.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No maintenance predictions available at this time.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {predictiveMaintenance.map((prediction) => (
                <div 
                  key={prediction.id} 
                  className={`border rounded-lg p-4 ${
                    prediction.is_addressed 
                      ? 'bg-green-50 border-green-200' 
                      : prediction.urgency_level === 'High' 
                        ? 'bg-red-50 border-red-200'
                        : prediction.urgency_level === 'Medium'
                          ? 'bg-yellow-50 border-yellow-200'
                          : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-semibold text-lg">{prediction.equipment_name}</h3>
                        <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${
                          prediction.urgency_level === 'High' 
                            ? 'bg-red-100 text-red-800' 
                            : prediction.urgency_level === 'Medium'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                        }`}>
                          {prediction.urgency_level} Priority
                        </span>
                      </div>
                      <p className="mt-1 text-sm font-medium text-gray-600">{prediction.maintenance_type}</p>
                      <p className="mt-2">{prediction.prediction}</p>
                      <div className="mt-3 text-sm text-gray-600">
                        {prediction.predicted_failure_date && (
                          <p><span className="font-medium">Predicted failure date:</span> {new Date(prediction.predicted_failure_date).toLocaleDateString()}</p>
                        )}
                        <p className="mt-1"><span className="font-medium">Confidence score:</span> {prediction.confidence_score}%</p>
                        <p className="mt-1"><span className="font-medium">Generated:</span> {new Date(prediction.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleAddressPrediction(prediction.id, !prediction.is_addressed)}
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          prediction.is_addressed
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        }`}
                      >
                        {prediction.is_addressed ? 'Addressed ✓' : 'Mark as Addressed'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Harvest Recommendations Tab Content */}
      {activeTab === 'harvest-recommendations' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Harvest Timing Recommendations</h2>
            <button 
              onClick={fetchHarvestRecommendations}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={loadingHarvestRecommendations}
            >
              {loadingHarvestRecommendations ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          <p className="mb-6 text-gray-600">
            Our AI analyzes crop maturity, weather forecasts, and historical data to recommend optimal harvest timing.
            Harvesting at the right time maximizes yield quality and minimizes post-harvest losses.
          </p>

          {loadingHarvestRecommendations ? (
            <div className="flex justify-center items-center h-64">
              <svg className="animate-spin h-10 w-10 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : harvestRecommendations.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No harvest timing recommendations available at this time.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {harvestRecommendations.map((recommendation) => (
                <div 
                  key={recommendation.id} 
                  className={`border rounded-lg p-4 ${recommendation.is_implemented ? 'bg-green-50 border-green-200' : 'bg-white'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-semibold text-lg">{recommendation.field_name}</h3>
                        <span className="ml-3 px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                          {recommendation.crop_type}
                        </span>
                      </div>
                      <p className="mt-1 text-sm font-medium text-gray-600">Recommended harvest: {recommendation.harvest_date}</p>
                      <p className="mt-2">{recommendation.explanation}</p>
                      <div className="mt-3 text-sm text-gray-600">
                        <p><span className="font-medium">Factors considered:</span> {recommendation.factors_considered}</p>
                        <p className="mt-1"><span className="font-medium">Confidence score:</span> {recommendation.confidence_score}%</p>
                        <p className="mt-1"><span className="font-medium">Generated:</span> {new Date(recommendation.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleImplementHarvestRecommendation(recommendation.id, !recommendation.is_implemented)}
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          recommendation.is_implemented
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        }`}
                      >
                        {recommendation.is_implemented ? 'Implemented ✓' : 'Mark as Implemented'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Yield and Profit Maximization Tab Content */}
      {activeTab === 'yield-profit' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Yield & Profit Maximization Recommendations</h2>
            <button 
              onClick={fetchYieldProfitRecommendations}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={loadingYieldProfitRecommendations}
            >
              {loadingYieldProfitRecommendations ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          <p className="mb-6 text-gray-600">
            Our AI analyzes your farm's production data, soil conditions, and market trends to recommend strategies for maximizing crop yields and overall farm profitability.
            These recommendations focus on optimizing both production and financial outcomes.
          </p>

          {loadingYieldProfitRecommendations ? (
            <div className="flex justify-center items-center h-64">
              <svg className="animate-spin h-10 w-10 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : yieldProfitRecommendations.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No yield and profit maximization recommendations available at this time.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {yieldProfitRecommendations.map((recommendation) => (
                <div 
                  key={recommendation.id} 
                  className={`border rounded-lg p-4 ${recommendation.is_implemented ? 'bg-green-50 border-green-200' : 'bg-white'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-semibold text-lg">{recommendation.category}</h3>
                        <span className="ml-3 px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {recommendation.implementation_timeframe}
                        </span>
                      </div>
                      <p className="mt-2">{recommendation.recommendation}</p>
                      <div className="mt-3 text-sm text-gray-600">
                        <p><span className="font-medium">Yield impact:</span> {recommendation.yield_impact}</p>
                        <p className="mt-1"><span className="font-medium">Profit impact:</span> {recommendation.profit_impact}</p>
                        <p className="mt-1"><span className="font-medium">Confidence score:</span> {recommendation.confidence_score}%</p>
                        <p className="mt-1"><span className="font-medium">Generated:</span> {new Date(recommendation.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleImplementYieldProfitRecommendation(recommendation.id, !recommendation.is_implemented)}
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          recommendation.is_implemented
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        }`}
                      >
                        {recommendation.is_implemented ? 'Implemented ✓' : 'Mark as Implemented'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Financial Optimization Tab Content */}
      {activeTab === 'financial-optimization' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Financial Optimization Recommendations</h2>
            <button 
              onClick={fetchFinancialRecommendations}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={loadingFinancialRecommendations}
            >
              {loadingFinancialRecommendations ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          <p className="mb-6 text-gray-600">
            Our AI analyzes your farm's financial data, market trends, and expense patterns to recommend strategies for optimizing costs and increasing revenue.
            These recommendations can help improve your farm's profitability and financial sustainability.
          </p>

          {loadingFinancialRecommendations ? (
            <div className="flex justify-center items-center h-64">
              <svg className="animate-spin h-10 w-10 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : financialRecommendations.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No financial optimization recommendations available at this time.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {financialRecommendations.map((recommendation) => (
                <div 
                  key={recommendation.id} 
                  className={`border rounded-lg p-4 ${recommendation.is_implemented ? 'bg-green-50 border-green-200' : 'bg-white'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-semibold text-lg">{recommendation.category}</h3>
                        <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${
                          recommendation.implementation_difficulty === 'Easy' 
                            ? 'bg-green-100 text-green-800' 
                            : recommendation.implementation_difficulty === 'Moderate'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                        }`}>
                          {recommendation.implementation_difficulty} Difficulty
                        </span>
                      </div>
                      <p className="mt-2">{recommendation.recommendation}</p>
                      <div className="mt-3 text-sm text-gray-600">
                        <p><span className="font-medium">Financial impact:</span> {recommendation.financial_impact}</p>
                        <p className="mt-1"><span className="font-medium">Confidence score:</span> {recommendation.confidence_score}%</p>
                        <p className="mt-1"><span className="font-medium">Generated:</span> {new Date(recommendation.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleImplementFinancialRecommendation(recommendation.id, !recommendation.is_implemented)}
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          recommendation.is_implemented
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        }`}
                      >
                        {recommendation.is_implemented ? 'Implemented ✓' : 'Mark as Implemented'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      </div>
    </Layout>
  );
};

export default AIAssistant;
