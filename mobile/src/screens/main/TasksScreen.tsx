import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';

type TasksScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Tasks'>,
  NativeStackScreenProps<MainStackParamList>
>;

type Task = {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: string;
  assignedTo?: string;
  fieldId?: string;
  fieldName?: string;
  equipmentId?: string;
  equipmentName?: string;
  createdAt: string;
};

type Filter = {
  status: string | null;
  priority: string | null;
  assignedTo: string | null;
  field: string | null;
};

const TasksScreen: React.FC<TasksScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<Filter>({
    status: null,
    priority: null,
    assignedTo: null,
    field: null,
  });
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium' as const,
    dueDate: new Date().toISOString().split('T')[0],
    assignedTo: '',
  });

  useEffect(() => {
    // This would normally be an API call
    // For now, we'll use mock data
    const mockTasks: Task[] = [
      {
        id: '1',
        title: 'Spray north field',
        description: 'Apply herbicide to control weeds in the north field',
        status: 'pending',
        priority: 'high',
        dueDate: '2023-09-15',
        assignedTo: 'John Doe',
        fieldId: '1',
        fieldName: 'North Field',
        createdAt: '2023-09-10',
      },
      {
        id: '2',
        title: 'Repair tractor',
        description: 'Fix hydraulic system on the John Deere tractor',
        status: 'in-progress',
        priority: 'urgent',
        dueDate: '2023-09-16',
        assignedTo: 'Jane Smith',
        equipmentId: '1',
        equipmentName: 'John Deere 8R',
        createdAt: '2023-09-09',
      },
      {
        id: '3',
        title: 'Order seeds',
        description: 'Order corn seeds for next season',
        status: 'pending',
        priority: 'medium',
        dueDate: '2023-09-18',
        assignedTo: 'John Doe',
        createdAt: '2023-09-08',
      },
      {
        id: '4',
        title: 'Soil sampling',
        description: 'Take soil samples from south field for analysis',
        status: 'completed',
        priority: 'medium',
        dueDate: '2023-09-05',
        assignedTo: 'Jane Smith',
        fieldId: '2',
        fieldName: 'South Field',
        createdAt: '2023-09-01',
      },
      {
        id: '5',
        title: 'Irrigation maintenance',
        description: 'Check and repair irrigation system in west field',
        status: 'pending',
        priority: 'low',
        dueDate: '2023-09-20',
        fieldId: '3',
        fieldName: 'West Field',
        createdAt: '2023-09-10',
      },
      {
        id: '6',
        title: 'Harvest planning',
        description: 'Create schedule for upcoming harvest operations',
        status: 'in-progress',
        priority: 'high',
        dueDate: '2023-09-14',
        assignedTo: 'John Doe',
        createdAt: '2023-09-07',
      },
      {
        id: '7',
        title: 'Equipment inspection',
        description: 'Perform routine inspection on all harvest equipment',
        status: 'pending',
        priority: 'medium',
        dueDate: '2023-09-17',
        assignedTo: 'Jane Smith',
        createdAt: '2023-09-10',
      },
    ];

    setTasks(mockTasks);
    setFilteredTasks(mockTasks);
    setLoading(false);
  }, []);

  useEffect(() => {
    applyFiltersAndSearch();
  }, [searchQuery, filters, tasks]);

  const applyFiltersAndSearch = () => {
    let result = [...tasks];

    // Apply search query
    if (searchQuery) {
      result = result.filter(
        task =>
          task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (task.fieldName && task.fieldName.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (task.equipmentName && task.equipmentName.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (task.assignedTo && task.assignedTo.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply filters
    if (filters.status) {
      result = result.filter(task => task.status === filters.status);
    }
    if (filters.priority) {
      result = result.filter(task => task.priority === filters.priority);
    }
    if (filters.assignedTo) {
      result = result.filter(task => task.assignedTo === filters.assignedTo);
    }
    if (filters.field) {
      result = result.filter(task => task.fieldId === filters.field);
    }

    setFilteredTasks(result);
  };

  const resetFilters = () => {
    setFilters({
      status: null,
      priority: null,
      assignedTo: null,
      field: null,
    });
  };

  const handleCreateTask = () => {
    if (!newTask.title.trim()) {
      Alert.alert('Error', 'Task title is required');
      return;
    }

    // This would normally be an API call
    // For now, we'll just add to the local state
    const newTaskItem: Task = {
      id: (tasks.length + 1).toString(),
      title: newTask.title,
      description: newTask.description,
      status: 'pending',
      priority: newTask.priority,
      dueDate: newTask.dueDate,
      assignedTo: newTask.assignedTo || undefined,
      createdAt: new Date().toISOString().split('T')[0],
    };

    setTasks([newTaskItem, ...tasks]);
    setIsCreateModalVisible(false);
    setNewTask({
      title: '',
      description: '',
      priority: 'medium',
      dueDate: new Date().toISOString().split('T')[0],
      assignedTo: '',
    });

    Alert.alert('Success', 'Task created successfully');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return '#9ca3af';
      case 'medium':
        return '#60a5fa';
      case 'high':
        return '#f59e0b';
      case 'urgent':
        return '#ef4444';
      default:
        return '#9ca3af';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'in-progress':
        return '#60a5fa';
      case 'completed':
        return '#22c55e';
      case 'cancelled':
        return '#9ca3af';
      default:
        return '#9ca3af';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'in-progress':
        return 'play-outline';
      case 'completed':
        return 'checkmark-circle-outline';
      case 'cancelled':
        return 'close-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const renderTaskItem = ({ item }: { item: Task }) => (
    <TouchableOpacity
      style={styles.taskItem}
      onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
    >
      <View style={styles.taskHeader}>
        <View style={styles.taskTitleContainer}>
          <Ionicons
            name={getStatusIcon(item.status)}
            size={20}
            color={getStatusColor(item.status)}
            style={styles.statusIcon}
          />
          <Text style={styles.taskTitle}>{item.title}</Text>
        </View>
        <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
          <Text style={styles.priorityText}>{item.priority.toUpperCase()}</Text>
        </View>
      </View>
      
      <View style={styles.taskDetails}>
        <View style={styles.taskDetailRow}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.taskDetailText}>Due: {item.dueDate}</Text>
        </View>
        
        {item.assignedTo && (
          <View style={styles.taskDetailRow}>
            <Ionicons name="person-outline" size={16} color="#666" />
            <Text style={styles.taskDetailText}>{item.assignedTo}</Text>
          </View>
        )}
        
        {item.fieldName && (
          <View style={styles.taskDetailRow}>
            <Ionicons name="map-outline" size={16} color="#666" />
            <Text style={styles.taskDetailText}>{item.fieldName}</Text>
          </View>
        )}
        
        {item.equipmentName && (
          <View style={styles.taskDetailRow}>
            <Ionicons name="construct-outline" size={16} color="#666" />
            <Text style={styles.taskDetailText}>{item.equipmentName}</Text>
          </View>
        )}
      </View>
      
      <View style={styles.taskFooter}>
        <Text style={styles.taskStatus}>
          Status: <Text style={{ color: getStatusColor(item.status) }}>{item.status.toUpperCase()}</Text>
        </Text>
        <Ionicons name="chevron-forward" size={20} color="#999" />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading tasks...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tasks..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={() => setShowFilters(!showFilters)}>
            <Ionicons
              name="options-outline"
              size={20}
              color={Object.values(filters).some(v => v !== null) ? '#22c55e' : '#999'}
            />
          </TouchableOpacity>
        )}
      </View>

      {showFilters && (
        <View style={styles.filtersContainer}>
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Status:</Text>
            <View style={styles.filterOptions}>
              {['pending', 'in-progress', 'completed', 'cancelled'].map(status => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.filterOption,
                    filters.status === status && { backgroundColor: getStatusColor(status) },
                  ]}
                  onPress={() => setFilters({ ...filters, status: filters.status === status ? null : status })}
                >
                  <Text
                    style={[
                      styles.filterOptionText,
                      filters.status === status && { color: '#fff' },
                    ]}
                  >
                    {status}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Priority:</Text>
            <View style={styles.filterOptions}>
              {['low', 'medium', 'high', 'urgent'].map(priority => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.filterOption,
                    filters.priority === priority && { backgroundColor: getPriorityColor(priority) },
                  ]}
                  onPress={() => setFilters({ ...filters, priority: filters.priority === priority ? null : priority })}
                >
                  <Text
                    style={[
                      styles.filterOptionText,
                      filters.priority === priority && { color: '#fff' },
                    ]}
                  >
                    {priority}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
            <Text style={styles.resetButtonText}>Reset Filters</Text>
          </TouchableOpacity>
        </View>
      )}

      {filteredTasks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="clipboard" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No tasks found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery || Object.values(filters).some(v => v !== null)
              ? "Try different search terms or filters"
              : "Add your first task to get started"}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredTasks}
          renderItem={renderTaskItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setIsCreateModalVisible(true)}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Create Task Modal */}
      <Modal
        visible={isCreateModalVisible}
        transparent
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create New Task</Text>
            
            <Text style={styles.inputLabel}>Title *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter task title"
              value={newTask.title}
              onChangeText={(text) => setNewTask({ ...newTask, title: text })}
            />
            
            <Text style={styles.inputLabel}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter task description"
              value={newTask.description}
              onChangeText={(text) => setNewTask({ ...newTask, description: text })}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            
            <Text style={styles.inputLabel}>Priority</Text>
            <View style={styles.prioritySelector}>
              {['low', 'medium', 'high', 'urgent'].map(priority => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.priorityOption,
                    newTask.priority === priority && { backgroundColor: getPriorityColor(priority) },
                  ]}
                  onPress={() => setNewTask({ ...newTask, priority: priority as any })}
                >
                  <Text
                    style={[
                      styles.priorityOptionText,
                      newTask.priority === priority && { color: '#fff' },
                    ]}
                  >
                    {priority}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <Text style={styles.inputLabel}>Due Date</Text>
            <TextInput
              style={styles.input}
              placeholder="YYYY-MM-DD"
              value={newTask.dueDate}
              onChangeText={(text) => setNewTask({ ...newTask, dueDate: text })}
            />
            
            <Text style={styles.inputLabel}>Assign To</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter assignee name"
              value={newTask.assignedTo}
              onChangeText={(text) => setNewTask({ ...newTask, assignedTo: text })}
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setIsCreateModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={handleCreateTask}
              >
                <Text style={styles.createButtonText}>Create Task</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  filtersContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  filterRow: {
    marginBottom: 10,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#666',
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    marginRight: 8,
    marginBottom: 8,
  },
  filterOptionText: {
    fontSize: 12,
    color: '#666',
  },
  resetButton: {
    alignSelf: 'flex-end',
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  resetButtonText: {
    fontSize: 14,
    color: '#22c55e',
    fontWeight: '500',
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for the add button
  },
  taskItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  taskTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusIcon: {
    marginRight: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  taskDetails: {
    marginBottom: 10,
  },
  taskDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  taskDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 10,
  },
  taskStatus: {
    fontSize: 12,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 15,
  },
  textArea: {
    height: 100,
  },
  prioritySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  priorityOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
    marginHorizontal: 4,
    alignItems: 'center',
  },
  priorityOptionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: '#f3f4f6',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  createButton: {
    backgroundColor: '#22c55e',
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
});

export default TasksScreen;