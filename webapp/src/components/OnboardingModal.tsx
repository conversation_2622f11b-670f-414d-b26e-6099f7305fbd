import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { FarmContext } from '../context/FarmContext';
import { MenuPreferencesContext } from '../context/MenuPreferencesContext';
import { saveUserMenuPreferences } from '../services/menuPreferencesService';
import axios from 'axios';
import { API_URL } from '../config';

interface FeatureOption {
  id: string;
  title: string;
  description: string;
  category: string;
  selected: boolean;
}

const OnboardingModal: React.FC = () => {
  const { user, updateUser } = useContext(AuthContext);
  const { currentFarm } = useContext(FarmContext);
  const { headerItems, sidebarCategories, quickLinksItems, refreshPreferences } = useContext(MenuPreferencesContext);

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [featureOptions, setFeatureOptions] = useState<FeatureOption[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // Check if this is a new user who hasn't completed onboarding
  useEffect(() => {
    if (user && !user.onboardingCompleted) {
      // Initialize feature options
      initializeFeatureOptions();
      setIsOpen(true);
    }
  }, [user]);

  // Initialize feature options from sidebar categories
  const initializeFeatureOptions = () => {
    const options: FeatureOption[] = [];

    // Add options for each sidebar category
    sidebarCategories.forEach(category => {
      // Skip required categories
      if (category.items.some(item => item.isRequired)) {
        return;
      }

      options.push({
        id: category.id,
        title: category.title,
        description: `Features related to ${category.title.toLowerCase()}`,
        category: category.id,
        selected: true // Default to selected
      });
    });

    setFeatureOptions(options);
  };

  // Toggle a feature option
  const toggleFeatureOption = (id: string) => {
    setFeatureOptions(prevOptions =>
      prevOptions.map(option =>
        option.id === id ? { ...option, selected: !option.selected } : option
      )
    );
  };

  // Save preferences and close modal
  const savePreferences = async () => {
    if (!user || !currentFarm) return;

    setLoading(true);

    try {
      // Create a map of selected features
      const selectedFeatures = featureOptions.reduce((map, option) => {
        map[option.id] = option.selected;
        return map;
      }, {} as Record<string, boolean>);

      // Update visibility of menu items based on selected features
      const updatedHeaderItems = headerItems.map(item => ({
        ...item,
        isVisible: item.isRequired || (
          // Keep visible if the item's category is selected or if it's already visible
          (selectedFeatures[item.category] !== false && item.isVisible)
        )
      }));

      // Update visibility of sidebar categories and their items
      const updatedSidebarCategories = sidebarCategories.map(category => {
        // If this category is not selected, hide all its items
        const isSelected = selectedFeatures[category.id] !== false;

        return {
          ...category,
          items: category.items.map(item => ({
            ...item,
            isVisible: item.isRequired || (isSelected && item.isVisible)
          }))
        };
      });

      // Update visibility of quick links items
      const updatedQuickLinksItems = quickLinksItems.map(item => ({
        ...item,
        isVisible: item.isRequired || (
          // Keep visible if the item's original menu item's category is selected
          (selectedFeatures[item.category] !== false && item.isVisible)
        )
      }));

      // Save the updated preferences
      await saveUserMenuPreferences({
        userId: user.id,
        headerItems: updatedHeaderItems,
        sidebarCategories: updatedSidebarCategories,
        quickLinksItems: updatedQuickLinksItems
      });

      // Refresh preferences in the context
      await refreshPreferences();

      // Mark onboarding as completed
      await markOnboardingCompleted();

      // Close the modal
      setIsOpen(false);
    } catch (error) {
      console.error('Error saving feature preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mark onboarding as completed
  const markOnboardingCompleted = async () => {
    try {
      // Call API to update user's onboarding_completed flag
      await axios.post(`${API_URL}/users/mark-onboarding-completed`);
      
      // Update user in context
      if (user) {
        updateUser({ ...user, onboardingCompleted: true });
      }
    } catch (error) {
      console.error('Error marking onboarding as completed:', error);
    }
  };

  // Skip and close modal
  const skipAndClose = async () => {
    // Mark onboarding as completed even if user skips
    await markOnboardingCompleted();
    setIsOpen(false);
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to NxtAcre!</h2>
          <p className="text-gray-600 mb-6">
            Let's customize your experience. Select the features you're interested in using.
            We'll hide the rest to keep your interface clean and focused.
          </p>
          <p className="text-gray-600 mb-6">
            Don't worry, you can always add more features later through the Menu Customization page.
          </p>

          <div className="space-y-4 mb-6">
            {featureOptions.map(option => (
              <div 
                key={option.id} 
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                onClick={() => toggleFeatureOption(option.id)}
              >
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={option.selected}
                    onChange={() => toggleFeatureOption(option.id)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-3 block text-sm font-medium text-gray-700">
                    {option.title}
                  </label>
                </div>
                <p className="mt-1 text-sm text-gray-500 ml-7">
                  {option.description}
                </p>
              </div>
            ))}
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={skipAndClose}
              className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Skip for Now
            </button>
            <button
              type="button"
              onClick={savePreferences}
              disabled={loading}
              className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-70"
            >
              {loading ? 'Saving...' : 'Save Preferences'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingModal;