import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import dotenv from 'dotenv';
import { uploadToSpaces, fileExistsInSpaces } from '../server/utils/spacesUtils.js';

// Load environment variables
dotenv.config();

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create test data directory
const testDataDir = path.join(__dirname, '..', 'test', 'data');
if (!fs.existsSync(testDataDir)) {
  fs.mkdirSync(testDataDir, { recursive: true });
}

// Create sample PDF file if needed
const createSamplePDF = async () => {
  const pdfFilename = '05-versions-space.pdf';
  const pdfPath = path.join(testDataDir, pdfFilename);
  const spacesPath = `test/data/${pdfFilename}`;

  try {
    // First check if the file exists in Spaces
    const existsInSpaces = await fileExistsInSpaces(spacesPath);

    if (existsInSpaces) {
      console.log('Sample PDF file already exists in Spaces.');
      return;
    }

    // If not in Spaces, check local filesystem
    if (fs.existsSync(pdfPath)) {
      console.log('Sample PDF file exists locally, uploading to Spaces...');
      const fileBuffer = fs.readFileSync(pdfPath);
      await uploadToSpaces(fileBuffer, spacesPath);
      console.log('Sample PDF uploaded to Spaces successfully.');
      return;
    }

    // If file doesn't exist anywhere, create it
    console.log('Creating sample PDF file...');
    // Create a simple PDF file directly
    const pdfContent = '%PDF-1.4\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/MediaBox[0 0 612 792]/Parent 2 0 R/Resources<<>>>>\nendobj\nxref\n0 4\n0000000000 65535 f\n0000000010 00000 n\n0000000053 00000 n\n0000000102 00000 n\ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n178\n%%EOF';

    const fileBuffer = Buffer.from(pdfContent);

    // Try to upload to Spaces first
    try {
      await uploadToSpaces(fileBuffer, spacesPath);
      console.log('Sample PDF created and uploaded to Spaces successfully.');
    } catch (spacesError) {
      console.error('Error uploading to Spaces, falling back to local storage:', spacesError.message);
      // Fallback to local storage
      fs.writeFileSync(pdfPath, fileBuffer);
      console.log('Sample PDF created locally successfully.');
    }
  } catch (error) {
    console.error('Error creating sample PDF:', error.message);
    // Fallback to local storage in case of any error
    try {
      if (!fs.existsSync(pdfPath)) {
        const pdfContent = '%PDF-1.4\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/MediaBox[0 0 612 792]/Parent 2 0 R/Resources<<>>>>\nendobj\nxref\n0 4\n0000000000 65535 f\n0000000010 00000 n\n0000000053 00000 n\n0000000102 00000 n\ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n178\n%%EOF';
        fs.writeFileSync(pdfPath, Buffer.from(pdfContent));
        console.log('Sample PDF created locally as fallback.');
      }
    } catch (fallbackError) {
      console.error('Error creating local fallback PDF:', fallbackError.message);
    }
  }
};

// Run setup
createSamplePDF().catch(console.error);

console.log('Test data setup complete.');
