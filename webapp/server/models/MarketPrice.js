import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const MarketPrice = defineModel('MarketPrice', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Optional farm association, null for global market prices'
  },
  commodity: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Name of the commodity'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Current price of the commodity'
  },
  unit: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'Unit of measurement (e.g., bushel, cwt, ton)'
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: 'Date of the price data'
  },
  location: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Geographic market location'
  },
  trend: {
    type: DataTypes.ENUM('up', 'down', 'stable'),
    allowNull: false,
    comment: 'Price trend compared to previous period'
  },
  percent_change: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: 'Percentage price change from previous period'
  },
  source: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Source of the market data'
  },
  api_source: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'API source (e.g., AMS, DATA_GOV)'
  },
  is_cached: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Indicates if this is cached data'
  },
  cache_expiry: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the cache expires'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'market_prices',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'market_prices_commodity_date_idx',
      fields: ['commodity', 'date']
    },
    {
      name: 'market_prices_farm_id_idx',
      fields: ['farm_id']
    }
  ]
});

export default MarketPrice;