import React, { useState, useEffect } from 'react';
import { useFarm } from '../../context/FarmContext';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Heading } from '../../components/ui/Heading';
import { Text } from '../../components/ui/Text';
import { Spinner } from '../../components/ui/Spinner';
import { Alert, AlertDescription, AlertTitle } from '../../components/ui/Alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/Table';
import { Badge } from '../../components/ui/Badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/Select';
import { Input } from '../../components/ui/Input';
import { ChartLineUp, MagnifyingGlass } from 'phosphor-react';

const PriceComparison: React.FC = () => {
  const { currentFarm } = useFarm();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  // Simulated data - in a real implementation, this would be fetched from the API
  const [priceComparisons, setPriceComparisons] = useState<any[]>([]);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        // In a real implementation, this would be an actual API call
        // For now, we'll just simulate some data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Set simulated data
        const data = [
          {
            id: '1',
            product_name: 'Nitrogen Fertilizer',
            product_category: 'Fertilizers',
            unit: 'ton',
            comparison_date: '2023-05-15',
            price_data: [
              { supplier_id: 's1', supplier_name: 'AgriChem Solutions', price: 450, currency: 'USD' },
              { supplier_id: 's2', supplier_name: 'Farm Supply Co.', price: 475, currency: 'USD' },
              { supplier_id: 's3', supplier_name: 'Rural Chemicals', price: 425, currency: 'USD' }
            ],
            best_price: 425,
            best_price_supplier_id: 's3',
            price_range: 50,
            average_price: 450
          },
          {
            id: '2',
            product_name: 'Corn Seed (Hybrid)',
            product_category: 'Seeds',
            unit: 'bag',
            comparison_date: '2023-05-10',
            price_data: [
              { supplier_id: 's4', supplier_name: 'Pioneer Seeds', price: 320, currency: 'USD' },
              { supplier_id: 's5', supplier_name: 'DeKalb', price: 310, currency: 'USD' },
              { supplier_id: 's6', supplier_name: 'Syngenta', price: 335, currency: 'USD' }
            ],
            best_price: 310,
            best_price_supplier_id: 's5',
            price_range: 25,
            average_price: 321.67
          },
          {
            id: '3',
            product_name: 'Tractor Fuel',
            product_category: 'Fuel',
            unit: 'gallon',
            comparison_date: '2023-05-20',
            price_data: [
              { supplier_id: 's7', supplier_name: 'Rural Fuels', price: 3.75, currency: 'USD' },
              { supplier_id: 's8', supplier_name: 'Farm Energy', price: 3.65, currency: 'USD' },
              { supplier_id: 's9', supplier_name: 'AgriGas', price: 3.80, currency: 'USD' }
            ],
            best_price: 3.65,
            best_price_supplier_id: 's8',
            price_range: 0.15,
            average_price: 3.73
          },
          {
            id: '4',
            product_name: 'Herbicide (Glyphosate)',
            product_category: 'Chemicals',
            unit: 'gallon',
            comparison_date: '2023-05-18',
            price_data: [
              { supplier_id: 's1', supplier_name: 'AgriChem Solutions', price: 28.50, currency: 'USD' },
              { supplier_id: 's3', supplier_name: 'Rural Chemicals', price: 27.75, currency: 'USD' },
              { supplier_id: 's10', supplier_name: 'Farm Chemicals Inc.', price: 29.25, currency: 'USD' }
            ],
            best_price: 27.75,
            best_price_supplier_id: 's3',
            price_range: 1.50,
            average_price: 28.50
          },
          {
            id: '5',
            product_name: 'Irrigation Equipment',
            product_category: 'Equipment',
            unit: 'set',
            comparison_date: '2023-05-05',
            price_data: [
              { supplier_id: 's11', supplier_name: 'Irrigation Systems', price: 1250, currency: 'USD' },
              { supplier_id: 's12', supplier_name: 'Farm Equipment Co.', price: 1350, currency: 'USD' },
              { supplier_id: 's13', supplier_name: 'AgriTech Solutions', price: 1175, currency: 'USD' }
            ],
            best_price: 1175,
            best_price_supplier_id: 's13',
            price_range: 175,
            average_price: 1258.33
          }
        ];

        setPriceComparisons(data);

        // Extract unique categories
        const uniqueCategories = Array.from(new Set(data.map(item => item.product_category)));
        setCategories(uniqueCategories);
      } catch (err) {
        console.error('Error fetching price comparisons:', err);
        setError('Failed to load price comparisons. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm]);

  // Filter price comparisons based on search term and selected category
  const filteredComparisons = priceComparisons.filter(comparison => {
    const matchesSearch = searchTerm === '' || 
      comparison.product_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || 
      comparison.product_category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const calculateSavings = (comparison: any) => {
    const highestPrice = Math.max(...comparison.price_data.map((item: any) => item.price));
    return ((highestPrice - comparison.best_price) * 100 / highestPrice).toFixed(1);
  };

  if (!currentFarm) {
    return (
      <Alert variant="warning">
        <AlertTitle>No Farm Selected</AlertTitle>
        <AlertDescription>
          Please select a farm to view price comparisons.
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <ChartLineUp size={24} className="text-primary-500" />
          <Heading level={1}>Price Comparison</Heading>
        </div>
        <Button>New Comparison</Button>
      </div>

      <Card>
        <CardHeader>
          <Heading level={2}>Price Comparison Tools</Heading>
          <Text>Compare prices from different suppliers to find the best deals for your farm inputs.</Text>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlass size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-64">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {filteredComparisons.length === 0 ? (
            <div className="text-center py-8">
              <Text className="text-gray-500">No price comparisons found. Create your first comparison to get started.</Text>
              <div className="mt-4">
                <Button>Create Comparison</Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Best Price</TableHead>
                  <TableHead>Best Supplier</TableHead>
                  <TableHead>Avg. Price</TableHead>
                  <TableHead>Savings</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredComparisons.map((comparison) => (
                  <TableRow key={comparison.id}>
                    <TableCell className="font-medium">{comparison.product_name}</TableCell>
                    <TableCell>{comparison.product_category}</TableCell>
                    <TableCell>{comparison.unit}</TableCell>
                    <TableCell className="font-semibold text-green-600">
                      ${comparison.best_price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </TableCell>
                    <TableCell>
                      {comparison.price_data.find((item: any) => item.supplier_id === comparison.best_price_supplier_id)?.supplier_name}
                    </TableCell>
                    <TableCell>
                      ${comparison.average_price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </TableCell>
                    <TableCell>
                      <Badge variant="success">{calculateSavings(comparison)}%</Badge>
                    </TableCell>
                    <TableCell>{new Date(comparison.comparison_date).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">View</Button>
                      <Button variant="ghost" size="sm">Update</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PriceComparison;