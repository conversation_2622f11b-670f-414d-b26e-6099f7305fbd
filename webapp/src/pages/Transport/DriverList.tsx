import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getDrivers, deleteDriver, formatDate, Driver } from '../../services/driverService';

// Using Driver interface from driverService.ts

const DriverList = () => {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch drivers for the selected farm
  useEffect(() => {
    const fetchDrivers = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        const driversData = await getDrivers(currentFarm.id);
        setDrivers(driversData);
      } catch (err: any) {
        console.error('Error fetching drivers:', err);
        setError('Failed to load drivers. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDrivers();
  }, [currentFarm]);

  // Handle driver deletion
  const handleDeleteDriver = async (driverId: string) => {
    if (!window.confirm('Are you sure you want to delete this driver?')) {
      return;
    }

    try {
      await deleteDriver(driverId);
      setDrivers(drivers.filter(driver => driver.id !== driverId));
    } catch (err: any) {
      console.error('Error deleting driver:', err);
      setError('Failed to delete driver. Please try again later.');
    }
  };

  // Using formatDate from driverService.ts

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Driver Management</h1>
        <div>
          <Link
            to="/transport/drivers/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Driver
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading drivers...</p>
        </div>
      ) : drivers.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No drivers found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first driver to start managing your transportation team.
          </p>
          <Link
            to="/transport/drivers/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Driver
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {drivers.map((driver) => (
              <li key={driver.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {driver.firstName} {driver.lastName}
                        </p>
                        <p className="text-sm text-gray-500">
                          {driver.email && <span className="mr-2">{driver.email}</span>}
                          {driver.phoneNumber && <span className="mr-2">Phone: {driver.phoneNumber}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/transport/drivers/${driver.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/transport/drivers/${driver.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteDriver(driver.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      {driver.vehicleType && (
                        <p className="flex items-center text-sm text-gray-500 mr-6">
                          <span>Vehicle: {driver.vehicleType}</span>
                        </p>
                      )}
                      {driver.vehiclePlate && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>Plate: {driver.vehiclePlate}</span>
                        </p>
                      )}
                      {driver.licenseNumber && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>License: {driver.licenseNumber}</span>
                        </p>
                      )}
                      {driver.licenseExpiry && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <span>Expires: {formatDate(driver.licenseExpiry)}</span>
                        </p>
                      )}
                    </div>
                    {driver.status && (
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          driver.status === 'active' ? 'bg-green-100 text-green-800' : 
                          driver.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                          driver.status === 'on_leave' ? 'bg-yellow-100 text-yellow-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {driver.status === 'active' ? 'Active' : 
                           driver.status === 'inactive' ? 'Inactive' : 
                           driver.status === 'on_leave' ? 'On Leave' : 
                           driver.status}
                        </span>
                      </div>
                    )}
                  </div>
                  {driver.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{driver.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default DriverList;
