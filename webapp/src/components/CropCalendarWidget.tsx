import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useFarm } from '../context/FarmContext';
import { API_URL } from '../config';

// Define interfaces for the data
interface Crop {
  id: string;
  name: string;
  crop_type: string;
  planting_date: string;
  expected_harvest_date: string;
  status: string;
  field_id: string;
  field_name: string;
}

interface HarvestSchedule {
  id: string;
  crop_id: string;
  planned_date: string;
  status: string;
}

const CropCalendarWidget: React.FC = () => {
  // Get the selected farm from context
  const { selectedFarm } = useFarm();

  // State for crops and schedules
  const [crops, setCrops] = useState<Crop[]>([]);
  const [harvestSchedules, setHarvestSchedules] = useState<HarvestSchedule[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  // Fetch crops and harvest schedules when selected farm changes
  useEffect(() => {
    const fetchCropData = async () => {
      if (!selectedFarm) {
        setCrops([]);
        setHarvestSchedules([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch crops for the farm
        const cropsResponse = await axios.get(`${API_URL}/crops/farm/${selectedFarm.id}`);
        setCrops(cropsResponse.data);

        // Fetch harvest schedules for each crop
        const schedules: HarvestSchedule[] = [];
        for (const crop of cropsResponse.data) {
          try {
            const scheduleResponse = await axios.get(`${API_URL}/harvest-schedules/crop/${crop.id}`);
            schedules.push(...scheduleResponse.data);
          } catch (scheduleError) {
            console.error(`Error fetching harvest schedules for crop ${crop.id}:`, scheduleError);
          }
        }
        setHarvestSchedules(schedules);
      } catch (error) {
        console.error('Error fetching crop data:', error);
        setError('Failed to load crop calendar data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCropData();
  }, [selectedFarm]);

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'growing':
        return 'bg-green-100 text-green-800';
      case 'planned':
        return 'bg-blue-100 text-blue-800';
      case 'harvested':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  // Get month name
  const getMonthName = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Check if a crop has activity in the current month
  const hasCropActivityInMonth = (crop: Crop) => {
    const plantingDate = crop.planting_date ? new Date(crop.planting_date) : null;
    const harvestDate = crop.expected_harvest_date ? new Date(crop.expected_harvest_date) : null;
    
    if (plantingDate && 
        plantingDate.getMonth() === currentMonth.getMonth() && 
        plantingDate.getFullYear() === currentMonth.getFullYear()) {
      return true;
    }
    
    if (harvestDate && 
        harvestDate.getMonth() === currentMonth.getMonth() && 
        harvestDate.getFullYear() === currentMonth.getFullYear()) {
      return true;
    }
    
    // Check if any harvest schedules for this crop are in the current month
    return harvestSchedules.some(schedule => {
      if (schedule.crop_id === crop.id && schedule.planned_date) {
        const scheduleDate = new Date(schedule.planned_date);
        return scheduleDate.getMonth() === currentMonth.getMonth() && 
               scheduleDate.getFullYear() === currentMonth.getFullYear();
      }
      return false;
    });
  };

  // Filter crops to show only those with activity in the current month
  const cropsInCurrentMonth = crops.filter(hasCropActivityInMonth);

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      {/* Header with farm name */}
      <div className="mb-4">
        <h2 className="text-lg font-bold text-gray-800">Crop Calendar</h2>
        <p className="text-sm text-gray-600">
          {selectedFarm 
            ? `${selectedFarm.name}`
            : 'Please select a farm to view crop calendar'}
        </p>
      </div>

      {/* Month navigation */}
      <div className="flex justify-between items-center mb-4">
        <button 
          onClick={goToPreviousMonth}
          className="p-2 rounded-full hover:bg-gray-100"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </button>
        <h3 className="text-md font-medium">{getMonthName(currentMonth)}</h3>
        <button 
          onClick={goToNextMonth}
          className="p-2 rounded-full hover:bg-gray-100"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Crop calendar content */}
      <div className="crop-calendar-container">
        {isLoading ? (
          <div className="flex justify-center items-center py-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-500 ml-2">Loading crop calendar...</p>
          </div>
        ) : error ? (
          <div className="text-red-500 text-center py-4">{error}</div>
        ) : cropsInCurrentMonth.length === 0 ? (
          <div className="text-gray-500 text-center py-4">
            No crop activities scheduled for {getMonthName(currentMonth)}
          </div>
        ) : (
          <div className="overflow-hidden rounded-md border border-gray-200">
            <ul className="divide-y divide-gray-200">
              {cropsInCurrentMonth.map(crop => (
                <li key={crop.id} className="px-3 py-3 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">{crop.name}</span>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(crop.status)}`}>
                        {crop.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      Field: {crop.field_name || 'N/A'}
                    </div>
                  </div>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    {crop.planting_date && new Date(crop.planting_date).getMonth() === currentMonth.getMonth() && (
                      <div className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
                        Planting: {formatDate(crop.planting_date)}
                      </div>
                    )}
                    {crop.expected_harvest_date && new Date(crop.expected_harvest_date).getMonth() === currentMonth.getMonth() && (
                      <div className="text-xs bg-green-50 text-green-700 px-2 py-1 rounded">
                        Harvest: {formatDate(crop.expected_harvest_date)}
                      </div>
                    )}
                    {harvestSchedules
                      .filter(schedule => 
                        schedule.crop_id === crop.id && 
                        schedule.planned_date && 
                        new Date(schedule.planned_date).getMonth() === currentMonth.getMonth()
                      )
                      .map(schedule => (
                        <div key={schedule.id} className="text-xs bg-purple-50 text-purple-700 px-2 py-1 rounded">
                          {schedule.status}: {formatDate(schedule.planned_date)}
                        </div>
                      ))
                    }
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default CropCalendarWidget;