import axios from 'axios';
import Alert from '../models/Alert.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import { sequelize } from '../config/database.js';

// National Weather Service API base URL
const NWS_API_BASE = 'https://api.weather.gov';

// Get weather alerts for a farm
export const getFarmAlerts = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }

    // Fetch weather alerts from NWS API
    const alerts = await fetchWeatherAlerts(farmLocation.latitude, farmLocation.longitude);

    // Store alerts in database
    await storeWeatherAlerts(alerts, farmId, null, farmLocation.latitude, farmLocation.longitude);

    return res.status(200).json(alerts);
  } catch (error) {
    console.error('Error getting farm weather alerts:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get weather alerts for a field
export const getFieldAlerts = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }

    // Fetch weather alerts from NWS API
    const alerts = await fetchWeatherAlerts(fieldLocation.center.latitude, fieldLocation.center.longitude);

    // Store alerts in database
    await storeWeatherAlerts(alerts, field.farm_id, fieldId, fieldLocation.center.latitude, fieldLocation.center.longitude);

    return res.status(200).json(alerts);
  } catch (error) {
    console.error('Error getting field weather alerts:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to fetch weather alerts from NWS API
export const fetchWeatherAlerts = async (latitude, longitude) => {
  try {
    // Step 1: Get the grid point for the location
    const pointsUrl = `${NWS_API_BASE}/points/${latitude},${longitude}`;
    const pointsResponse = await axios.get(pointsUrl, {
      headers: {
        'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
        'Accept': 'application/geo+json'
      }
    });

    // Step 2: Get the county code from the response
    const county = pointsResponse.data.properties.county;
    if (!county) {
      throw new Error('County information not available for this location');
    }

    // Extract the county code from the URL
    const countyCode = county.split('/').pop();

    // Step 3: Get the alerts for the county
    const alertsUrl = `${NWS_API_BASE}/alerts/active/zone/${countyCode}`;
    const alertsResponse = await axios.get(alertsUrl, {
      headers: {
        'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
        'Accept': 'application/geo+json'
      }
    });

    // Process alerts data
    const alertsData = alertsResponse.data.features || [];

    // Format alerts for our application
    const formattedAlerts = alertsData.map(alert => {
      const properties = alert.properties;
      return {
        id: properties.id,
        event: properties.event,
        headline: properties.headline,
        description: properties.description,
        instruction: properties.instruction,
        severity: properties.severity,
        certainty: properties.certainty,
        urgency: properties.urgency,
        onset: properties.onset,
        expires: properties.expires,
        status: properties.status,
        messageType: properties.messageType,
        category: properties.category,
        response: properties.response,
        source: properties.sender
      };
    });

    return {
      alerts: formattedAlerts,
      count: formattedAlerts.length,
      location: `${latitude},${longitude}`
    };
  } catch (error) {
    console.error('Error fetching weather alerts:', error);
    throw new Error(`Failed to fetch weather alerts: ${error.message}`);
  }
};

// Helper function to store weather alerts in database
const storeWeatherAlerts = async (alertsData, farmId, fieldId, latitude, longitude) => {
  const transaction = await sequelize.transaction();

  try {
    // Store each alert
    for (const alert of alertsData.alerts) {
      // Check if alert already exists
      const existingAlert = await Alert.findOne({
        where: {
          external_id: alert.id,
          type: 'weather'
        }
      });

      if (!existingAlert) {
        // Create new alert
        await Alert.create({
          farm_id: farmId,
          field_id: fieldId,
          type: 'weather',
          external_id: alert.id,
          title: alert.event,
          message: alert.headline,
          details: alert.description,
          instructions: alert.instruction,
          severity: mapAlertSeverity(alert.severity),
          status: 'active',
          start_time: alert.onset ? new Date(alert.onset) : new Date(),
          end_time: alert.expires ? new Date(alert.expires) : null,
          location: `${latitude},${longitude}`,
          metadata: JSON.stringify(alert)
        }, { transaction });
      }
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error('Error storing weather alerts:', error);
    throw new Error(`Failed to store weather alerts: ${error.message}`);
  }
};

// Helper function to map NWS alert severity to our severity levels
const mapAlertSeverity = (nwsSeverity) => {
  switch (nwsSeverity.toLowerCase()) {
    case 'extreme':
      return 'critical';
    case 'severe':
      return 'high';
    case 'moderate':
      return 'medium';
    case 'minor':
      return 'low';
    case 'unknown':
    default:
      return 'info';
  }
};
