import { useState, useEffect, useRef } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../config';

interface Signer {
  id: string;
  signer_email: string;
  signer_name: string;
  signer_role: string | null;
  status: string;
}

interface DocumentField {
  id: string;
  field_type: string;
  field_name: string;
  field_value: string | null;
  is_required: boolean;
  page_number: number;
  x_position: number;
  y_position: number;
  width: number;
  height: number;
}

interface SignableDocument {
  id: string;
  title: string;
  description: string;
  document_type: string;
  status: string;
  file_path: string;
  file_type: string;
  mime_type: string;
  version: number;
  created_at: string;
}

const SignableDocumentSigning = () => {
  const { documentId, signerId } = useParams<{ documentId: string; signerId: string }>();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const navigate = useNavigate();

  const [document, setDocument] = useState<SignableDocument | null>(null);
  const [signer, setSigner] = useState<Signer | null>(null);
  const [fields, setFields] = useState<DocumentField[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [signatureData, setSignatureData] = useState<string | null>(null);
  const [signatureType, setSignatureType] = useState<'drawn' | 'typed'>('drawn');
  const [typedSignature, setTypedSignature] = useState('');
  const [fieldValues, setFieldValues] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [declineReason, setDeclineReason] = useState('');
  const [showDeclineModal, setShowDeclineModal] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  // Fetch document and signer details
  useEffect(() => {
    const fetchData = async () => {
      if (!documentId || !signerId || !token) {
        setError('Invalid URL. Please check the link and try again.');
        setLoading(false);
        return;
      }

      try {
        const response = await axios.get(
          `${API_URL}/document-signing/sign/${documentId}/${signerId}?token=${token}`
        );

        setDocument(response.data.document);
        setSigner(response.data.signer);
        setFields(response.data.document.fields || []);

        // Initialize field values
        const initialValues: Record<string, string> = {};
        if (response.data.document.fields) {
          response.data.document.fields.forEach((field: DocumentField) => {
            initialValues[field.id] = field.field_value || '';
          });
        }
        setFieldValues(initialValues);
      } catch (err: any) {
        console.error('Error fetching document:', err);
        setError(err.response?.data?.error || 'Failed to load document. Please check the link and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [documentId, signerId, token]);

  // Initialize canvas for signature drawing
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Set canvas style
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.strokeStyle = '#000';

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  }, []);

  // Handle mouse/touch events for signature drawing
  const startDrawing = (e: React.MouseEvent | React.TouchEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    setIsDrawing(true);

    // Get mouse/touch position
    let x, y;
    if ('touches' in e) {
      const rect = canvas.getBoundingClientRect();
      x = e.touches[0].clientX - rect.left;
      y = e.touches[0].clientY - rect.top;
    } else {
      x = e.nativeEvent.offsetX;
      y = e.nativeEvent.offsetY;
    }

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Get mouse/touch position
    let x, y;
    if ('touches' in e) {
      const rect = canvas.getBoundingClientRect();
      x = e.touches[0].clientX - rect.left;
      y = e.touches[0].clientY - rect.top;
    } else {
      x = e.nativeEvent.offsetX;
      y = e.nativeEvent.offsetY;
    }

    ctx.lineTo(x, y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.closePath();
    setIsDrawing(false);

    // Save signature data
    setSignatureData(canvas.toDataURL('image/png'));
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setSignatureData(null);
  };

  // Handle field value changes
  const handleFieldChange = (fieldId: string, value: string) => {
    setFieldValues({
      ...fieldValues,
      [fieldId]: value
    });
  };

  // Handle document signing
  const handleSign = async () => {
    if (!documentId || !signerId || !token) {
      setError('Invalid URL. Please check the link and try again.');
      return;
    }

    if (signatureType === 'drawn' && !signatureData) {
      setError('Please draw your signature before signing.');
      return;
    }

    if (signatureType === 'typed' && !typedSignature.trim()) {
      setError('Please type your signature before signing.');
      return;
    }

    // Check required fields
    const requiredFields = fields.filter(field => field.is_required);
    for (const field of requiredFields) {
      if (!fieldValues[field.id]) {
        setError(`Please fill in the required field: ${field.field_name}`);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await axios.post(
        `${API_URL}/document-signing/sign/${documentId}/${signerId}?token=${token}`,
        {
          signatureType,
          signatureData: signatureType === 'drawn' ? signatureData : typedSignature,
          fields: Object.keys(fieldValues).map(fieldId => ({
            id: fieldId,
            value: fieldValues[fieldId]
          }))
        }
      );

      setSuccess('Document signed successfully!');

      // Redirect to a thank you page after a short delay
      setTimeout(() => {
        navigate(`/documents/signing/thank-you?status=signed`);
      }, 2000);
    } catch (err: any) {
      console.error('Error signing document:', err);
      setError(err.response?.data?.error || 'Failed to sign document. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle document declining
  const handleDecline = async () => {
    if (!documentId || !signerId || !token) {
      setError('Invalid URL. Please check the link and try again.');
      return;
    }

    if (!declineReason.trim()) {
      setError('Please provide a reason for declining.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await axios.post(
        `${API_URL}/document-signing/sign/${documentId}/${signerId}/decline?token=${token}`,
        {
          reason: declineReason
        }
      );

      setSuccess('You have declined to sign this document.');
      setShowDeclineModal(false);

      // Redirect to a thank you page after a short delay
      setTimeout(() => {
        navigate(`/documents/signing/thank-you?status=declined`);
      }, 2000);
    } catch (err: any) {
      console.error('Error declining document:', err);
      setError(err.response?.data?.error || 'Failed to decline document. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle document download
  const handleDownload = async () => {
    if (!documentId || !signerId || !token) {
      setError('Invalid URL. Please check the link and try again.');
      return;
    }

    try {
      window.open(`${API_URL}/document-signing/sign/${documentId}/${signerId}/download?token=${token}`, '_blank');
    } catch (err: any) {
      console.error('Error downloading document:', err);
      setError(err.response?.data?.error || 'Failed to download document. Please try again later.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
      <div className="relative py-3 sm:max-w-4xl sm:mx-auto">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-600 shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl"></div>
        <div className="relative px-4 py-10 bg-white shadow-lg sm:rounded-3xl sm:p-20">
          <div className="max-w-3xl mx-auto">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Document Signing</h1>
                {document && (
                  <p className="text-gray-600 mt-1">{document.title}</p>
                )}
              </div>
              {signer && (
                <div className="text-right">
                  <p className="text-sm text-gray-600">Signing as:</p>
                  <p className="text-lg font-medium text-gray-900">{signer.signer_name}</p>
                  <p className="text-sm text-gray-600">{signer.signer_email}</p>
                </div>
              )}
            </div>

            {/* Error message */}
            {error && (
              <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
                <span>{error}</span>
              </div>
            )}

            {/* Success message */}
            {success && (
              <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
                <span>{success}</span>
              </div>
            )}

            {/* Loading state */}
            {loading ? (
              <div className="flex flex-col justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                <p className="mt-4 text-gray-600">Loading document...</p>
              </div>
            ) : document && signer ? (
              <div className="space-y-8">
                {/* Document preview button */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Document Preview</h3>
                      <p className="text-sm text-gray-500">
                        Review the document before signing
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={handleDownload}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <svg className="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Download
                    </button>
                  </div>
                </div>

                {/* Form fields */}
                {fields.length > 0 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Form Fields</h3>
                    {fields.map((field) => (
                      <div key={field.id} className="space-y-1">
                        <label htmlFor={field.id} className="block text-sm font-medium text-gray-700">
                          {field.field_name} {field.is_required && <span className="text-red-500">*</span>}
                        </label>
                        {field.field_type === 'text' && (
                          <input
                            type="text"
                            id={field.id}
                            value={fieldValues[field.id] || ''}
                            onChange={(e) => handleFieldChange(field.id, e.target.value)}
                            required={field.is_required}
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                          />
                        )}
                        {field.field_type === 'date' && (
                          <input
                            type="date"
                            id={field.id}
                            value={fieldValues[field.id] || ''}
                            onChange={(e) => handleFieldChange(field.id, e.target.value)}
                            required={field.is_required}
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                          />
                        )}
                        {field.field_type === 'checkbox' && (
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={field.id}
                              checked={fieldValues[field.id] === 'true'}
                              onChange={(e) => handleFieldChange(field.id, e.target.checked ? 'true' : 'false')}
                              required={field.is_required}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />
                            <label htmlFor={field.id} className="ml-2 block text-sm text-gray-900">
                              I agree
                            </label>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Signature section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Your Signature</h3>
                  
                  {/* Signature type selector */}
                  <div className="flex space-x-4 mb-4">
                    <button
                      type="button"
                      onClick={() => setSignatureType('drawn')}
                      className={`px-4 py-2 text-sm font-medium rounded-md ${
                        signatureType === 'drawn'
                          ? 'bg-primary-100 text-primary-700 border border-primary-300'
                          : 'text-gray-700 border border-gray-300 bg-white hover:bg-gray-50'
                      }`}
                    >
                      Draw Signature
                    </button>
                    <button
                      type="button"
                      onClick={() => setSignatureType('typed')}
                      className={`px-4 py-2 text-sm font-medium rounded-md ${
                        signatureType === 'typed'
                          ? 'bg-primary-100 text-primary-700 border border-primary-300'
                          : 'text-gray-700 border border-gray-300 bg-white hover:bg-gray-50'
                      }`}
                    >
                      Type Signature
                    </button>
                  </div>

                  {/* Drawn signature */}
                  {signatureType === 'drawn' && (
                    <div className="space-y-2">
                      <div
                        className="border-2 border-gray-300 rounded-md p-2 bg-white"
                        style={{ touchAction: 'none' }}
                      >
                        <canvas
                          ref={canvasRef}
                          className="w-full h-32 border border-gray-200 rounded cursor-crosshair"
                          onMouseDown={startDrawing}
                          onMouseMove={draw}
                          onMouseUp={stopDrawing}
                          onMouseLeave={stopDrawing}
                          onTouchStart={startDrawing}
                          onTouchMove={draw}
                          onTouchEnd={stopDrawing}
                        />
                      </div>
                      <button
                        type="button"
                        onClick={clearSignature}
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        Clear signature
                      </button>
                    </div>
                  )}

                  {/* Typed signature */}
                  {signatureType === 'typed' && (
                    <div className="space-y-2">
                      <input
                        type="text"
                        value={typedSignature}
                        onChange={(e) => setTypedSignature(e.target.value)}
                        placeholder="Type your full name"
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      />
                      <div className="p-4 border border-gray-200 rounded-md bg-white">
                        <p className="font-signature text-xl text-center">
                          {typedSignature || 'Your signature will appear here'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action buttons */}
                <div className="flex justify-between pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowDeclineModal(true)}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    disabled={isSubmitting}
                  >
                    Decline to Sign
                  </button>
                  <button
                    type="button"
                    onClick={handleSign}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Signing...' : 'Sign Document'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-white p-6 rounded-lg shadow">
                <p className="text-gray-500">Document not found or you don't have permission to view it.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Decline modal */}
      {showDeclineModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Decline to Sign</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Please provide a reason for declining to sign this document.
                      </p>
                      <textarea
                        value={declineReason}
                        onChange={(e) => setDeclineReason(e.target.value)}
                        rows={4}
                        className="mt-2 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        placeholder="Enter your reason here..."
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleDecline}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Submitting...' : 'Decline'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowDeclineModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SignableDocumentSigning;