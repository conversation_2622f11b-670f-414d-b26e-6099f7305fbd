# Add Payment Transaction ID to Invoices

## Overview

This migration adds the `payment_transaction_id` column to the `invoices` table. This column is referenced in the Invoice model but was missing from the database schema, causing errors when accessing invoice data through the API.

## Issue Fixed

The migration fixes the following error:
```
https://api.nxtacre.com/payments/invoices/4d9141d5-df79-4e3c-9423-ca342bd6cbcc 
column Invoice.payment_transaction_id does not exist
```

This error occurred because the `payment_transaction_id` column was defined in the Invoice model but did not exist in the actual database table.

## Migration Details

The migration file `add_payment_transaction_id_to_invoices.sql` performs the following actions:

1. Checks if the `payment_transaction_id` column already exists in the `invoices` table
2. If it doesn't exist, adds the following columns:
   - `payment_transaction_id` (UUID, nullable)
   - `payment_method` (VARCHAR(50), nullable)
   - `payment_date` (TIMESTAMP, nullable)
   - `payment_amount` (DECIMAL(15, 2), nullable)
   - `stripe_payment_intent_id` (VARCHAR(255), nullable)
   - `stripe_payment_method_id` (VARCHAR(255), nullable)
3. If the `transactions` table exists, adds a foreign key constraint from `payment_transaction_id` to `transactions.id`
4. Records the migration in the `database_migrations` table if it exists

## How to Apply the Migration

To apply this migration, run the following command from the PostgreSQL command line:

```sql
\i '/path/to/webapp/server/db/migrations/add_payment_transaction_id_to_invoices.sql'
```

Or you can run it using the database migration script:

```bash
node server/scripts/run-migration.js add_payment_transaction_id_to_invoices.sql
```

## Dependencies

This migration depends on:
- `add_customer_portal_features.sql` (which originally attempted to add these columns)

## Verification

After running the migration, you can verify that the columns were added correctly by running:

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'site' 
AND table_name = 'invoices' 
AND column_name IN ('payment_transaction_id', 'payment_method', 'payment_date', 'payment_amount', 'stripe_payment_intent_id', 'stripe_payment_method_id');
```

This should return the newly added columns with their data types.