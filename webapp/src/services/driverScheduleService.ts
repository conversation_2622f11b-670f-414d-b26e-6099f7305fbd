import axios from 'axios';
import { API_URL } from '../config';
import { handleApiError } from '../utils/errorHandler';

// Define DriverSchedule interface
export interface DriverSchedule {
  id: string;
  driverId: string;
  startTime: string;
  endTime: string;
  status: string;
  notes: string;
  assignedDeliveries: string[];
  assignedPickups: string[];
  farmId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  driver?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

// Get all driver schedules
export const getDriverSchedules = async (
  params: {
    farmId?: string;
    driverId?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.farmId) queryParams.append('farmId', params.farmId);
    if (params.driverId) queryParams.append('driverId', params.driverId);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.status) queryParams.append('status', params.status);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/driver-schedules${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.schedules || [];
  } catch (error: unknown) {
    console.error('Error fetching driver schedules:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver schedules');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get schedules for a specific driver
export const getDriverSchedulesByDriverId = async (
  driverId: string,
  params: {
    startDate?: string;
    endDate?: string;
    status?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.status) queryParams.append('status', params.status);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/driver-schedules/driver/${driverId}${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.schedules || [];
  } catch (error: unknown) {
    console.error('Error fetching driver schedules:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver schedules');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get a specific schedule by ID
export const getDriverScheduleById = async (scheduleId: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.get(`${API_URL}/driver-schedules/${scheduleId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching driver schedule:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver schedule details');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Create a new driver schedule
export const createDriverSchedule = async (
  data: {
    driverId: string;
    startTime: string;
    endTime: string;
    status: string;
    notes?: string;
    assignedDeliveries?: string[];
    assignedPickups?: string[];
    farmId: string;
  }
) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/driver-schedules`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error creating driver schedule:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to create driver schedule');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update a driver schedule
export const updateDriverSchedule = async (
  scheduleId: string,
  data: {
    driverId?: string;
    startTime?: string;
    endTime?: string;
    status?: string;
    notes?: string;
    assignedDeliveries?: string[];
    assignedPickups?: string[];
  }
) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.put(`${API_URL}/driver-schedules/${scheduleId}`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating driver schedule:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update driver schedule');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete a driver schedule
export const deleteDriverSchedule = async (scheduleId: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.delete(`${API_URL}/driver-schedules/${scheduleId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting driver schedule:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete driver schedule');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};