-- Migration: Add document signing system tables

-- Set the search path to the site schema
SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'Migration step: %', step_name;
END;
$$ LANGUAGE plpgsql;

-- Begin transaction
BEGIN;

-- Step 1: Create signable_documents table
SELECT log_migration_step('Creating signable_documents table');

CREATE TABLE IF NOT EXISTS site.signable_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  document_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  file_path VARCHAR(1024),
  file_size BIGINT,
  file_type VARCHAR(100),
  mime_type VARCHAR(100),
  version INT NOT NULL DEFAULT 1,
  is_template BOOLEAN NOT NULL DEFAULT FALSE,
  template_id UUID REFERENCES site.signable_documents(id),
  tenant_id UUID NOT NULL REFERENCES site.tenants(id),
  farm_id UUID REFERENCES site.farms(id),
  created_by UUID REFERENCES site.users(id),
  completed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for document_type
ALTER TABLE site.signable_documents DROP CONSTRAINT IF EXISTS signable_documents_document_type_check;
ALTER TABLE site.signable_documents ADD CONSTRAINT signable_documents_document_type_check 
  CHECK (document_type IN ('agreement', 'contract', 'lease', 'invoice', 'receipt', 'other'));

-- Add enum constraint for status
ALTER TABLE site.signable_documents DROP CONSTRAINT IF EXISTS signable_documents_status_check;
ALTER TABLE site.signable_documents ADD CONSTRAINT signable_documents_status_check 
  CHECK (status IN ('draft', 'sent', 'viewed', 'signed', 'completed', 'declined', 'expired', 'canceled'));

-- Add comments to columns
COMMENT ON COLUMN site.signable_documents.id IS 'Unique identifier for the document';
COMMENT ON COLUMN site.signable_documents.title IS 'Title of the document';
COMMENT ON COLUMN site.signable_documents.description IS 'Description of the document';
COMMENT ON COLUMN site.signable_documents.document_type IS 'Type of document: agreement, contract, lease, etc.';
COMMENT ON COLUMN site.signable_documents.status IS 'Status of the document: draft, sent, viewed, signed, completed, declined, expired, canceled';
COMMENT ON COLUMN site.signable_documents.file_path IS 'Path to the stored document file';
COMMENT ON COLUMN site.signable_documents.file_size IS 'Size of the document file in bytes';
COMMENT ON COLUMN site.signable_documents.file_type IS 'Type of the document file';
COMMENT ON COLUMN site.signable_documents.mime_type IS 'MIME type of the document file';
COMMENT ON COLUMN site.signable_documents.version IS 'Version number of the document';
COMMENT ON COLUMN site.signable_documents.is_template IS 'Whether this document is a template';
COMMENT ON COLUMN site.signable_documents.template_id IS 'ID of the template this document was created from (if any)';
COMMENT ON COLUMN site.signable_documents.tenant_id IS 'ID of the tenant this document belongs to';
COMMENT ON COLUMN site.signable_documents.farm_id IS 'ID of the farm this document is associated with (if any)';
COMMENT ON COLUMN site.signable_documents.created_by IS 'ID of the user who created the document';
COMMENT ON COLUMN site.signable_documents.completed_at IS 'Timestamp when the document was completed (all signatures collected)';
COMMENT ON COLUMN site.signable_documents.expires_at IS 'Timestamp when the document expires';
COMMENT ON COLUMN site.signable_documents.created_at IS 'Timestamp when the document was created';
COMMENT ON COLUMN site.signable_documents.updated_at IS 'Timestamp when the document was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS signable_documents_title_idx ON site.signable_documents(title);
CREATE INDEX IF NOT EXISTS signable_documents_document_type_idx ON site.signable_documents(document_type);
CREATE INDEX IF NOT EXISTS signable_documents_status_idx ON site.signable_documents(status);
CREATE INDEX IF NOT EXISTS signable_documents_tenant_id_idx ON site.signable_documents(tenant_id);
CREATE INDEX IF NOT EXISTS signable_documents_farm_id_idx ON site.signable_documents(farm_id);
CREATE INDEX IF NOT EXISTS signable_documents_created_by_idx ON site.signable_documents(created_by);
CREATE INDEX IF NOT EXISTS signable_documents_template_id_idx ON site.signable_documents(template_id);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_signable_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_signable_documents_timestamp
BEFORE UPDATE ON site.signable_documents
FOR EACH ROW EXECUTE FUNCTION update_signable_documents_updated_at();

-- Step 2: Create document_signers table
SELECT log_migration_step('Creating document_signers table');

CREATE TABLE IF NOT EXISTS site.document_signers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES site.signable_documents(id) ON DELETE CASCADE,
  signer_email VARCHAR(255) NOT NULL,
  signer_name VARCHAR(255) NOT NULL,
  signer_role VARCHAR(100),
  signer_order INT NOT NULL DEFAULT 1,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  access_code VARCHAR(100),
  verification_method VARCHAR(20) DEFAULT 'email',
  verification_phone VARCHAR(20),
  sent_at TIMESTAMP WITH TIME ZONE,
  viewed_at TIMESTAMP WITH TIME ZONE,
  signed_at TIMESTAMP WITH TIME ZONE,
  declined_at TIMESTAMP WITH TIME ZONE,
  decline_reason TEXT,
  reminder_sent_at TIMESTAMP WITH TIME ZONE,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.document_signers DROP CONSTRAINT IF EXISTS document_signers_status_check;
ALTER TABLE site.document_signers ADD CONSTRAINT document_signers_status_check 
  CHECK (status IN ('pending', 'sent', 'viewed', 'signed', 'declined'));

-- Add enum constraint for verification_method
ALTER TABLE site.document_signers DROP CONSTRAINT IF EXISTS document_signers_verification_method_check;
ALTER TABLE site.document_signers ADD CONSTRAINT document_signers_verification_method_check 
  CHECK (verification_method IN ('email', 'sms', 'phone', 'none'));

-- Add comments to columns
COMMENT ON COLUMN site.document_signers.id IS 'Unique identifier for the document signer';
COMMENT ON COLUMN site.document_signers.document_id IS 'ID of the document to be signed';
COMMENT ON COLUMN site.document_signers.signer_email IS 'Email address of the signer';
COMMENT ON COLUMN site.document_signers.signer_name IS 'Name of the signer';
COMMENT ON COLUMN site.document_signers.signer_role IS 'Role of the signer (e.g., owner, tenant, witness)';
COMMENT ON COLUMN site.document_signers.signer_order IS 'Order in which signers should sign (for sequential signing)';
COMMENT ON COLUMN site.document_signers.status IS 'Status of the signing process: pending, sent, viewed, signed, declined';
COMMENT ON COLUMN site.document_signers.access_code IS 'Optional access code required to view the document';
COMMENT ON COLUMN site.document_signers.verification_method IS 'Method used to verify signer identity: email, sms, phone, none';
COMMENT ON COLUMN site.document_signers.verification_phone IS 'Phone number for SMS or phone verification';
COMMENT ON COLUMN site.document_signers.sent_at IS 'Timestamp when the signing request was sent';
COMMENT ON COLUMN site.document_signers.viewed_at IS 'Timestamp when the document was first viewed';
COMMENT ON COLUMN site.document_signers.signed_at IS 'Timestamp when the document was signed';
COMMENT ON COLUMN site.document_signers.declined_at IS 'Timestamp when the signing was declined';
COMMENT ON COLUMN site.document_signers.decline_reason IS 'Reason provided for declining to sign';
COMMENT ON COLUMN site.document_signers.reminder_sent_at IS 'Timestamp when the last reminder was sent';
COMMENT ON COLUMN site.document_signers.ip_address IS 'IP address of the signer when signing';
COMMENT ON COLUMN site.document_signers.user_agent IS 'User agent of the signer when signing';
COMMENT ON COLUMN site.document_signers.created_at IS 'Timestamp when the signer record was created';
COMMENT ON COLUMN site.document_signers.updated_at IS 'Timestamp when the signer record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS document_signers_document_id_idx ON site.document_signers(document_id);
CREATE INDEX IF NOT EXISTS document_signers_signer_email_idx ON site.document_signers(signer_email);
CREATE INDEX IF NOT EXISTS document_signers_status_idx ON site.document_signers(status);
CREATE INDEX IF NOT EXISTS document_signers_signer_order_idx ON site.document_signers(signer_order);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_document_signers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_document_signers_timestamp
BEFORE UPDATE ON site.document_signers
FOR EACH ROW EXECUTE FUNCTION update_document_signers_updated_at();

-- Step 3: Create document_signatures table
SELECT log_migration_step('Creating document_signatures table');

CREATE TABLE IF NOT EXISTS site.document_signatures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES site.signable_documents(id) ON DELETE CASCADE,
  signer_id UUID NOT NULL REFERENCES site.document_signers(id) ON DELETE CASCADE,
  signature_type VARCHAR(20) NOT NULL DEFAULT 'drawn',
  signature_image_path VARCHAR(1024),
  signature_text VARCHAR(255),
  signature_font VARCHAR(100),
  page_number INT,
  x_position FLOAT,
  y_position FLOAT,
  width FLOAT,
  height FLOAT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for signature_type
ALTER TABLE site.document_signatures DROP CONSTRAINT IF EXISTS document_signatures_signature_type_check;
ALTER TABLE site.document_signatures ADD CONSTRAINT document_signatures_signature_type_check 
  CHECK (signature_type IN ('drawn', 'typed', 'uploaded', 'click'));

-- Add comments to columns
COMMENT ON COLUMN site.document_signatures.id IS 'Unique identifier for the signature';
COMMENT ON COLUMN site.document_signatures.document_id IS 'ID of the document being signed';
COMMENT ON COLUMN site.document_signatures.signer_id IS 'ID of the signer record';
COMMENT ON COLUMN site.document_signatures.signature_type IS 'Type of signature: drawn, typed, uploaded, click';
COMMENT ON COLUMN site.document_signatures.signature_image_path IS 'Path to the stored signature image (for drawn or uploaded signatures)';
COMMENT ON COLUMN site.document_signatures.signature_text IS 'Text of the signature (for typed signatures)';
COMMENT ON COLUMN site.document_signatures.signature_font IS 'Font used for typed signatures';
COMMENT ON COLUMN site.document_signatures.page_number IS 'Page number where the signature appears';
COMMENT ON COLUMN site.document_signatures.x_position IS 'X coordinate of the signature on the page';
COMMENT ON COLUMN site.document_signatures.y_position IS 'Y coordinate of the signature on the page';
COMMENT ON COLUMN site.document_signatures.width IS 'Width of the signature area';
COMMENT ON COLUMN site.document_signatures.height IS 'Height of the signature area';
COMMENT ON COLUMN site.document_signatures.ip_address IS 'IP address of the signer when signing';
COMMENT ON COLUMN site.document_signatures.user_agent IS 'User agent of the signer when signing';
COMMENT ON COLUMN site.document_signatures.created_at IS 'Timestamp when the signature was created';
COMMENT ON COLUMN site.document_signatures.updated_at IS 'Timestamp when the signature was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS document_signatures_document_id_idx ON site.document_signatures(document_id);
CREATE INDEX IF NOT EXISTS document_signatures_signer_id_idx ON site.document_signatures(signer_id);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_document_signatures_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_document_signatures_timestamp
BEFORE UPDATE ON site.document_signatures
FOR EACH ROW EXECUTE FUNCTION update_document_signatures_updated_at();

-- Step 4: Create document_fields table
SELECT log_migration_step('Creating document_fields table');

CREATE TABLE IF NOT EXISTS site.document_fields (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES site.signable_documents(id) ON DELETE CASCADE,
  signer_id UUID REFERENCES site.document_signers(id) ON DELETE CASCADE,
  field_type VARCHAR(20) NOT NULL,
  field_name VARCHAR(100) NOT NULL,
  field_value TEXT,
  is_required BOOLEAN NOT NULL DEFAULT FALSE,
  page_number INT,
  x_position FLOAT,
  y_position FLOAT,
  width FLOAT,
  height FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for field_type
ALTER TABLE site.document_fields DROP CONSTRAINT IF EXISTS document_fields_field_type_check;
ALTER TABLE site.document_fields ADD CONSTRAINT document_fields_field_type_check 
  CHECK (field_type IN ('signature', 'initial', 'date', 'text', 'checkbox', 'radio', 'dropdown', 'attachment'));

-- Add comments to columns
COMMENT ON COLUMN site.document_fields.id IS 'Unique identifier for the document field';
COMMENT ON COLUMN site.document_fields.document_id IS 'ID of the document containing the field';
COMMENT ON COLUMN site.document_fields.signer_id IS 'ID of the signer assigned to this field (if any)';
COMMENT ON COLUMN site.document_fields.field_type IS 'Type of field: signature, initial, date, text, checkbox, radio, dropdown, attachment';
COMMENT ON COLUMN site.document_fields.field_name IS 'Name of the field';
COMMENT ON COLUMN site.document_fields.field_value IS 'Value of the field (if completed)';
COMMENT ON COLUMN site.document_fields.is_required IS 'Whether the field is required';
COMMENT ON COLUMN site.document_fields.page_number IS 'Page number where the field appears';
COMMENT ON COLUMN site.document_fields.x_position IS 'X coordinate of the field on the page';
COMMENT ON COLUMN site.document_fields.y_position IS 'Y coordinate of the field on the page';
COMMENT ON COLUMN site.document_fields.width IS 'Width of the field area';
COMMENT ON COLUMN site.document_fields.height IS 'Height of the field area';
COMMENT ON COLUMN site.document_fields.created_at IS 'Timestamp when the field was created';
COMMENT ON COLUMN site.document_fields.updated_at IS 'Timestamp when the field was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS document_fields_document_id_idx ON site.document_fields(document_id);
CREATE INDEX IF NOT EXISTS document_fields_signer_id_idx ON site.document_fields(signer_id);
CREATE INDEX IF NOT EXISTS document_fields_field_type_idx ON site.document_fields(field_type);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_document_fields_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_document_fields_timestamp
BEFORE UPDATE ON site.document_fields
FOR EACH ROW EXECUTE FUNCTION update_document_fields_updated_at();

-- Step 5: Create document_audit_logs table
SELECT log_migration_step('Creating document_audit_logs table');

CREATE TABLE IF NOT EXISTS site.document_audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES site.signable_documents(id) ON DELETE CASCADE,
  signer_id UUID REFERENCES site.document_signers(id) ON DELETE SET NULL,
  user_id UUID REFERENCES site.users(id) ON DELETE SET NULL,
  event_type VARCHAR(50) NOT NULL,
  event_details JSONB,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.document_audit_logs.id IS 'Unique identifier for the audit log entry';
COMMENT ON COLUMN site.document_audit_logs.document_id IS 'ID of the document related to this event';
COMMENT ON COLUMN site.document_audit_logs.signer_id IS 'ID of the signer related to this event (if any)';
COMMENT ON COLUMN site.document_audit_logs.user_id IS 'ID of the user who performed the action (if any)';
COMMENT ON COLUMN site.document_audit_logs.event_type IS 'Type of event (e.g., created, sent, viewed, signed, completed)';
COMMENT ON COLUMN site.document_audit_logs.event_details IS 'Additional details about the event in JSON format';
COMMENT ON COLUMN site.document_audit_logs.ip_address IS 'IP address of the user or signer';
COMMENT ON COLUMN site.document_audit_logs.user_agent IS 'User agent of the user or signer';
COMMENT ON COLUMN site.document_audit_logs.created_at IS 'Timestamp when the event occurred';

-- Create indexes
CREATE INDEX IF NOT EXISTS document_audit_logs_document_id_idx ON site.document_audit_logs(document_id);
CREATE INDEX IF NOT EXISTS document_audit_logs_signer_id_idx ON site.document_audit_logs(signer_id);
CREATE INDEX IF NOT EXISTS document_audit_logs_user_id_idx ON site.document_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS document_audit_logs_event_type_idx ON site.document_audit_logs(event_type);
CREATE INDEX IF NOT EXISTS document_audit_logs_created_at_idx ON site.document_audit_logs(created_at);


-- Commit transaction
COMMIT;

-- Verify all tables were created
DO $$
DECLARE
    missing_tables TEXT := '';
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'signable_documents') THEN
        missing_tables := missing_tables || 'signable_documents, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'document_signers') THEN
        missing_tables := missing_tables || 'document_signers, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'document_signatures') THEN
        missing_tables := missing_tables || 'document_signatures, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'document_fields') THEN
        missing_tables := missing_tables || 'document_fields, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'document_audit_logs') THEN
        missing_tables := missing_tables || 'document_audit_logs, ';
    END IF;

    IF missing_tables <> '' THEN
        missing_tables := SUBSTRING(missing_tables, 1, LENGTH(missing_tables) - 2);
        RAISE EXCEPTION 'Migration failed. The following tables were not created: %', missing_tables;
    ELSE
        RAISE NOTICE 'Migration successful. All tables were created.';
    END IF;
END $$;
