import cron from 'node-cron';
import syncUsersToMatrix from './syncUsersToMatrix.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Schedule the user synchronization script to run periodically
 * @param {string} schedule - Cron schedule expression (default: every day at 1:00 AM)
 */
function scheduleSyncUsersToMatrix(schedule = '0 1 * * *') {
  console.log(`Scheduling user synchronization with Matrix Synapse: ${schedule}`);
  
  // Validate the cron expression
  if (!cron.validate(schedule)) {
    console.error(`Invalid cron schedule expression: ${schedule}`);
    return false;
  }
  
  // Schedule the task
  const task = cron.schedule(schedule, async () => {
    console.log(`Running scheduled user synchronization with Matrix Synapse at ${new Date().toISOString()}`);
    
    try {
      await syncUsersToMatrix();
      console.log(`Scheduled user synchronization completed at ${new Date().toISOString()}`);
    } catch (error) {
      console.error('Error in scheduled user synchronization:', error);
    }
  });
  
  // Start the task
  task.start();
  
  console.log('User synchronization scheduled successfully');
  return task;
}

// Run the script if called directly
if (process.argv[1].endsWith('scheduleSyncUsersToMatrix.js')) {
  // Get schedule from environment variable or use default
  const schedule = process.env.MATRIX_SYNC_SCHEDULE || '0 1 * * *';
  scheduleSyncUsersToMatrix(schedule);
  
  // Keep the process running
  console.log('Process will keep running to maintain the scheduled task');
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('Received SIGINT. Shutting down scheduler.');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM. Shutting down scheduler.');
    process.exit(0);
  });
}

export default scheduleSyncUsersToMatrix;