import express from 'express';
import { protect } from '../middleware/authMiddleware.js';
import {
  getFarmHistoricalWeather,
  getFieldHistoricalWeather
} from '../controllers/historicalWeatherController.js';

const router = express.Router();

// Protect all routes
router.use(protect);

// GET /api/historical-weather/farm/:farmId - Get historical weather data for a farm
router.get('/farm/:farmId', getFarmHistoricalWeather);

// GET /api/historical-weather/field/:fieldId - Get historical weather data for a field
router.get('/field/:fieldId', getFieldHistoricalWeather);

export default router;