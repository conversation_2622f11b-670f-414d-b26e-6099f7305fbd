/**
 * React Error Decoder Utility
 * Helps decode minified React error messages in production
 */

// Common React error codes and their meanings
const REACT_ERROR_CODES: Record<string, string> = {
  '31': 'Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s',
  '130': 'Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s',
  '185': 'Calling ReactDOM.render() to hydrate server-rendered markup will stop working in React v18. Replace the ReactDOM.render() call with ReactDOM.hydrate() if you want React to attach to the server HTML.',
  '200': 'Target container is not a DOM element.',
  '299': 'ReactDOM.render is no longer supported in React 18. Use createRoot instead. Until you switch to the new API, your app will behave as if it\'s running React 17.',
  '300': 'You are calling ReactDOM.createRoot() on a container that has already been passed to createRoot() before. Instead, call root.render() on the existing root instead if you want to update it.',
  '418': 'Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.',
  '419': 'Objects are not valid as a React child (found: %s). If you meant to render a collection of children, use an array instead.',
  '423': 'Text strings must be rendered within a <Text> component.',
  '425': 'Expected `onClick` listener to be a function, instead got a value of `%s` type.',
};

/**
 * Decodes a minified React error message
 * @param error The error object or message
 * @returns Decoded error information
 */
export function decodeReactError(error: Error | string): {
  originalMessage: string;
  decodedMessage?: string;
  errorCode?: string;
  isReactError: boolean;
  suggestions: string[];
} {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const originalMessage = errorMessage;
  
  // Check if this is a minified React error
  const minifiedErrorRegex = /Minified React error #(\d+);/;
  const match = errorMessage.match(minifiedErrorRegex);
  
  if (!match) {
    // Check for other React error patterns
    const reactErrorPatterns = [
      /Element type is invalid/,
      /Objects are not valid as a React child/,
      /Functions are not valid as a React child/,
      /ReactDOM\.render/,
      /createRoot/,
      /Target container is not a DOM element/,
    ];
    
    const isReactError = reactErrorPatterns.some(pattern => pattern.test(errorMessage));
    
    return {
      originalMessage,
      isReactError,
      suggestions: isReactError ? getGeneralReactErrorSuggestions(errorMessage) : []
    };
  }
  
  const errorCode = match[1];
  const decodedMessage = REACT_ERROR_CODES[errorCode];
  
  return {
    originalMessage,
    decodedMessage,
    errorCode,
    isReactError: true,
    suggestions: getSpecificErrorSuggestions(errorCode, errorMessage)
  };
}

/**
 * Get suggestions for specific React error codes
 */
function getSpecificErrorSuggestions(errorCode: string, errorMessage: string): string[] {
  const suggestions: string[] = [];
  
  switch (errorCode) {
    case '31':
    case '130':
      suggestions.push(
        'Check that you\'re importing components correctly',
        'Verify that the component is exported properly',
        'Make sure you\'re not passing undefined or null as a component',
        'Check for circular imports'
      );
      break;
      
    case '200':
      suggestions.push(
        'Ensure the DOM element exists before rendering',
        'Check that the container ID matches an element in your HTML',
        'Verify the element is not null or undefined'
      );
      break;
      
    case '299':
    case '300':
      suggestions.push(
        'Update to use createRoot() instead of ReactDOM.render()',
        'Follow the React 18 migration guide',
        'Ensure you\'re only calling createRoot() once per container'
      );
      break;
      
    case '418':
      suggestions.push(
        'Return JSX elements, not function references',
        'Use <Component /> instead of Component',
        'Check that you\'re not returning a function from render'
      );
      break;
      
    case '419':
      suggestions.push(
        'Wrap objects in JSX elements',
        'Use arrays for collections of children',
        'Convert objects to strings or JSX before rendering'
      );
      break;
      
    default:
      suggestions.push(
        'Check the React documentation for this error code',
        'Look for recent changes in your component code',
        'Verify all imports and exports are correct'
      );
  }
  
  return suggestions;
}

/**
 * Get general suggestions for React errors
 */
function getGeneralReactErrorSuggestions(errorMessage: string): string[] {
  const suggestions: string[] = [];
  
  if (errorMessage.includes('Element type is invalid')) {
    suggestions.push(
      'Check component imports and exports',
      'Verify component names are correct',
      'Look for undefined components'
    );
  }
  
  if (errorMessage.includes('not valid as a React child')) {
    suggestions.push(
      'Convert objects to strings or JSX',
      'Use arrays for multiple children',
      'Check what you\'re returning from components'
    );
  }
  
  if (errorMessage.includes('ReactDOM')) {
    suggestions.push(
      'Update to React 18 createRoot API',
      'Check ReactDOM usage patterns',
      'Verify DOM container elements'
    );
  }
  
  // Add general suggestions if none were added
  if (suggestions.length === 0) {
    suggestions.push(
      'Check recent code changes',
      'Verify all imports are correct',
      'Look for console warnings',
      'Check component prop types'
    );
  }
  
  return suggestions;
}

/**
 * Enhanced error logging for React errors
 */
export function logReactError(error: Error, errorInfo?: any): void {
  const decoded = decodeReactError(error);
  
  console.group('🚨 React Error Detected');
  console.error('Original Error:', error);
  
  if (decoded.isReactError) {
    console.log('✅ This is a React-specific error');
    
    if (decoded.errorCode) {
      console.log('📋 Error Code:', decoded.errorCode);
    }
    
    if (decoded.decodedMessage) {
      console.log('📝 Decoded Message:', decoded.decodedMessage);
    }
    
    if (decoded.suggestions.length > 0) {
      console.log('💡 Suggestions:');
      decoded.suggestions.forEach((suggestion, index) => {
        console.log(`   ${index + 1}. ${suggestion}`);
      });
    }
  }
  
  if (errorInfo) {
    console.log('📍 Component Stack:', errorInfo.componentStack);
  }
  
  console.log('🔗 For more help, visit: https://reactjs.org/docs/error-decoder.html');
  console.groupEnd();
}
