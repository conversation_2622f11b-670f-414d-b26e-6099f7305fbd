import { useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';

const SustainabilityDashboard = () => {
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Dashboard cards for different sustainability features
  const dashboardCards = [
    {
      title: 'Carbon Footprint',
      description: 'Calculate and track your farm\'s carbon footprint over time.',
      icon: '🌱',
      link: '/sustainability/carbon-footprint',
      color: 'bg-green-100',
      textColor: 'text-green-800',
      borderColor: 'border-green-200'
    },
    {
      title: 'Sustainable Practices',
      description: 'Track and report on sustainable farming practices.',
      icon: '♻️',
      link: '/sustainability/practices',
      color: 'bg-blue-100',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Certification Management',
      description: 'Manage organic and other sustainability certifications.',
      icon: '📜',
      link: '/sustainability/certifications',
      color: 'bg-purple-100',
      textColor: 'text-purple-800',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Environmental Impact',
      description: 'Generate reports on environmental impact and sustainability metrics.',
      icon: '📊',
      link: '/sustainability/impact',
      color: 'bg-yellow-100',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200'
    }
  ];

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Sustainability Tracking</h1>
      </div>

      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Sustainability Overview</h2>
        <p className="text-gray-600 mb-4">
          Track and manage your farm's sustainability efforts, including carbon footprint calculation, 
          sustainable practices tracking, certification management, and environmental impact reporting.
          Monitor your progress towards sustainability goals and identify areas for improvement.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {dashboardCards.map((card, index) => (
          <Link 
            key={index} 
            to={card.link} 
            className={`block p-6 rounded-lg shadow-sm border ${card.borderColor} ${card.color} hover:shadow-md transition-shadow duration-200`}
          >
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">{card.icon}</span>
              <h3 className={`text-lg font-medium ${card.textColor}`}>{card.title}</h3>
            </div>
            <p className="text-gray-600">{card.description}</p>
          </Link>
        ))}
      </div>

      <div className="mt-8 bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link 
            to="/sustainability/carbon-footprint/calculate" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Calculate Carbon Footprint
          </Link>
          <Link 
            to="/sustainability/practices/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Add Sustainable Practice
          </Link>
          <Link 
            to="/sustainability/certifications/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Add Certification
          </Link>
          <Link 
            to="/sustainability/impact/generate" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
          >
            Generate Impact Report
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default SustainabilityDashboard;