# React Error Debugging Guide

## Overview

This guide addresses the minified React errors occurring on the document explorer page and provides comprehensive debugging tools and solutions.

## Problem Analysis

The application is using React 19.0.0, which is a very recent version that may have compatibility issues with some React libraries. Minified React errors are typically production build errors that are difficult to debug without proper tooling.

## Solution Implementation

### 1. React Error Boundary

**File:** `webapp/src/components/ui/ErrorBoundary.tsx`

A comprehensive React Error Boundary component that:
- Catches React errors during rendering
- Provides detailed error information in development
- Logs errors to Sentry in production
- Offers retry functionality
- Shows user-friendly error messages

**Usage:**
```tsx
<ErrorBoundary onError={handleReactError}>
  <YourComponent />
</ErrorBoundary>
```

### 2. React Error Decoder

**File:** `webapp/src/utils/reactErrorDecoder.ts`

Utility functions to decode minified React errors:
- Maps common React error codes to readable messages
- Provides specific suggestions for each error type
- Enhanced error logging with context
- React 19 specific error pattern detection

**Key Functions:**
- `decodeReactError(error)` - Decodes minified React errors
- `logReactError(error, errorInfo)` - Enhanced error logging

### 3. React 19 Compatibility Checker

**File:** `webapp/src/utils/react19Compatibility.ts`

Comprehensive compatibility checking for React 19:
- Checks library versions for known compatibility issues
- Identifies React 19 specific error patterns
- Provides solutions for common problems
- Automatic compatibility reporting

**Key Functions:**
- `checkReact19Compatibility()` - Checks for compatibility issues
- `logReact19CompatibilityIssues()` - Logs compatibility problems
- `initReact19CompatibilityCheck()` - Initializes checking

### 4. Development Debugging Tools

**File:** `webapp/src/utils/debugReactErrors.ts`

Development-only debugging utilities:
- Enhanced console error handling
- Unhandled error and promise rejection catching
- Component render logging
- Memory leak detection
- Performance monitoring

**Key Functions:**
- `setupReactErrorDebugging()` - Sets up error catching
- `logComponentRender()` - Logs component renders
- `checkForMemoryLeaks()` - Monitors for memory leaks

## Implementation Details

### Document Explorer Updates

The following components have been wrapped with ErrorBoundary:

1. **DocumentList** (`webapp/src/pages/Documents/DocumentList.tsx`)
   - Added ErrorBoundary wrapper
   - Integrated React error logging
   - Enhanced error handling

2. **FileManagerPage** (`webapp/src/pages/Documents/FileManagerPage.tsx`)
   - Added ErrorBoundary wrapper
   - Integrated React error logging

### Application-Level Integration

1. **App.tsx** - Added React 19 compatibility checking initialization
2. **main.tsx** - Added development debugging utilities initialization

## Known React 19 Compatibility Issues

### Libraries Checked:
- **react-pdf**: May have compatibility issues with React 19
- **react-dnd**: Requires version 16.0.1+ for React 19
- **react-router-dom**: Requires version 6.22.0+ for React 19
- **antd**: Requires version 5.24.0+ for React 19

### Common Error Patterns:
- `Cannot read properties of undefined (reading 'current')` - Ref access issues
- `Element type is invalid` - Component import/export issues
- `ReactDOM.render is no longer supported` - Legacy API usage

## Usage Instructions

### For Development

1. **Error Boundaries are automatically active** - They will catch and display React errors
2. **Console logging is enhanced** - Check browser console for detailed error information
3. **Compatibility checking runs on startup** - Check console for compatibility warnings

### For Production

1. **Error Boundaries provide user-friendly error messages**
2. **Errors are automatically sent to Sentry**
3. **Users can retry failed operations**

### Debugging Steps

1. **Check Browser Console** - Look for enhanced error messages and suggestions
2. **Review Compatibility Warnings** - Address any library compatibility issues
3. **Use Error Boundary Information** - Check component stack traces
4. **Decode Minified Errors** - Use the provided decoder utilities

## Common Solutions

### For Minified React Errors:
1. Run the application in development mode to see full error messages
2. Use the React Error Decoder utility
3. Check for recent code changes that might have introduced the error

### For React 19 Issues:
1. Update incompatible libraries to latest versions
2. Check ref usage patterns
3. Verify component import/export statements
4. Review async operations in useEffect hooks

### For Component Errors:
1. Check prop types and required props
2. Verify state management patterns
3. Review component lifecycle usage
4. Check for circular dependencies

## Monitoring and Maintenance

### Regular Checks:
1. Monitor browser console for new error patterns
2. Review Sentry error reports
3. Update library versions as needed
4. Test error boundary functionality

### Performance Monitoring:
1. Use the performance monitoring utilities in development
2. Check for memory leaks
3. Monitor component render times
4. Review Suspense usage

## Additional Resources

- [React Error Decoder](https://reactjs.org/docs/error-decoder.html)
- [React 19 Migration Guide](https://react.dev/blog/2024/04/25/react-19)
- [React Error Boundaries Documentation](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)

## Support

If you continue to experience issues:
1. Check the enhanced console logging for specific error details
2. Review the compatibility checker output
3. Use the error boundary information to identify problematic components
4. Consult the React 19 migration guide for breaking changes
