import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import Farm from './Farm.js';
import MigrationSystem from './MigrationSystem.js';

const MigrationJob = defineModel('MigrationJob', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  source_system: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: MigrationSystem,
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'failed'),
    defaultValue: 'pending'
  },
  entities: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: []
  },
  file_path: {
    type: DataTypes.STRING,
    allowNull: true
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'migration_jobs',
  timestamps: true,
  underscored: true
});

// Associations are defined in associations.js

export default MigrationJob;
