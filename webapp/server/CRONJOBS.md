# Cron Jobs for Data Aggregation

This document describes the cron jobs implemented for fetching and aggregating data from various APIs.

## Weather Data Aggregation

A cron job has been set up to fetch and store weather data for all farms and their fields. This job runs every 3 hours and stores the data in the `weather` table in the database.

### Schedule

The weather data aggregation job runs every 3 hours at minute 15 (e.g., 0:15, 3:15, 6:15, etc.).

### Implementation

The job is implemented in `server/index.js` and uses the `fetchAllFarmsWeather` function from `server/controllers/cronController.js`. This function:

1. Fetches weather data from the National Weather Service API for each farm
2. Stores the current weather, hourly forecast, and daily forecast in the database
3. Repeats the process for each field within the farm

### API Endpoint

The job can also be triggered manually via the `/api/cron/fetch-weather` endpoint. This endpoint is protected by the `CRON_SECRET_KEY` environment variable.

## Grants Data Aggregation

A cron job has been set up to fetch and store grants data from various sources. This job runs once a day and stores the data in the `grants` table in the database.

### Schedule

The grants data aggregation job runs once a day at 2:30 AM.

### Implementation

The job is implemented in `server/index.js` and uses the `fetchAndStoreGrants` function from `server/controllers/cronController.js`. This function:

1. Fetches grants data from grants.gov, farmers.gov, and USDA APIs
2. Stores the grants data in the database, updating existing grants if they already exist
3. Handles API failures gracefully by using mock data when necessary

### API Endpoint

The job can also be triggered manually via the `/api/cron/fetch-grants` endpoint. This endpoint is protected by the `CRON_SECRET_KEY` environment variable.

## Error Handling

Both jobs log any errors to the `script_executions` table in the database. This allows for monitoring and troubleshooting of the jobs.

## Environment Variables

The following environment variables are used by the cron jobs:

- `CRON_SECRET_KEY`: Secret key for protecting the cron job endpoints
- `GRANTS_GOV_API_KEY`: API key for grants.gov
- `GRANTS_GOV_API_URL`: API URL for grants.gov
- `FARMERS_GOV_API_KEY`: API key for farmers.gov
- `FARMERS_GOV_API_URL`: API URL for farmers.gov
- `USDA_ARMS_API_KEY`: API key for USDA ARMS
- `USDA_ARMS_API_URL`: API URL for USDA ARMS

## Manual Execution

To manually trigger the cron jobs, you can use the following curl commands:

```bash
# Fetch weather data
curl -X POST http://localhost:3001/api/cron/fetch-weather -H "x-cron-secret: your_cron_secret_key"

# Fetch grants data
curl -X POST http://localhost:3001/api/cron/fetch-grants -H "x-cron-secret: your_cron_secret_key"
```

Replace `your_cron_secret_key` with the value of the `CRON_SECRET_KEY` environment variable.

## Benefits

These cron jobs provide several benefits:

1. **Reduced API Calls**: By aggregating data at regular intervals, we reduce the number of API calls made to third-party services, which can help avoid rate limits and reduce costs.

2. **Improved Performance**: Since the data is stored locally in the database, the application can retrieve it quickly without waiting for external API responses, resulting in faster page loads.

3. **Data Analysis**: Having historical weather data and grants information stored in the database enables deeper analysis and reporting capabilities.

4. **Offline Access**: Users can access previously fetched data even if the external APIs are temporarily unavailable.

5. **Customized Data**: The aggregated data can be processed and transformed to better suit the specific needs of farmers using the application.