import express from 'express';
import {
  getCustomerNotifications,
  createCustomerNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification
} from '../controllers/customerNotificationController.js';
import { authenticateCustomer } from '../middleware/customerAuthMiddleware.js';

const router = express.Router();

// Get notifications for a customer
router.get('/', authenticateCustomer, getCustomerNotifications);

// Create a new notification (internal use only, not exposed to customers)
router.post('/', createCustomerNotification);

// Mark a notification as read
router.put('/:notificationId/read', authenticateCustomer, markNotificationAsRead);

// Mark all notifications as read
router.put('/read-all', authenticateCustomer, markAllNotificationsAsRead);

// Delete a notification
router.delete('/:notificationId', authenticateCustomer, deleteNotification);

export default router;