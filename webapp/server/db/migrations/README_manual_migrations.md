# Database Migrations Guide

## Important Change: Migrations Are Now Manual Only

As of the latest update, database migrations are no longer automatically executed when the application starts. Instead, all migrations must be applied manually through the Global Admin Migrations page.

## Why This Change?

This change was made to:

1. Give administrators more control over when and how migrations are applied
2. Prevent unexpected schema changes during application deployment
3. Allow for proper testing and verification of migrations before they are applied to production
4. Provide a clear audit trail of who applied which migrations and when

## How to Apply Migrations

### Step 1: Access the Global Admin Migrations Page

Navigate to the Global Admin section of the application and select the "Database Migrations" option.

### Step 2: Scan for New Migrations

Click the "Scan for New Migrations" button to detect any new migration files that have been added to the system.

### Step 3: Review Pending Migrations

Review the list of pending migrations. Each migration will show:
- Name
- Description
- Dependencies (if any)
- Status (pending, applied, skipped, failed)

### Step 4: Apply Migrations

For each migration you want to apply:
1. Click the "Apply" button next to the migration
2. Confirm the action in the dialog that appears
3. Wait for the migration to complete

Migrations should be applied in order. The system will prevent you from applying a migration if its dependencies or prior migrations haven't been applied yet.

### Step 5: Verify Migration Status

After applying migrations, verify that they have been successfully applied by checking their status in the migrations list.

## Skipping Migrations

If a migration is not applicable to your environment or you've manually applied the changes, you can skip a migration:

1. Click the "Skip" button next to the migration
2. Confirm the action in the dialog that appears

Skipped migrations can be retried later if needed.

## Troubleshooting

### Failed Migrations

If a migration fails:
1. Check the error message displayed in the migration list
2. Fix any issues in the database or migration file
3. Click "Retry" to attempt the migration again

### Missing Dependencies

If a migration shows missing dependencies:
1. Make sure all migration files are present in the server/db/migrations directory
2. Click "Scan for New Migrations" to detect any missing files
3. Apply the dependency migrations first

## Best Practices

1. Always back up your database before applying migrations
2. Apply migrations during low-traffic periods
3. Test migrations in a staging environment before applying them to production
4. Apply migrations in small batches rather than all at once
5. Document any manual changes made to the database outside of the migration system