import YieldPrediction from '../models/YieldPrediction.js';
import Crop from '../models/Crop.js';
import Field from '../models/Field.js';
import Farm from '../models/Farm.js';
import { Op } from 'sequelize';
import axios from 'axios';
import { sequelize } from '../config/database.js';

// Get all yield predictions for a farm
export const getYieldPredictions = async (req, res) => {
  try {
    const { farmId, cropId, fieldId, year } = req.query;

    if (!farmId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID is required' 
      });
    }

    // Build where clause for filtering
    const whereClause = { farm_id: farmId };

    if (cropId) whereClause.crop_id = cropId;
    if (fieldId) whereClause.field_id = fieldId;
    if (year) whereClause.harvest_year = year;

    // Get all yield predictions with filtering
    const predictions = await YieldPrediction.findAll({
      where: whereClause,
      include: [
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        },
        {
          model: Field,
          attributes: ['id', 'name']
        }
      ],
      order: [['prediction_date', 'DESC']]
    });

    return res.status(200).json({ 
      success: true, 
      predictions: predictions.map(prediction => ({
        id: prediction.id,
        crop_id: prediction.crop_id,
        crop_name: prediction.Crop ? prediction.Crop.name : 'Unknown Crop',
        crop_type: prediction.Crop ? prediction.Crop.variety : 'Unknown',
        field_id: prediction.field_id,
        field_name: prediction.Field ? prediction.Field.name : 'Unknown Field',
        predicted_yield: parseFloat(prediction.predicted_yield),
        yield_unit: prediction.yield_unit,
        confidence_level: parseFloat(prediction.confidence_level),
        factors: prediction.factors || [],
        prediction_date: prediction.prediction_date,
        harvest_year: prediction.harvest_year,
        created_at: prediction.created_at
      }))
    });
  } catch (error) {
    console.error('Error getting yield predictions:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get yield predictions', 
      error: error.message 
    });
  }
};

// Get a single yield prediction by ID
export const getYieldPredictionById = async (req, res) => {
  try {
    const { predictionId } = req.params;

    const prediction = await YieldPrediction.findByPk(predictionId, {
      include: [
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        },
        {
          model: Field,
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    if (!prediction) {
      return res.status(404).json({ 
        success: false, 
        message: 'Yield prediction not found' 
      });
    }

    return res.status(200).json({ 
      success: true, 
      prediction: {
        id: prediction.id,
        farm_id: prediction.farm_id,
        farm_name: prediction.Farm ? prediction.Farm.name : 'Unknown Farm',
        crop_id: prediction.crop_id,
        crop_name: prediction.Crop ? prediction.Crop.name : 'Unknown Crop',
        crop_type: prediction.Crop ? prediction.Crop.variety : 'Unknown',
        field_id: prediction.field_id,
        field_name: prediction.Field ? prediction.Field.name : 'Unknown Field',
        predicted_yield: parseFloat(prediction.predicted_yield),
        yield_unit: prediction.yield_unit,
        confidence_level: parseFloat(prediction.confidence_level),
        factors: prediction.factors || [],
        prediction_date: prediction.prediction_date,
        harvest_year: prediction.harvest_year,
        created_at: prediction.created_at,
        updated_at: prediction.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting yield prediction by ID:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get yield prediction', 
      error: error.message 
    });
  }
};

// Create a new yield prediction
export const createYieldPrediction = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      cropId, 
      fieldId, 
      predictedYield, 
      yieldUnit, 
      confidenceLevel, 
      factors, 
      harvestYear 
    } = req.body;

    // Validate required fields
    if (!farmId || !cropId || !predictedYield || !yieldUnit || !harvestYear) {
      await transaction.rollback();
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID, crop ID, predicted yield, yield unit, and harvest year are required' 
      });
    }

    // Validate confidence level
    if (confidenceLevel !== undefined && (confidenceLevel < 0 || confidenceLevel > 1)) {
      await transaction.rollback();
      return res.status(400).json({ 
        success: false, 
        message: 'Confidence level must be between 0 and 1' 
      });
    }

    // Create yield prediction
    const prediction = await YieldPrediction.create({
      farm_id: farmId,
      crop_id: cropId,
      field_id: fieldId || null,
      predicted_yield: predictedYield,
      yield_unit: yieldUnit,
      confidence_level: confidenceLevel || 0.7, // Default confidence level
      factors: factors || [],
      prediction_date: new Date(),
      harvest_year: harvestYear
    }, { transaction });

    await transaction.commit();

    // Fetch the created prediction with associations
    const createdPrediction = await YieldPrediction.findByPk(prediction.id, {
      include: [
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        },
        {
          model: Field,
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(201).json({ 
      success: true, 
      message: 'Yield prediction created successfully',
      prediction: {
        id: createdPrediction.id,
        crop_id: createdPrediction.crop_id,
        crop_name: createdPrediction.Crop ? createdPrediction.Crop.name : 'Unknown Crop',
        crop_type: createdPrediction.Crop ? createdPrediction.Crop.variety : 'Unknown',
        field_id: createdPrediction.field_id,
        field_name: createdPrediction.Field ? createdPrediction.Field.name : 'Unknown Field',
        predicted_yield: parseFloat(createdPrediction.predicted_yield),
        yield_unit: createdPrediction.yield_unit,
        confidence_level: parseFloat(createdPrediction.confidence_level),
        factors: createdPrediction.factors || [],
        prediction_date: createdPrediction.prediction_date,
        harvest_year: createdPrediction.harvest_year,
        created_at: createdPrediction.created_at
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating yield prediction:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to create yield prediction', 
      error: error.message 
    });
  }
};

// Generate a yield prediction using AI
export const generateYieldPrediction = async (req, res) => {
  try {
    const { farmId, cropId } = req.body;

    if (!farmId || !cropId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID and crop ID are required' 
      });
    }

    // Get crop and farm data
    const crop = await Crop.findByPk(cropId, {
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit', 'location_data']
        }
      ]
    });

    if (!crop) {
      return res.status(404).json({ 
        success: false, 
        message: 'Crop not found' 
      });
    }

    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      return res.status(404).json({ 
        success: false, 
        message: 'Farm not found' 
      });
    }

    // Get field data if available
    const field = crop.Fields && crop.Fields.length > 0 ? crop.Fields[0] : null;

    // Define yield ranges for different crop types
    const yieldRanges = {
      'corn': [150, 220, 'bushels/acre'],
      'soybeans': [45, 65, 'bushels/acre'],
      'wheat': [55, 75, 'bushels/acre'],
      'alfalfa': [4, 8, 'tons/acre'],
      'cotton': [800, 1200, 'lbs/acre'],
      'default': [50, 150, 'bushels/acre']
    };

    // Get yield range for the crop type
    const cropType = crop.variety ? crop.variety.toLowerCase() : 'default';
    const [min, max, unit] = yieldRanges[cropType] || yieldRanges['default'];

    // Generate a random yield within the range
    // In a real implementation, this would use AI models, weather data, soil data, etc.
    const predictedYield = Math.floor(Math.random() * (max - min + 1)) + min;

    // Generate a random confidence level between 0.7 and 0.95
    const confidenceLevel = Math.round((Math.random() * 0.25 + 0.7) * 100) / 100;

    // Define potential factors that influence yield
    const potentialFactors = [
      'Current weather patterns',
      'Historical yield data',
      'Soil conditions',
      'Applied fertilizers',
      'Irrigation schedule',
      'Pest management practices',
      'Planting density',
      'Seed variety',
      'Tillage practices',
      'Crop rotation history'
    ];

    // Randomly select 3-5 factors
    const numFactors = Math.floor(Math.random() * 3) + 3; // 3 to 5 factors
    const selectedFactors = potentialFactors
      .sort(() => 0.5 - Math.random())
      .slice(0, numFactors);

    // Get current year for harvest year
    const currentYear = new Date().getFullYear();

    // Create the yield prediction
    const prediction = await YieldPrediction.create({
      farm_id: farmId,
      crop_id: cropId,
      field_id: field ? field.id : null,
      predicted_yield: predictedYield,
      yield_unit: unit,
      confidence_level: confidenceLevel,
      factors: selectedFactors,
      prediction_date: new Date(),
      harvest_year: currentYear
    });

    // Fetch the created prediction with associations
    const createdPrediction = await YieldPrediction.findByPk(prediction.id, {
      include: [
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        },
        {
          model: Field,
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(201).json({ 
      success: true, 
      message: 'Yield prediction generated successfully',
      prediction: {
        id: createdPrediction.id,
        crop_id: createdPrediction.crop_id,
        crop_name: createdPrediction.Crop ? createdPrediction.Crop.name : 'Unknown Crop',
        crop_type: createdPrediction.Crop ? createdPrediction.Crop.variety : 'Unknown',
        field_id: createdPrediction.field_id,
        field_name: createdPrediction.Field ? createdPrediction.Field.name : 'Unknown Field',
        predicted_yield: parseFloat(createdPrediction.predicted_yield),
        yield_unit: createdPrediction.yield_unit,
        confidence_level: parseFloat(createdPrediction.confidence_level),
        factors: createdPrediction.factors || [],
        prediction_date: createdPrediction.prediction_date,
        harvest_year: createdPrediction.harvest_year,
        created_at: createdPrediction.created_at
      }
    });
  } catch (error) {
    console.error('Error generating yield prediction:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to generate yield prediction', 
      error: error.message 
    });
  }
};

// Delete a yield prediction
export const deleteYieldPrediction = async (req, res) => {
  try {
    const { predictionId } = req.params;

    // Find the prediction
    const prediction = await YieldPrediction.findByPk(predictionId);

    if (!prediction) {
      return res.status(404).json({ 
        success: false, 
        message: 'Yield prediction not found' 
      });
    }

    // Delete the prediction
    await prediction.destroy();

    return res.status(200).json({ 
      success: true, 
      message: 'Yield prediction deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting yield prediction:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to delete yield prediction', 
      error: error.message 
    });
  }
};
