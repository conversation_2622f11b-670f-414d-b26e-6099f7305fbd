import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as Speech from 'expo-speech';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '@/store/AuthProvider';
import { useFarm } from '../../hooks/useFarm';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withTiming, Easing } from 'react-native-reanimated';

type VoiceCommandsScreenNavigationProp = StackNavigationProp<MainStackParamList, 'VoiceCommands'>;

type CommandHistory = {
  id: string;
  command: string;
  action: string;
  timestamp: string;
};

const VoiceCommandsScreen = () => {
  const navigation = useNavigation<VoiceCommandsScreenNavigationProp>();
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [commandHistory, setCommandHistory] = useState<CommandHistory[]>([
    { 
      id: '1',
      command: "Record soil sample", 
      action: "Added soil sample record for North Field", 
      timestamp: new Date(Date.now() - 86400000).toLocaleString() 
    },
    { 
      id: '2',
      command: "Start irrigation in Field 3", 
      action: "Started irrigation system in East Field", 
      timestamp: new Date(Date.now() - 172800000).toLocaleString() 
    },
    { 
      id: '3',
      command: "Log equipment maintenance", 
      action: "Created maintenance record for Tractor #2", 
      timestamp: new Date(Date.now() - 259200000).toLocaleString() 
    }
  ]);
  const [micPermission, setMicPermission] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);

  // Animation for the microphone button
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  useEffect(() => {
    const checkMicPermission = async () => {
      try {
        const { status } = await Audio.requestPermissionsAsync();
        setMicPermission(status === 'granted');
      } catch (error) {
        console.error('Error checking microphone permission:', error);
        setMicPermission(false);
      }
    };

    checkMicPermission();
  }, []);

  const startListeningAnimation = () => {
    scale.value = withRepeat(
      withTiming(1.2, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
      -1,
      true
    );
    opacity.value = withRepeat(
      withTiming(0.7, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
      -1,
      true
    );
  };

  const stopListeningAnimation = () => {
    scale.value = withTiming(1, { duration: 300 });
    opacity.value = withTiming(1, { duration: 300 });
  };

  const handleStartListening = () => {
    if (!micPermission) {
      Alert.alert(
        'Microphone Permission Required',
        'Please grant microphone permission to use voice commands.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsListening(true);
    setTranscript('Listening...');
    startListeningAnimation();

    // Simulate voice recognition after 3 seconds
    setTimeout(() => {
      const mockCommands = [
        "Check weather forecast",
        "Record crop observation",
        "Start field mapping",
        "Log equipment usage",
        "Create task reminder"
      ];

      const randomCommand = mockCommands[Math.floor(Math.random() * mockCommands.length)];
      setTranscript(randomCommand);

      // Simulate processing after 1 more second
      setTimeout(() => {
        setIsListening(false);
        stopListeningAnimation();

        // Add to command history
        const newAction = getActionForCommand(randomCommand);
        const newCommand = {
          id: Date.now().toString(),
          command: randomCommand,
          action: newAction,
          timestamp: new Date().toLocaleString()
        };

        setCommandHistory(prev => [newCommand, ...prev]);

        // Speak the response
        Speech.speak(newAction, {
          language: 'en',
          pitch: 1.0,
          rate: 0.9
        });
      }, 1000);
    }, 3000);
  };

  const getActionForCommand = (command: string): string => {
    switch (command) {
      case "Check weather forecast":
        return "Displaying 5-day weather forecast for your farm location";
      case "Record crop observation":
        return "Created new crop observation entry for current field";
      case "Start field mapping":
        return "Initiated field boundary mapping for current location";
      case "Log equipment usage":
        return "Recorded 2.5 hours of usage for Tractor #1";
      case "Create task reminder":
        return "Added task reminder for tomorrow at 9:00 AM";
      default:
        return "Command processed successfully";
    }
  };

  const clearHistory = () => {
    Alert.alert(
      'Clear Command History',
      'Are you sure you want to clear your command history?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear', 
          style: 'destructive', 
          onPress: () => setCommandHistory([]) 
        }
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text style={styles.loadingText}>Loading...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <Text style={styles.title}>Voice Commands</Text>
        <Text style={styles.subtitle}>
          Use voice commands for hands-free operation in the field. Simply speak commands to perform actions.
        </Text>
      </View>

      {!micPermission && (
        <View style={styles.permissionWarning}>
          <Ionicons name="warning" size={24} color="#f59e0b" />
          <Text style={styles.permissionText}>
            Microphone permission is required for voice commands. Please enable microphone access in your device settings.
          </Text>
        </View>
      )}

      <View style={styles.voiceSection}>
        <Animated.View style={[styles.micButtonContainer, animatedStyles]}>
          <TouchableOpacity
            style={[
              styles.micButton,
              isListening ? styles.micButtonActive : null
            ]}
            onPress={handleStartListening}
            disabled={isListening}
          >
            <Ionicons 
              name={isListening ? "mic" : "mic-outline"} 
              size={40} 
              color={isListening ? "#ffffff" : "#4f46e5"} 
            />
          </TouchableOpacity>
        </Animated.View>

        <Text style={styles.listeningStatus}>
          {isListening ? 'Listening...' : 'Tap microphone to speak'}
        </Text>

        {transcript && transcript !== 'Listening...' && (
          <View style={styles.transcriptContainer}>
            <Text style={styles.transcriptLabel}>Recognized command:</Text>
            <Text style={styles.transcriptText}>"{transcript}"</Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Command History</Text>
          {commandHistory.length > 0 && (
            <TouchableOpacity onPress={clearHistory}>
              <Text style={styles.clearButton}>Clear</Text>
            </TouchableOpacity>
          )}
        </View>

        {commandHistory.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No command history yet. Try using voice commands above.</Text>
          </View>
        ) : (
          <FlatList
            data={commandHistory}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.historyItem}>
                <View style={styles.historyContent}>
                  <Text style={styles.commandText}>"{item.command}"</Text>
                  <Text style={styles.actionText}>{item.action}</Text>
                </View>
                <Text style={styles.timestampText}>{item.timestamp}</Text>
              </View>
            )}
            contentContainerStyle={styles.historyList}
          />
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Available Commands</Text>
        <Text style={styles.sectionSubtitle}>Try these voice commands</Text>

        <View style={styles.commandCategories}>
          <View style={styles.categoryCard}>
            <View style={styles.categoryHeader}>
              <Ionicons name="leaf-outline" size={20} color="#4f46e5" />
              <Text style={styles.categoryTitle}>Field Operations</Text>
            </View>
            <View style={styles.commandsList}>
              <Text style={styles.commandItem}>"Start field mapping"</Text>
              <Text style={styles.commandItem}>"Record soil sample"</Text>
              <Text style={styles.commandItem}>"Log crop observation"</Text>
            </View>
          </View>

          <View style={styles.categoryCard}>
            <View style={styles.categoryHeader}>
              <Ionicons name="construct-outline" size={20} color="#4f46e5" />
              <Text style={styles.categoryTitle}>Equipment</Text>
            </View>
            <View style={styles.commandsList}>
              <Text style={styles.commandItem}>"Start equipment diagnostics"</Text>
              <Text style={styles.commandItem}>"Log equipment usage"</Text>
              <Text style={styles.commandItem}>"Schedule maintenance"</Text>
            </View>
          </View>

          <View style={styles.categoryCard}>
            <View style={styles.categoryHeader}>
              <Ionicons name="list-outline" size={20} color="#4f46e5" />
              <Text style={styles.categoryTitle}>Tasks</Text>
            </View>
            <View style={styles.commandsList}>
              <Text style={styles.commandItem}>"Create task"</Text>
              <Text style={styles.commandItem}>"Complete task"</Text>
              <Text style={styles.commandItem}>"Assign task to [name]"</Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4b5563',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  permissionWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
  permissionText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#92400e',
  },
  voiceSection: {
    alignItems: 'center',
    padding: 24,
    marginTop: 8,
    marginHorizontal: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  micButtonContainer: {
    marginBottom: 16,
  },
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#eff6ff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#4f46e5',
  },
  micButtonActive: {
    backgroundColor: '#4f46e5',
  },
  listeningStatus: {
    fontSize: 16,
    color: '#4b5563',
    marginBottom: 16,
  },
  transcriptContainer: {
    width: '100%',
    padding: 12,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
  },
  transcriptLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  transcriptText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  section: {
    marginTop: 16,
    marginHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  clearButton: {
    fontSize: 14,
    color: '#ef4444',
    fontWeight: '500',
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
    marginBottom: 12,
  },
  emptyState: {
    padding: 24,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  historyList: {
    paddingBottom: 8,
  },
  historyItem: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  historyContent: {
    marginBottom: 8,
  },
  commandText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  actionText: {
    fontSize: 14,
    color: '#4b5563',
  },
  timestampText: {
    fontSize: 12,
    color: '#6b7280',
    alignSelf: 'flex-end',
  },
  commandCategories: {
    marginTop: 8,
  },
  categoryCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f9fafb',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 8,
  },
  commandsList: {
    padding: 12,
  },
  commandItem: {
    fontSize: 14,
    color: '#4b5563',
    paddingVertical: 6,
  },
});

export default VoiceCommandsScreen;
