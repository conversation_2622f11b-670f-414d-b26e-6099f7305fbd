import React, { useState, useEffect, useRef, Fragment } from 'react';
import { useNavigate } from 'react-router-dom';
import { Transition, Dialog } from '@headlessui/react';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useHelp } from '../context/HelpContext';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  url: string;
  category: string;
  type: 'guide' | 'page' | 'document';
  icon?: React.ReactNode;
}

const GlobalSearch: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  const { searchGuides, searchResults, loading } = useHelp();

  // Open search dialog with keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+K or Cmd+K to open search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Focus search input when dialog opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Handle search input changes
  useEffect(() => {
    if (query.trim()) {
      searchGuides(query);
    } else {
      setResults([]);
    }
  }, [query, searchGuides]);

  // Convert help guides to search results
  useEffect(() => {
    if (searchResults.length > 0) {
      const mappedResults = searchResults.map(guide => ({
        id: guide.id,
        title: guide.title,
        description: guide.content.replace(/<[^>]*>?/gm, '').substring(0, 100) + '...',
        url: `/help/guides/${guide.slug}`,
        category: guide.category + (guide.subcategory ? ` › ${guide.subcategory}` : ''),
        type: 'guide' as const,
        icon: (
          <div className="flex items-center justify-center h-6 w-6 rounded-md bg-primary-100 text-primary-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
        )
      }));

      setResults(mappedResults);
      setSelectedIndex(0);
    } else {
      setResults([]);
    }
  }, [searchResults]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : prev));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => (prev > 0 ? prev - 1 : 0));
    } else if (e.key === 'Enter' && results.length > 0) {
      e.preventDefault();
      handleResultClick(results[selectedIndex]);
    } else if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    setIsOpen(false);
    setQuery('');
    navigate(result.url);
  };

  return (
    <>
      {/* Search button */}
      <button
        type="button"
        className="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
        onClick={() => setIsOpen(true)}
        aria-label="Search"
      >
        <MagnifyingGlassIcon className="h-5 w-5" aria-hidden="true" />
      </button>

      {/* Search dialog */}
      <Transition show={isOpen} as={Fragment}>
        <Dialog
          as="div"
          className="fixed inset-0 z-50 overflow-y-auto"
          onClose={() => setIsOpen(false)}
        >
          <div className="min-h-screen px-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </Transition.Child>

            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <div className="inline-block w-full max-w-2xl my-16 text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
                <div className="relative">
                  <div className="flex items-center px-4 py-3 border-b border-gray-200">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 mr-3" aria-hidden="true" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      className="w-full focus:outline-none text-gray-900"
                      placeholder="Search for help, pages, and more..."
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      onKeyDown={handleKeyDown}
                    />
                    <button
                      type="button"
                      className="ml-2 text-gray-400 hover:text-gray-500"
                      onClick={() => setIsOpen(false)}
                    >
                      <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </div>

                  {/* Search results */}
                  <div className="max-h-96 overflow-y-auto p-2">
                    {loading && (
                      <div className="flex justify-center items-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
                      </div>
                    )}

                    {!loading && results.length === 0 && query.trim() !== '' && (
                      <div className="text-center py-8 text-gray-500">
                        No results found for "{query}"
                      </div>
                    )}

                    {!loading && results.length > 0 && (
                      <ul className="divide-y divide-gray-100">
                        {results.map((result, index) => (
                          <li key={result.id}>
                            <button
                              className={`w-full text-left px-4 py-3 flex items-start space-x-4 rounded-md ${
                                index === selectedIndex ? 'bg-gray-100' : 'hover:bg-gray-50'
                              }`}
                              onClick={() => handleResultClick(result)}
                            >
                              {result.icon}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {result.title}
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  {result.category}
                                </p>
                                <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                                  {result.description}
                                </p>
                              </div>
                            </button>
                          </li>
                        ))}
                      </ul>
                    )}

                    {/* Keyboard shortcuts help */}
                    {!loading && (
                      <div className="px-4 py-3 text-xs text-gray-500 border-t border-gray-100 mt-2">
                        <div className="flex justify-between">
                          <div className="flex items-center">
                            <span className="px-2 py-1 rounded bg-gray-100 mr-2">↑</span>
                            <span className="px-2 py-1 rounded bg-gray-100 mr-2">↓</span>
                            <span>to navigate</span>
                          </div>
                          <div className="flex items-center">
                            <span className="px-2 py-1 rounded bg-gray-100 mr-2">Enter</span>
                            <span>to select</span>
                          </div>
                          <div className="flex items-center">
                            <span className="px-2 py-1 rounded bg-gray-100 mr-2">Esc</span>
                            <span>to close</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default GlobalSearch;
