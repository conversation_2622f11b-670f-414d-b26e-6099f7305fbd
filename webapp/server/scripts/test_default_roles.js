import { sequelize } from '../config/database.js';

async function testDefaultRoles() {
  try {
    console.log('Testing default roles in the database...');
    
    // Check if default roles exist
    const [roleCount] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM role_permissions 
      WHERE farm_id IS NULL
    `);
    
    if (roleCount[0].count === 0) {
      console.error('No default roles found in the database!');
      process.exit(1);
    }
    
    console.log(`Found ${roleCount[0].count} default role permissions in the database.`);
    
    // Check each role type
    const roles = ['farm_owner', 'farm_admin', 'farm_manager', 'farm_employee', 'accountant'];
    
    for (const role of roles) {
      const [roleFeatures] = await sequelize.query(`
        SELECT feature, can_view, can_create, can_edit, can_delete
        FROM role_permissions
        WHERE farm_id IS NULL AND role_name = :role
        ORDER BY feature
      `, {
        replacements: { role }
      });
      
      if (roleFeatures.length === 0) {
        console.error(`No permissions found for role: ${role}`);
        continue;
      }
      
      console.log(`\nRole: ${role} (${roleFeatures.length} features)`);
      console.log('-------------------------------------------');
      
      for (const feature of roleFeatures) {
        console.log(`Feature: ${feature.feature}`);
        console.log(`  - View: ${feature.can_view ? 'Yes' : 'No'}`);
        console.log(`  - Create: ${feature.can_create ? 'Yes' : 'No'}`);
        console.log(`  - Edit: ${feature.can_edit ? 'Yes' : 'No'}`);
        console.log(`  - Delete: ${feature.can_delete ? 'Yes' : 'No'}`);
      }
    }
    
    console.log('\nDefault roles test completed successfully.');
    return true;
  } catch (error) {
    console.error('Error testing default roles:', error);
    return false;
  }
}

// Run the function
testDefaultRoles()
  .then(success => {
    if (success) {
      console.log('All tests passed!');
      process.exit(0);
    } else {
      console.error('Tests failed!');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });