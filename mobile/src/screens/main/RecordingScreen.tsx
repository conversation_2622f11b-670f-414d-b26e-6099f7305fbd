import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  Switch,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { RecordingPoint, RecordingStatus, RecordingStats, GpsDeviceStatus } from '@/types/gps';
import gpsRecordingService from '@/services/gpsRecordingService';
import bleGpsService from '@/services/bleGpsService';

type RecordingScreenProps = NativeStackScreenProps<MainStackParamList, 'Recording'>;

type Field = {
  id: string;
  name: string;
};

const RecordingScreen: React.FC<RecordingScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const mapRef = useRef<MapView>(null);
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingPath, setRecordingPath] = useState<RecordingPoint[]>([]);
  const [implementWidth, setImplementWidth] = useState('10'); // in meters
  const [recordingName, setRecordingName] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [fields, setFields] = useState<Field[]>([]);
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
  const [useExternalGPS, setUseExternalGPS] = useState(false);
  const [isConnectingGPS, setIsConnectingGPS] = useState(false);
  const [externalGpsStatus, setExternalGpsStatus] = useState<GpsDeviceStatus>({ connected: false });
  const [recordingStats, setRecordingStats] = useState({
    duration: 0,
    distance: 0,
    area: 0,
    avgSpeed: 0,
  });
  const [recordingStartTime, setRecordingStartTime] = useState<number | null>(null);
  const [locationSubscription, setLocationSubscription] = useState<Location.LocationSubscription | null>(null);

  // Timer for updating recording duration
  const [elapsedTime, setElapsedTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Load mock fields
    const mockFields: Field[] = [
      { id: '1', name: 'North Field' },
      { id: '2', name: 'South Field' },
      { id: '3', name: 'West Field' },
      { id: '4', name: 'East Field' },
      { id: '5', name: 'Creek Bottom' },
    ];
    setFields(mockFields);

    // Request location permissions
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg('Permission to access location was denied');
        return;
      }

      try {
        let location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.BestForNavigation,
        });
        setLocation(location);
      } catch (error) {
        setErrorMsg('Error getting location');
        console.error(error);
      }
    })();

    // Clean up on unmount
    return () => {
      if (locationSubscription) {
        locationSubscription.remove();
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Clean up BLE GPS service
      if (useExternalGPS) {
        bleGpsService.disconnect();
      }
      bleGpsService.destroy();
    };
  }, []);

  // Start/stop timer for recording duration
  useEffect(() => {
    if (isRecording && !isPaused) {
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRecording, isPaused]);

  // Update stats when recording path changes
  useEffect(() => {
    if (recordingPath.length > 1) {
      // Calculate distance
      let totalDistance = 0;
      for (let i = 1; i < recordingPath.length; i++) {
        const prev = recordingPath[i - 1];
        const curr = recordingPath[i];
        totalDistance += calculateDistance(
          prev.latitude, prev.longitude,
          curr.latitude, curr.longitude
        );
      }

      // Calculate area (simplified)
      const widthInKm = parseFloat(implementWidth) / 1000;
      const areaInHectares = totalDistance * widthInKm;
      const areaInAcres = areaInHectares * 2.47105;

      // Calculate average speed
      const avgSpeed = recordingPath.reduce((sum, point) => sum + (point.speed || 0), 0) / recordingPath.length;

      setRecordingStats({
        duration: elapsedTime,
        distance: totalDistance,
        area: areaInAcres,
        avgSpeed: avgSpeed,
      });
    }
  }, [recordingPath, elapsedTime, implementWidth]);

  const startLocationTracking = async () => {
    try {
      const subscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 2000,
          distanceInterval: 5,
        },
        (newLocation) => {
          setLocation(newLocation);

          if (isRecording && !isPaused) {
            const newPoint: RecordingPoint = {
              latitude: newLocation.coords.latitude,
              longitude: newLocation.coords.longitude,
              timestamp: newLocation.timestamp,
              accuracy: newLocation.coords.accuracy,
              altitude: newLocation.coords.altitude,
              speed: newLocation.coords.speed,
              heading: newLocation.coords.heading,
            };

            setRecordingPath(prev => [...prev, newPoint]);
          }
        }
      );

      setLocationSubscription(subscription);
    } catch (error) {
      console.error('Error starting location tracking:', error);
      Alert.alert('Error', 'Failed to start location tracking');
    }
  };

  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    return distance;
  };

  const deg2rad = (deg: number) => {
    return deg * (Math.PI / 180);
  };

  const handleStartRecording = () => {
    if (!location) {
      Alert.alert('Error', 'Cannot start recording without location data');
      return;
    }

    setIsRecording(true);
    setIsPaused(false);
    setRecordingStartTime(Date.now());
    setElapsedTime(0);
    setRecordingPath([
      {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: location.timestamp,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        speed: location.coords.speed,
        heading: location.coords.heading,
      },
    ]);

    startLocationTracking();
  };

  const handlePauseRecording = () => {
    setIsPaused(true);
  };

  const handleResumeRecording = () => {
    setIsPaused(false);
  };

  const handleStopRecording = () => {
    if (recordingPath.length <= 1) {
      Alert.alert(
        'Discard Recording',
        'Not enough data was recorded. Do you want to discard this recording?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Discard',
            onPress: () => {
              setIsRecording(false);
              setIsPaused(false);
              setRecordingPath([]);
              setElapsedTime(0);
            },
          },
        ]
      );
      return;
    }

    setIsModalVisible(true);
  };

  const handleSaveRecording = async () => {
    if (!recordingName.trim()) {
      Alert.alert('Error', 'Please enter a name for this recording');
      return;
    }

    setIsSaving(true);

    try {
      // Prepare the recording data
      const saveParams = {
        name: recordingName.trim(),
        fieldId: selectedFieldId || undefined,
        implementWidth: parseFloat(implementWidth),
        recordingPath,
        startTime: recordingStartTime || Date.now() - elapsedTime * 1000,
        endTime: Date.now(),
        stats: {
          duration: elapsedTime,
          distance: recordingStats.distance,
          area: recordingStats.area,
          avgSpeed: recordingStats.avgSpeed,
        }
      };

      // Save the recording to the server
      const savedRecording = await gpsRecordingService.saveRecording(saveParams);

      setIsSaving(false);
      setIsModalVisible(false);
      setIsRecording(false);
      setIsPaused(false);

      Alert.alert(
        'Success',
        'Recording saved successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      setIsSaving(false);
      console.error('Error saving recording:', error);
      Alert.alert(
        'Error',
        'Failed to save recording. Please try again.',
        [
          {
            text: 'OK',
          },
        ]
      );
    }
  };

  const handleConnectExternalGPS = async () => {
    setIsConnectingGPS(true);

    try {
      // Set up status change callback
      bleGpsService.onStatusChange((status) => {
        setExternalGpsStatus(status);

        if (status.connected) {
          setUseExternalGPS(true);
          setIsConnectingGPS(false);
          Alert.alert('Success', `Connected to ${status.deviceName || 'external GPS device'}`);
        } else if (isConnectingGPS) {
          // Only show disconnection alert if we were trying to connect
          setIsConnectingGPS(false);
          setUseExternalGPS(false);
          Alert.alert('Error', 'Failed to connect to GPS device');
        }
      });

      // Set up location update callback
      bleGpsService.onLocationUpdate((latitude, longitude, accuracy) => {
        if (isRecording && !isPaused && useExternalGPS) {
          const newPoint: RecordingPoint = {
            latitude,
            longitude,
            timestamp: Date.now(),
            accuracy,
            // Note: altitude, speed, and heading might not be available from all external GPS devices
          };

          setRecordingPath(prev => [...prev, newPoint]);
        }
      });

      // Start scanning for GPS devices
      await bleGpsService.startScan();
    } catch (error) {
      console.error('Error connecting to external GPS:', error);
      setIsConnectingGPS(false);
      Alert.alert('Error', 'Failed to connect to GPS device');
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const renderRecordingControls = () => {
    if (!isRecording) {
      return (
        <TouchableOpacity
          style={styles.startButton}
          onPress={handleStartRecording}
        >
          <Ionicons name="radio-button-on" size={24} color="#fff" />
          <Text style={styles.buttonText}>Start Recording</Text>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.recordingControls}>
        {isPaused ? (
          <TouchableOpacity
            style={[styles.controlButton, styles.resumeButton]}
            onPress={handleResumeRecording}
          >
            <Ionicons name="play" size={24} color="#fff" />
            <Text style={styles.buttonText}>Resume</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.controlButton, styles.pauseButton]}
            onPress={handlePauseRecording}
          >
            <Ionicons name="pause" size={24} color="#fff" />
            <Text style={styles.buttonText}>Pause</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, styles.stopButton]}
          onPress={handleStopRecording}
        >
          <Ionicons name="stop" size={24} color="#fff" />
          <Text style={styles.buttonText}>Stop</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {location ? (
        <MapView
          ref={mapRef}
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          initialRegion={{
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
          showsUserLocation
          followsUserLocation
        >
          {/* Recording path */}
          {recordingPath.length > 1 && (
            <Polyline
              coordinates={recordingPath}
              strokeColor="#e11d48"
              strokeWidth={4}
            />
          )}
        </MapView>
      ) : (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#22c55e" />
          <Text style={styles.loadingText}>
            {errorMsg || 'Getting your location...'}
          </Text>
        </View>
      )}

      {/* Recording status overlay */}
      {isRecording && (
        <View style={styles.statusOverlay}>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Status:</Text>
            <View style={styles.statusIndicatorContainer}>
              <View style={[
                styles.statusIndicator,
                { backgroundColor: isPaused ? '#f59e0b' : '#e11d48' }
              ]} />
              <Text style={styles.statusText}>
                {isPaused ? 'PAUSED' : 'RECORDING'}
              </Text>
            </View>
          </View>

          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Duration:</Text>
            <Text style={styles.statusValue}>{formatDuration(elapsedTime)}</Text>
          </View>

          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Distance:</Text>
            <Text style={styles.statusValue}>
              {recordingStats.distance.toFixed(2)} km
            </Text>
          </View>

          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Area covered:</Text>
            <Text style={styles.statusValue}>
              {recordingStats.area.toFixed(2)} acres
            </Text>
          </View>

          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Speed:</Text>
            <Text style={styles.statusValue}>
              {location && location.coords.speed 
                ? (location.coords.speed * 3.6).toFixed(1) 
                : '0'} km/h
            </Text>
          </View>
        </View>
      )}

      {/* Settings panel */}
      <View style={styles.settingsPanel}>
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Implement width:</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.widthInput}
              value={implementWidth}
              onChangeText={setImplementWidth}
              keyboardType="numeric"
              editable={!isRecording}
            />
            <Text style={styles.unitText}>meters</Text>
          </View>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>External GPS:</Text>
          <View style={styles.switchContainer}>
            <Switch
              value={useExternalGPS}
              onValueChange={(value) => {
                if (value && !useExternalGPS) {
                  // Connect to external GPS
                  handleConnectExternalGPS();
                } else if (!value && useExternalGPS) {
                  // Disconnect from external GPS
                  bleGpsService.disconnect();
                  setUseExternalGPS(false);
                  setExternalGpsStatus({ connected: false });
                }
              }}
              disabled={isRecording || isConnectingGPS}
              trackColor={{ false: '#d1d5db', true: '#86efac' }}
              thumbColor={useExternalGPS ? '#22c55e' : '#f4f4f5'}
            />
            {isConnectingGPS && (
              <ActivityIndicator size="small" color="#22c55e" style={styles.connectingIndicator} />
            )}
          </View>
        </View>
      </View>

      {/* Recording controls */}
      <View style={styles.controlsContainer}>
        {renderRecordingControls()}
      </View>

      {/* Save recording modal */}
      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Save Recording</Text>

            <Text style={styles.inputLabel}>Recording Name</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter a name for this recording"
              value={recordingName}
              onChangeText={setRecordingName}
              autoFocus
            />

            <Text style={styles.inputLabel}>Field (Optional)</Text>
            <ScrollView style={styles.fieldSelector}>
              {fields.map(field => (
                <TouchableOpacity
                  key={field.id}
                  style={[
                    styles.fieldOption,
                    selectedFieldId === field.id && styles.selectedFieldOption
                  ]}
                  onPress={() => setSelectedFieldId(field.id)}
                >
                  <Text style={[
                    styles.fieldOptionText,
                    selectedFieldId === field.id && styles.selectedFieldOptionText
                  ]}>
                    {field.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View style={styles.recordingStats}>
              <Text style={styles.statsTitle}>Recording Summary</Text>
              <View style={styles.statsRow}>
                <Text style={styles.statsLabel}>Duration:</Text>
                <Text style={styles.statsValue}>{formatDuration(elapsedTime)}</Text>
              </View>
              <View style={styles.statsRow}>
                <Text style={styles.statsLabel}>Distance:</Text>
                <Text style={styles.statsValue}>{recordingStats.distance.toFixed(2)} km</Text>
              </View>
              <View style={styles.statsRow}>
                <Text style={styles.statsLabel}>Area covered:</Text>
                <Text style={styles.statsValue}>{recordingStats.area.toFixed(2)} acres</Text>
              </View>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setIsModalVisible(false)}
                disabled={isSaving}
              >
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleSaveRecording}
                disabled={isSaving}
              >
                {isSaving ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.modalButtonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  statusOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  statusIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  settingsPanel: {
    position: 'absolute',
    bottom: 150,
    left: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingLabel: {
    fontSize: 14,
    color: '#666',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  widthInput: {
    width: 60,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
    textAlign: 'center',
  },
  unitText: {
    marginLeft: 5,
    fontSize: 14,
    color: '#666',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectingIndicator: {
    marginLeft: 10,
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#22c55e',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  recordingControls: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 30,
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  pauseButton: {
    backgroundColor: '#f59e0b',
  },
  resumeButton: {
    backgroundColor: '#22c55e',
  },
  stopButton: {
    backgroundColor: '#ef4444',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 15,
  },
  fieldSelector: {
    maxHeight: 150,
    marginBottom: 15,
  },
  fieldOption: {
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedFieldOption: {
    borderColor: '#22c55e',
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
  },
  fieldOptionText: {
    fontSize: 14,
    color: '#333',
  },
  selectedFieldOptionText: {
    color: '#22c55e',
    fontWeight: '500',
  },
  recordingStats: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  statsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: '#666',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 14,
    color: '#666',
  },
  statsValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: '#f3f4f6',
  },
  saveButton: {
    backgroundColor: '#22c55e',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
});

export default RecordingScreen;
