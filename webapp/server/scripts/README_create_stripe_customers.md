# Create Stripe Customers for Existing Farms

This script creates Stripe customers for existing farms that don't have a Stripe customer ID yet.

## Background

When new farms are created, a Stripe customer is automatically created for them. However, farms that were created before this feature was implemented don't have a Stripe customer ID. This script identifies those farms and creates Stripe customers for them.

## Usage

```bash
# Run the script in production mode
node create_stripe_customers_for_existing_farms.js

# Run the script in dry-run mode (no changes to database or Stripe)
node create_stripe_customers_for_existing_farms.js --dry-run

# Process only the first N farms (useful for testing)
node create_stripe_customers_for_existing_farms.js --limit=10

# Combine options
node create_stripe_customers_for_existing_farms.js --dry-run --limit=5
```

## Options

- `--dry-run`: Run the script without making any changes to the database or Stripe
- `--limit=<n>`: Process only the first n farms (useful for testing)

## Testing

A test script is provided to verify that the main script works correctly:

```bash
node test_create_stripe_customers.js
```

This will run the main script in dry-run mode with a limit of 5 farms.

## What the Script Does

1. Queries the database for all farms that don't have a `stripe_customer_id`
2. For each farm, finds the farm owner
3. Creates a Stripe customer for the farm using the Stripe API
4. Updates the farm record with the new Stripe customer ID
5. Provides detailed logging and error handling

## Requirements

- Node.js
- Access to the database
- Stripe API credentials in the environment variables